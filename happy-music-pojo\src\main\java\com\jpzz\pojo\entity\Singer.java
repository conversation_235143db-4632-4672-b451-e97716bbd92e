package com.jpzz.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 歌手实体类
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_singer")
@Schema(description = "歌手实体")
public class Singer implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "singer_id", type = IdType.AUTO)
    @Schema(description = "歌手ID")
    private Long singerId;

    @Schema(description = "歌手名称")
    private String singerName;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "歌手类型(男歌手/女歌手/组合)")
    private String type;

    @Schema(description = "出生日期")
    private LocalDate birth;

    @Schema(description = "地区")
    private String location;

    @Schema(description = "个人简介")
    private String introduction;

    @Schema(description = "歌曲数量")
    private Integer songsCount;

    @Schema(description = "粉丝数量")
    private Long fansCount;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @TableLogic
    @Schema(description = "逻辑删除标记(0-未删除,1-已删除)", hidden = true)
    private Integer deleted;
}