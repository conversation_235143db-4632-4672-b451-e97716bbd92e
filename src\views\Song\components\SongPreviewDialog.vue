<template>
  <el-dialog
    v-model="visible"
    title="歌曲试听"
    width="500px"
    center
    @update:model-value="handleVisibleChange"
  >
    <div v-if="songData" class="song-preview">
      <div class="flex items-center gap-4 mb-4">
        <!-- 封面 -->
        <el-image
          :src="songData.coverUrl || defaultCover"
          class="w-16 h-16 rounded"
          fit="cover"
        >
          <template #error>
            <div class="w-16 h-16 bg-gray-100 rounded flex items-center justify-center">
              <el-icon class="text-gray-400"><Picture /></el-icon>
            </div>
          </template>
        </el-image>

        <!-- 基本信息 -->
        <div>
          <div class="text-lg font-bold">{{ songData.name }}</div>
          <div class="text-sm text-gray-600">{{ songData.artistName || '未知歌手' }}</div>
        </div>
      </div>

      <!-- 音频播放器 -->
      <div v-if="songData.audioUrl" class="mt-4">
        <audio controls class="w-full">
          <source :src="songData.audioUrl" type="audio/mpeg">
          您的浏览器不支持音频播放。
        </audio>
      </div>
      <div v-else class="text-center py-4 text-gray-500">
        该歌曲暂无音频文件
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Picture } from '@element-plus/icons-vue'
import type { SongInfo } from '@/types/song'

interface Props {
  modelValue: boolean
  songData?: SongInfo | null
  defaultCover?: string
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  defaultCover: '/favicon.ico'
})

const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleVisibleChange = (value: boolean) => {
  emit('update:modelValue', value)
}

const handleClose = () => {
  emit('update:modelValue', false)
}
</script>

<style scoped>
.song-preview {
  @apply text-sm;
}
</style>
