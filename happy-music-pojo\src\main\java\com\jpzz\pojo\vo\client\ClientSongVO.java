package com.jpzz.pojo.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户端歌曲VO
 * <AUTHOR>
 */
@Data
@Schema(description = "客户端歌曲信息")
public class ClientSongVO {

    @Schema(description = "歌曲ID")
    private Long id;

    @Schema(description = "歌手ID")
    private Long artistId;

    @Schema(description = "歌手名称")
    private String artistName;

    @Schema(description = "歌曲名称")
    private String name;

    @Schema(description = "专辑名称")
    private String album;

    @Schema(description = "歌词")
    private String lyric;

    @Schema(description = "时长")
    private String duration;

    @Schema(description = "风格")
    private String style;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "音频URL")
    private String audioUrl;

    @Schema(description = "发行时间")
    private String releaseTime;

    @Schema(description = "喜欢次数")
    private Long likeCount;

    @Schema(description = "播放次数")
    private Long playCount;

    @Schema(description = "用户是否喜欢")
    private Boolean isLiked;

    @Schema(description = "用户是否收藏")
    private Boolean isCollected;

    @Schema(description = "推荐分数")
    private Double recommendScore;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "状态")
    private String status;
}
