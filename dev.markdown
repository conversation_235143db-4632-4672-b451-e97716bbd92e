如果您后续需要连接真实的后端API，只需将USE_MOCK设置为false，并确保后端API接口与前端预期的格式一致即可。

# 代码风格指南

## 函数定义

- 优先使用箭头函数定义方法和回调
- 对于组件方法，使用 `const methodName = () => { ... }` 形式

## 导入顺序

导入应按以下顺序分组，每组之间留一个空行：

1. Vue 相关导入 (vue, vue-router, pinia)
2. 第三方库导入
3. 类型导入 (使用 type 关键字)
4. 项目内部导入 (按路径长度排序)
5. 样式导入

示例：

```typescript
// Vue 相关
import { defineComponent, ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'

// 第三方库
import { ElMessage } from 'element-plus'
import axios from 'axios'

// 类型
import type { UserInfo } from '@/types/user'

// 内部导入
import { useUserStore } from '@/stores/user'
import request from '@/utils/request'

// 样式
import '@/styles/index.scss'
```

## 命名约定

- 组件名：PascalCase (如 UserDialog)
- 方法名：camelCase，动词开头 (如 handleSubmit, getUserInfo)
- 变量名：camelCase (如 userList, isLoading)
- 常量：全大写下划线分隔 (如 API_BASE_URL)

## 异步处理

- 优先使用 async/await 而不是 Promise.then()
- 始终使用 try/catch 处理异步错误

## 后端开发优化

- 架构设计
  采用分层架构：Controller → Service → Repository，保持职责分明
  使用DTO模式分离业务逻辑和数据传输对象
  实现统一响应结构和全局异常处理
  考虑引入领域驱动设计(DDD)思想组织复杂业务逻辑
  安全增强
  除JWT外，添加刷新token机制
  实现RBAC权限模型，细化权限控制
  接口添加防重放攻击设计(nonce+timestamp)
  敏感信息加密存储，使用Spring Security Crypto
  对外接口添加限流保护（可使用Sentinel或Resilience4j）
  性能优化
  Redis合理使用不同数据结构存储不同类型数据
  大文件上传使用分片上传，可配合MinIO policies
  MyBatis-Plus合理设计索引，避免N+1查询问题
  考虑引入本地缓存Caffeine与Redis形成多级缓存
  开发效率
  使用MapStruct简化对象映射
  集成Knife4j美化API文档
  添加CI/CD流程，如GitHub Actions
  使用Docker Compose简化开发环境部署
  可观测性
  集成ELK或Loki进行日志收集
  添加Micrometer + Prometheus + Grafana监控链路
  使用Skywalking做分布式追踪
  未来拓展
  预留消息队列接口（如RocketMQ/Kafka）
  考虑模块化设计，为未来微服务拆分做准备
  接口版本控制，如/api/v1/命名方式
  这些建议可以根据项目实际规模和需求灵活调整，不必一次性全部实现。
