package com.jpzz.service.common.Impl;

import com.jpzz.constant.CacheConstant;
import com.jpzz.service.common.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CacheServiceImpl implements CacheService {

    @Override
    @CachePut(value = CacheConstant.JWT_TOKEN_CACHE, key = "#userId")
    public String cacheUserToken(Long userId, String token) {
        log.info("缓存用户id:{}", userId);
        log.info("缓存用户token:{}", token);
        return token;
    }

    @Override
    @Cacheable(value = CacheConstant.JWT_TOKEN_CACHE, key = "#userId")
    public String getUserToken(Long userId) {
        log.info("缓存中不存在用户token，用户ID:{}", userId);
        return null;
    }

    @Override
    @CacheEvict(value = CacheConstant.JWT_TOKEN_CACHE, key = "#userId")
    public void clearUserToken(Long userId) {
        log.info("清除用户token缓存，用户ID:{}", userId);

    }


}
