// src/utils/mockApi.ts
import { UserRoles } from '@/types/user'

// 模拟用户数据
const mockUsers = [
  {
    userId: 1,
    username: 'admin',
    password: 'admin123', // 真实项目中不会在前端保存密码
    role: UserRoles.ADMIN,
    avatar: '',
    phone: '13812345678',
    email: '<EMAIL>',
    status: '1',
    createTime: '2024-01-01 00:00:00'
  },
  {
    userId: 2,
    username: 'user',
    password: 'user123', // 真实项目中不会在前端保存密码
    role: UserRoles.USER,
    avatar: '',
    phone: '13987654321',
    email: '<EMAIL>',
    status: '1',
    createTime: '2024-01-02 00:00:00'
  }
]

// 模拟登录API
export function mockLogin(username: string, password: string) {
  return new Promise((resolve, reject) => {
    // 模拟请求延时
    setTimeout(() => {
      const user = mockUsers.find(u => u.username === username && u.password === password)
      if (user) {
        // 返回一个随机token
        const token = `mock_token_${Math.random().toString(36).substring(2)}`
        resolve({
          code: 200,
          message: '登录成功',
          data: { token }
        })
      } else {
        reject(new Error('用户名或密码不正确'))
      }
    }, 500)
  })
}

// 模拟获取用户信息API
export function mockGetUserInfo(token: string) {
  return new Promise((resolve, reject) => {
    // 模拟请求延时
    setTimeout(() => {
      // 验证token有效性
      if (!token || !token.startsWith('mock_token_')) {
        reject(new Error('无效的token'))
        return
      }
      // 在实际应用中，这里应该根据token从后端获取对应用户信息
      // 这里简单模拟，随机返回一个用户信息
      const userIndex = Math.floor(Math.random() * mockUsers.length)
      const userInfo = { ...mockUsers[userIndex] }
      // 不返回密码
      userInfo.password = ''
      resolve({
        code: 200,
        message: '获取用户信息成功',
        data: userInfo
      })
    }, 300)
  })
}

// 模拟登出API
export function mockLogout() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        message: '注销成功',
        data: null
      })
    }, 300)
  })
}
