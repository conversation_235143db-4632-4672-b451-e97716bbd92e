package com.jpzz.service.admin.Impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jpzz.constant.MessageConstant;
import com.jpzz.exception.PlaylistExistedException;
import com.jpzz.exception.PlaylistNotFoundException;
import com.jpzz.exception.SongNotFoundException;
import com.jpzz.mapper.admin.PlaylistMapper;
import com.jpzz.mapper.admin.PlaylistSongMapper;
import com.jpzz.pojo.dto.admin.PlaylistDTO;
import com.jpzz.pojo.dto.admin.PlaylistQueryDTO;
import com.jpzz.pojo.dto.admin.PlaylistSongDTO;
import com.jpzz.pojo.dto.admin.PlaylistSongQueryDTO;
import com.jpzz.pojo.entity.Playlist;
import com.jpzz.pojo.entity.PlaylistSong;
import com.jpzz.pojo.entity.Song;
import com.jpzz.pojo.vo.admin.PlaylistVO;
import com.jpzz.pojo.vo.admin.SongVO;
import com.jpzz.result.PageResult;
import com.jpzz.service.admin.PlaylistService;
import com.jpzz.service.admin.PlaylistSongService;
import com.jpzz.service.admin.SongService;
import com.jpzz.utils.DateTimeUtils;
import com.jpzz.utils.MinioUtils;
import com.jpzz.utils.PageUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.IntStream;

/**
 * 歌单服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PlaylistServiceImpl extends ServiceImpl<PlaylistMapper, Playlist> implements PlaylistService {


    @Resource
    private PlaylistSongService playlistSongService;

    @Resource
    private PlaylistSongMapper playlistSongMapper;
    @Resource
    private SongService songService;

    @Resource
    private MinioUtils minioUtils;

    @Override
    public PageResult<PlaylistVO> getPlaylistPage(PlaylistQueryDTO playlistQueryDTO) {
        log.info("分页查询歌单列表，查询条件：{}", playlistQueryDTO);

        LocalDateTime[] times = DateTimeUtils.parseTimeRange(playlistQueryDTO.getBeginTime(), playlistQueryDTO.getEndTime());
        LocalDateTime beginTime = times[0];
        LocalDateTime endTime = times[1];
        Page<Playlist> p = new Page<>(playlistQueryDTO.getPageNum(), playlistQueryDTO.getPageSize());

        Page<Playlist> playlistPage = this.lambdaQuery()
                .like(StrUtil.isNotBlank(playlistQueryDTO.getName()), Playlist::getName, playlistQueryDTO.getName())
                .eq(StrUtil.isNotBlank(playlistQueryDTO.getStyle()), Playlist::getStyle, playlistQueryDTO.getStyle())
                .ge(beginTime != null, Playlist::getCreateTime, beginTime)
                .le(endTime != null, Playlist::getCreateTime, endTime)
                .orderByDesc(Playlist::getCreateTime)
                .page(p);


        return PageUtils.convertToPageResult(
                playlistPage,
                this::convertToPlaylistVO,
                playlistQueryDTO.getPageNum(),
                playlistQueryDTO.getPageSize());
    }

    @Override
    public PlaylistVO getPlaylistById(Long playlistId) {
        log.info("查询歌单详情，歌单ID：{}", playlistId);

        Playlist playlist = this.getById(playlistId);
        if (ObjectUtil.isNull(playlist)) {
            throw new PlaylistNotFoundException(MessageConstant.PLAYLIST_NOT_FOUND);
        }

        return convertToPlaylistVO(playlist);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addPlaylist(PlaylistDTO playlistDTO) {
        log.info("添加歌单，歌单信息：{}", playlistDTO);

        // 检查歌单名称是否已存在
        Playlist existPlaylist = this.lambdaQuery()
                .eq(Playlist::getName, playlistDTO.getName())
                .one();
        if (ObjectUtil.isNotNull(existPlaylist)) {
            throw new PlaylistExistedException(MessageConstant.PLAYLIST_EXISTS);
        }

        Playlist playlist = BeanUtil.copyProperties(playlistDTO, Playlist.class);
        // 设置默认值
        if (ObjectUtil.isNull(playlist.getSongCount())) {
            playlist.setSongCount(0);
        }
        this.save(playlist);
        log.info("歌单添加成功，歌单ID：{}", playlist.getId());
        return playlist.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePlaylist(PlaylistDTO playlistDTO) {
        log.info("更新歌单，歌单信息：{}", playlistDTO);

        // 检查歌单是否存在
        Playlist existPlaylist = this.getById(playlistDTO.getId());
        if (ObjectUtil.isNull(existPlaylist)) {
            throw new PlaylistNotFoundException(MessageConstant.PLAYLIST_NOT_FOUND);
        }

        // 检查名称是否重复（排除自己）
        Playlist duplicatePlaylist = this.lambdaQuery()
                .eq(Playlist::getName, playlistDTO.getName())
                .ne(Playlist::getId, playlistDTO.getId())
                .one();
        if (ObjectUtil.isNotNull(duplicatePlaylist)) {
            throw new PlaylistExistedException(MessageConstant.PLAYLIST_EXISTS);
        }

        Playlist playlist = BeanUtil.copyProperties(playlistDTO, Playlist.class);
        this.updateById(playlist);
        log.info("歌单更新成功，歌单ID：{}", playlist.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePlaylistById(Long playlistId) {
        log.info("删除歌单，歌单ID：{}", playlistId);

        // 检查歌单是否存在
        Playlist playlist = this.getById(playlistId);
        if (ObjectUtil.isNull(playlist)) {
            throw new PlaylistNotFoundException(MessageConstant.PLAYLIST_NOT_FOUND);
        }

        // 删除歌单（逻辑删除）
        this.removeById(playlistId);

        // 删除歌单中的所有歌曲关联
        playlistSongService.remove(new LambdaQueryWrapper<PlaylistSong>()
                .eq(PlaylistSong::getPlaylistId, playlistId));


        log.info("歌单删除成功，歌单ID：{}", playlistId);
    }

    @Override
    public void deletePlaylistByIds(List<Long> playlistIds) {

        if (ObjectUtil.isEmpty(playlistIds)) {
            return;
        }

        // 检查歌单是否存在
        List<Playlist> playlists = this.listByIds(playlistIds);
        if (playlists.size() != playlistIds.size()) {
            throw new PlaylistNotFoundException(MessageConstant.PLAYLIST_NOT_FOUND);
        }

        // 批量删除歌单（逻辑删除）
        this.removeByIds(playlistIds);

        // 删除歌单中的所有歌曲关联
        playlistSongService.remove(new LambdaQueryWrapper<PlaylistSong>()
                .in(PlaylistSong::getPlaylistId, playlistIds));

        log.info("批量删除歌单成功，删除数量：{}", playlistIds.size());
    }

    @Override
    public PageResult<SongVO> getPlaylistSongs(PlaylistSongQueryDTO queryDTO) {
        log.info("获取歌单中的歌曲列表，查询条件：{}", queryDTO);

        // 检查歌单是否存在
        Playlist playlist = this.getById(queryDTO.getPlaylistId());
        if (ObjectUtil.isNull(playlist)) {
            throw new PlaylistNotFoundException(MessageConstant.PLAYLIST_NOT_FOUND);
        }

        // 分页查询歌单中的歌曲
        Page<SongVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<SongVO> songPage = playlistSongMapper.selectPlaylistSongs(page, queryDTO);

        return PageResult.<SongVO>builder()
                .total(songPage.getTotal())
                .list(songPage.getRecords())
                .pageNum(queryDTO.getPageNum())
                .pageSize(queryDTO.getPageSize())
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String uploadCover(MultipartFile file, Long playlistId) {
        log.info("上传歌单封面，歌单ID：{}", playlistId);

        // 检查歌单是否存在
        Playlist playlist = this.getById(playlistId);
        if (ObjectUtil.isNull(playlist)) {
            throw new PlaylistNotFoundException(MessageConstant.PLAYLIST_NOT_FOUND);
        }

        try {
            // 上传文件到MinIO
            String fileName = "playlist/cover/" + playlistId + "_" + System.currentTimeMillis() + "_" + file.getOriginalFilename();
            String coverUrl = minioUtils.uploadFile(file, fileName);

            // 更新歌单封面URL
            playlist.setCoverUrl(coverUrl);
            this.updateById(playlist);

            log.info("歌单封面上传成功，封面URL：{}", coverUrl);
            return coverUrl;
        } catch (Exception e) {
            log.error("歌单封面上传失败", e);
            throw new RuntimeException("封面上传失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSongsToPlaylist(Long playlistId, List<Long> songIds) {
        // 检查歌单是否存在
        Playlist playlist = this.getById(playlistId);
        if (ObjectUtil.isNull(playlist)) {
            throw new PlaylistNotFoundException(MessageConstant.PLAYLIST_NOT_FOUND);
        }
        // 检查歌曲是否存在
        List<Song> existingSongs = songService.lambdaQuery()
                .in(Song::getId, songIds)
                .list();
        if (existingSongs.size() != songIds.size()) {
            // 找出不存在的歌曲ID
            List<Long> existingSongIds = existingSongs.stream()
                    .map(Song::getId)
                    .toList();
            List<Long> notFoundSongIds = songIds.stream()
                    .filter(songId -> !existingSongIds.contains(songId))
                    .toList();
            log.error("部分歌曲不存在，歌曲ID：{}", notFoundSongIds);
            throw new SongNotFoundException("部分歌曲不存在：" + notFoundSongIds);
        }

        // 过滤已经存在的歌曲
        List<Long> existingSongIds = playlistSongService.lambdaQuery()
                .eq(PlaylistSong::getPlaylistId, playlist.getId())
                .in(PlaylistSong::getSongId, songIds)
                .list()
                .stream()
                .map(PlaylistSong::getSongId)
                .toList();


        List<Long> newSongIds = songIds.stream()
                .filter(songId -> !existingSongIds.contains(songId))
                .toList();
        if (ObjectUtil.isEmpty(newSongIds)) {
            log.info("歌单中已存在歌曲，无新增歌曲");
            return;
        }

        Integer maxSortOrder = playlistSongService.lambdaQuery()
                .eq(PlaylistSong::getPlaylistId, playlist.getId())
                .orderByDesc(PlaylistSong::getSortOrder)
                .last("limit 1")
                .list()
                .stream().findFirst().map(PlaylistSong::getSortOrder).orElse(0);


        // 批量插入歌曲
        List<PlaylistSong> playlistSongs = IntStream.range(0, songIds.size())
                .mapToObj(i -> PlaylistSong.builder()
                        .playlistId(playlistId)
                        .songId(songIds.get(i))
                        .sortOrder(maxSortOrder + i + 1)
                        .build()
                )
                .toList();
        playlistSongService.saveBatch(playlistSongs);

        // 更新歌单歌曲数量
        updatePlaylistSongCount(playlistId);

        log.info("歌曲添加到歌单成功，歌单ID：{}，添加歌曲数：{}",
                playlistId, songIds.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeSongsByPlaylist(PlaylistSongDTO playlistSongDTO) {

        // 检查歌单是否存在
        Playlist playlist = this.getById(playlistSongDTO.getPlaylistId());
        if (ObjectUtil.isNull(playlist)) {
            throw new PlaylistNotFoundException(MessageConstant.PLAYLIST_NOT_FOUND);
        }

        // 删除歌单中的歌曲
        playlistSongService.lambdaUpdate()
                .eq(PlaylistSong::getPlaylistId, playlistSongDTO.getPlaylistId())
                .in(PlaylistSong::getSongId, playlistSongDTO.getSongIds())
                .remove();

        // 更新歌单歌曲数量
        updatePlaylistSongCount(playlistSongDTO.getPlaylistId());

        log.info("歌曲从歌单中移除成功，歌单ID：{}，移除歌曲数：{}",
                playlistSongDTO.getPlaylistId(), playlistSongDTO.getSongIds().size());
    }

    /**
     * 更新歌单歌曲数量
     *
     * @param playlistId 歌单ID
     */
    private void updatePlaylistSongCount(Long playlistId) {

        long count = playlistSongService.lambdaQuery()
                .eq(PlaylistSong::getPlaylistId, playlistId)
                .count();

        Playlist playlist = new Playlist();
        playlist.setId(playlistId);
        playlist.setSongCount((int) count);
        this.updateById(playlist);
    }

    /**
     * 将Playlist实体转换为PlaylistVO
     *
     * @param playlist 歌单实体
     * @return 歌单VO
     */
    private PlaylistVO convertToPlaylistVO(Playlist playlist) {
        PlaylistVO playlistVO = new PlaylistVO();
        BeanUtil.copyProperties(playlist, playlistVO);
        return playlistVO;
    }
}
