package com.jpzz.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jpzz.result.PageResult;

import java.util.List;
import java.util.function.Function;

/**
 * 分页工具类
 * <AUTHOR>
 */
public class PageUtils {

    /**
     * 将MyBatis-Plus的Page对象转换为PageResult
     * @param page MyBatis-Plus分页对象
     * @param converter 实体转VO的转换函数
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     * @param <T> 实体类型
     * @param <V> VO类型
     * @return PageResult
     */
    public static <T, V> PageResult<V> convertToPageResult(Page<T> page, Function<T, V> converter, Integer pageNum, Integer pageSize) {
        List<V> voList = page.getRecords()
                .stream()
                .map(converter)
                .toList();

        return PageResult.<V>builder()
                .pageNum(pageNum)
                .pageSize(pageSize)
                .list(voList)
                .total(page.getTotal())
                .build();
    }
}