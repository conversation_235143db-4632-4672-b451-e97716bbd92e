import type { PlaylistInfo, PlaylistQueryParams } from "@/types/playlist";
import request from "@/utils/request";
import type { SongInfo } from "@/types/song";
export interface PageResult<T> {
    total: number;
    list: T[];
}

export interface PlaylistSongQueryParams {
    pageNum: number;
    pageSize: number;
    name?: string;
    artistName?: string;
    style?: string;
}

/**
 * 获取歌单列表（分页）
 * @param params 查询参数
 * @returns 歌单列表分页数据
 */
export function getPlaylistList(params: PlaylistQueryParams): Promise<PageResult<PlaylistInfo>> {
    return request.get('/playlist/list', params)
}

/**
 * 获取歌单详情
 * @param playlistId 歌单ID
 * @returns 歌单详情
 */
export function getPlaylistInfo(playlistId: number): Promise<PlaylistInfo> {
    return request.get(`/playlist/${playlistId}`)
}

/**
 * 添加歌单
 * @param data 歌单数据
 * @returns 操作结果
 */
export function addPlaylist(data: Partial<PlaylistInfo>): Promise<unknown> {
    return request.post('/playlist', data)
}

/**
 * 更新歌单信息
 * @param data 歌单数据
 * @returns 操作结果
 */
export function updatePlaylist(data: Partial<PlaylistInfo>): Promise<unknown> {
    return request.put('/playlist', data)
}

/**
 * 删除歌单
 * @param playlistId 歌单ID
 * @returns 操作结果
 */
export function deletePlaylist(playlistId: number): Promise<unknown> {
    return request.delete(`/playlist/${playlistId}`)
}

/**
 * 批量删除歌单
 * @param playlistIds 歌单ID数组
 * @returns 操作结果
 */
export function batchDeletePlaylists(playlistIds: number[]): Promise<unknown> {
    return request.post('/playlist/batch-delete', playlistIds)
}

/**
 * 上传歌单封面
 * @param playlistId 歌单ID
 * @param file 封面文件
 * @returns 上传结果
 */
export function uploadPlaylistCover(playlistId: number, file: File): Promise<{ url: string }> {
    const formData = new FormData()
    formData.append('file', file)
    return request.post(`/playlist/${playlistId}/cover`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
    })
}

/**
 * 获取歌单中的歌曲列表
 * @param playlistId 歌单ID
 * @param params 查询参数
 * @returns 歌曲列表
 */
export function getPlaylistSongs(playlistId: number, params: PlaylistSongQueryParams): Promise<PageResult<SongInfo>> {
    return request.get(`/playlist/${playlistId}/songs`, params)
}

/**
 * 向歌单添加歌曲
 * @param playlistId 歌单ID
 * @param songIds 歌曲ID数组
 * @returns 操作结果
 */
export function addSongsToPlaylist(playlistId: number, songIds: number[]): Promise<unknown> {
    return request.post(`/playlist/${playlistId}/songs`,  songIds )
}

/**
 * 从歌单移除歌曲
 * @param playlistId 歌单ID
 * @param songId 歌曲ID
 * @returns 操作结果
 */
export function removeSongFromPlaylist(playlistId: number, songId: number): Promise<unknown> {
    return request.delete(`/playlist/${playlistId}/songs/${songId}`)
}

/**
 * 批量从歌单移除歌曲
 * @param playlistId 歌单ID
 * @param songIds 歌曲ID数组
 * @returns 操作结果
 */
export function batchRemoveSongsFromPlaylist(playlistId: number, songIds: number[]): Promise<unknown> {
    return request.post(`/playlist/${playlistId}/songs/batch-delete`, { songIds })
}

