package com.jpzz.service.admin.Impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jpzz.constant.MessageConstant;
import com.jpzz.exception.SongExistedException;
import com.jpzz.exception.SongNotFoundException;
import com.jpzz.mapper.admin.SongMapper;
import com.jpzz.pojo.dto.admin.SongDTO;
import com.jpzz.pojo.dto.admin.SongQueryDTO;
import com.jpzz.pojo.entity.Song;
import com.jpzz.pojo.vo.admin.SongVO;
import com.jpzz.result.PageResult;
import com.jpzz.service.admin.SongService;
import com.jpzz.utils.MinioUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 歌曲服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SongServiceImpl extends ServiceImpl<SongMapper, Song> implements SongService {

    @Resource
    private SongMapper songMapper;

    @Resource
    private MinioUtils minioUtils;

    @Override
    public PageResult<SongVO> pageSongList(SongQueryDTO songQueryDTO) {
        log.info("分页查询歌曲列表，查询参数：{}", songQueryDTO);

        Page<SongVO> page = new Page<>(songQueryDTO.getPageNum(), songQueryDTO.getPageSize());
        Page<SongVO> songPage = songMapper.selectSongPage(page, songQueryDTO);

        return PageResult.<SongVO>builder()
                .total(songPage.getTotal())
                .list(songPage.getRecords())
                .pageNum(songQueryDTO.getPageNum())
                .pageSize(songQueryDTO.getPageSize())
                .build();
    }

    @Override
    public SongVO getSongById(Long songId) {
        log.info("根据ID查询歌曲详情，歌曲ID：{}", songId);

        SongVO songVO = songMapper.selectSongById(songId);
        if (ObjectUtil.isNull(songVO)) {
            throw new SongNotFoundException(MessageConstant.SONG_NOT_FOUND);
        }
        return songVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addSong(SongDTO songDTO) {
        log.info("添加歌曲，歌曲信息：{}", songDTO);

        Song existSong = this.lambdaQuery()
                .eq(Song::getArtistId, songDTO.getArtistId())
                .eq(StrUtil.isNotBlank(songDTO.getName()), Song::getName, songDTO.getName())
                .one();

        if (ObjectUtil.isNotNull(existSong)) {
            throw new SongExistedException(MessageConstant.SONG_EXISTS + ":" + existSong.getName());
        }

        Song song = BeanUtil.copyProperties(songDTO, Song.class);
        // 设置默认值
        if (ObjectUtil.isNull(song.getLikeCount())) {
            song.setLikeCount(0);
        }

        this.save(song);
        log.info("歌曲添加成功，歌曲ID：{}", song.getId());
        return song.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSong(SongDTO songDTO) {
        log.info("更新歌曲，歌曲信息：{}", songDTO);

        // 检查歌曲是否存在
        Song existSong = this.getById(songDTO.getId());
        if (ObjectUtil.isNull(existSong)) {
            throw new SongNotFoundException(MessageConstant.SONG_NOT_FOUND);
        }

        if (!StrUtil.equals(existSong.getName(), songDTO.getName())) {
            Song duplicateSong = this.lambdaQuery()
                    .eq(Song::getArtistId, songDTO.getArtistId())
                    .eq(Song::getName, songDTO.getName())
                    .ne(Song::getId, songDTO.getId())
                    .one();
            if (ObjectUtil.isNotNull(duplicateSong)) {
                throw new SongExistedException(MessageConstant.SONG_EXISTS + ":" + songDTO.getName());
            }
        }

        Song song = BeanUtil.copyProperties(songDTO, Song.class);
        this.updateById(song);
        log.info("歌曲更新成功，歌曲ID：{}", song.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long songId) {
        log.info("删除歌曲，歌曲ID：{}", songId);

        Song song = this.getById(songId);
        if (ObjectUtil.isNull(song)) {
            throw new SongNotFoundException(MessageConstant.SONG_NOT_FOUND);
        }

        this.removeById(songId);
        log.info("歌曲删除成功，歌曲ID：{}", songId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> songIds) {
        log.info("批量删除歌曲，歌曲ID列表：{}", songIds);

        if (ObjectUtil.isEmpty(songIds)) {
            return;
        }

        this.removeByIds(songIds);
        log.info("批量删除歌曲成功，删除数量：{}", songIds.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String uploadCover(MultipartFile file, Long songId) {
        log.info("上传歌曲封面，歌曲ID：{}", songId);

        // 检查歌曲是否存在
        Song song = this.getById(songId);
        if (ObjectUtil.isNull(song)) {
            throw new SongNotFoundException(MessageConstant.SONG_NOT_FOUND);
        }

        try {
            // 上传文件到MinIO
            String fileName = "song/cover/" + songId + "_" + System.currentTimeMillis() + "_" + file.getOriginalFilename();
            String coverUrl = minioUtils.uploadFile(file, fileName);

            // 更新歌曲封面URL
            song.setCoverUrl(coverUrl);
            this.updateById(song);

            log.info("歌曲封面上传成功，封面URL：{}", coverUrl);
            return coverUrl;
        } catch (Exception e) {
            log.error("歌曲封面上传失败", e);
            throw new RuntimeException("封面上传失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String uploadAudio(MultipartFile file, Long songId) {
        log.info("上传歌曲音频，歌曲ID：{}", songId);

        // 检查歌曲是否存在
        Song song = this.getById(songId);
        if (ObjectUtil.isNull(song)) {
            throw new SongNotFoundException(MessageConstant.SONG_NOT_FOUND);
        }
        try {
            // 上传文件到MinIO
            String fileName = "song/audio/" + songId + "_" + System.currentTimeMillis() + "_" + file.getOriginalFilename();
            String audioUrl = minioUtils.uploadFile(file, fileName);

            // 更新歌曲音频URL
            song.setAudioUrl(audioUrl);
            this.updateById(song);

            log.info("歌曲音频上传成功，音频URL：{}", audioUrl);
            return audioUrl;
        } catch (Exception e) {
            log.error("歌曲音频上传失败", e);
            throw new RuntimeException("音频上传失败");
        }
    }

    @Override
    public long countByArtistId(Long artistId) {
        return this.lambdaQuery()
                .eq(Song::getArtistId, artistId)
                .count();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void softDeleteByArtistId(Long artistId) {
        log.info("软删除歌手ID为{}的所有歌曲", artistId);

        // MyBatis-Plus的软删除会自动设置deleted字段
        LambdaQueryWrapper<Song> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Song::getArtistId, artistId);

        this.remove(queryWrapper);
        log.info("软删除歌手ID为{}的所有歌曲完成", artistId);
    }
}