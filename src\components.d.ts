/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddSongToPlaylistDialog: typeof import('./views/Playlist/components/AddSongToPlaylistDialog.vue')['default']
    AvatarUploadDialog: typeof import('./views/Singer/components/AvatarUploadDialog.vue')['default']
    ContentStatsChart: typeof import('./views/Dashboard/components/ContentStatsChart.vue')['default']
    Dashboard: typeof import('./views/Dashboard/index.vue')['default']
    Edit: typeof import('./views/Playlist/components/edit.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonuser: typeof import('element-plus/es')['ElButtonuser']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapseTransition: typeof import('element-plus/es')['ElCollapseTransition']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElImageViewer: typeof import('element-plus/es')['ElImageViewer']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Feedback: typeof import('./views/Feedback/index.vue')['default']
    FeedbackDetailDialog: typeof import('./views/Feedback/components/FeedbackDetailDialog.vue')['default']
    FeedbackReplyDialog: typeof import('./views/Feedback/components/FeedbackReplyDialog.vue')['default']
    FeedbackStatsChart: typeof import('./views/Dashboard/components/FeedbackStatsChart.vue')['default']
    FeedbackStatusDialog: typeof import('./views/Feedback/components/FeedbackStatusDialog.vue')['default']
    Header: typeof import('./layout/components/NavBar/index.vue')['default']
    Home: typeof import('./views/Home/index.vue')['default']
    Layout: typeof import('./layout/index.vue')['default']
    Login: typeof import('./views/Login/index.vue')['default']
    MetricCard: typeof import('./views/Dashboard/components/MetricCard.vue')['default']
    NavBar: typeof import('./layout/components/NavBar/index.vue')['default']
    PerformanceMonitor: typeof import('./components/PerformanceMonitor.vue')['default']
    Playlist: typeof import('./views/Playlist/index.vue')['default']
    PlaylistDetailDialog: typeof import('./views/Playlist/components/PlaylistDetailDialog.vue')['default']
    PlaylistDialog: typeof import('./views/Playlist/components/PlaylistDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SideBar: typeof import('./layout/components/SideBar/index.vue')['default']
    Singer: typeof import('./views/Singer/index.vue')['default']
    SingerDialog: typeof import('./views/Singer/components/SingerDialog.vue')['default']
    Song: typeof import('./views/Song/index.vue')['default']
    SongDialog: typeof import('./views/Song/components/SongDialog.vue')['default']
    SongFormDialog: typeof import('./views/Song/components/SongFormDialog.vue')['default']
    SongPreviewDialog: typeof import('./views/Song/components/SongPreviewDialog.vue')['default']
    SongUploadDialog: typeof import('./views/Song/components/SongUploadDialog.vue')['default']
    User: typeof import('./views/User/index.vue')['default']
    UserDialog: typeof import('./views/User/components/UserDialog.vue')['default']
    UserTrendChart: typeof import('./views/Dashboard/components/UserTrendChart.vue')['default']
    VirtualTable: typeof import('./components/VirtualTable.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
