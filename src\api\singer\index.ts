import type { SingerInfo, SingerQueryParams } from "@/types/singer";
import request from "@/utils/request";

export interface PageResult<T> {
    total: number;
    list: T[];
}

/**
 * 获取歌手列表（分页）
 * @param params 查询参数
 * @returns 歌手列表分页数据
 */
export function getSingerList(params: SingerQueryParams): Promise<PageResult<SingerInfo>> {
    return request.get('/singer/list', params)
}

/**
 * 获取歌手详情
 * @param singerId 歌手ID
 * @returns 歌手详情
 */
export function getSingerInfo(singerId: number): Promise<SingerInfo> {
    return request.get(`/singer/${singerId}`)
}

/**
 * 添加歌手
 * @param data 歌手数据
 * @returns 操作结果
 */
export function addSinger(data: Partial<SingerInfo>): Promise<unknown> {
    return request.post('/singer', data)
}

/**
 * 更新歌手信息
 * @param data 歌手数据
 * @returns 操作结果
 */
export function updateSinger(data: Partial<SingerInfo>): Promise<unknown> {
    return request.put('/singer', data)
}

/**
 * 删除歌手
 * @param singerId 歌手ID
 * @returns 操作结果
 */
export function deleteSinger(singerId: number): Promise<unknown> {
    return request.delete(`/singer/${singerId}`)
}

/**
 * 批量删除歌手
 * @param singerIds 歌手ID数组
 * @returns 操作结果
 */
export function batchDeleteSingers(singerIds: number[]): Promise<unknown> {
    return request.post("/singer/batch-delete", singerIds)
}

/**
 * 上传歌手头像
 * @param singerId 歌手ID
 * @param file 头像文件
 * @returns 头像URL
 */
export function uploadSingerAvatar(singerId: number, file: File): Promise<{ avatarUrl: string }> {
    const formData = new FormData()
    formData.append('file', file)

    return request.post(`/singer/${singerId}/avatar`, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

