import { defineStore } from 'pinia'
import type { SingerIn<PERSON>, SingerQueryParams } from '@/types/singer'
import {
    getSingerList,
    addSinger as apiAddSinger,
    updateSinger as apiUpdateSinger,
    deleteSinger as apiDeleteSinger,
    batchDeleteSingers,
    uploadSingerAvatar as apiUploadSingerAvatar,
} from '@/api/singer'
import { ElMessage } from 'element-plus'

export const useSingerStore = defineStore('singer', () => {
    // 状态定义
    const singerList = ref<SingerInfo[]>([])
    const currentSinger = ref<SingerInfo | null>(null)
    const total = ref(0)
    const loading = ref(false)

    // 查询参数
    const queryParams = reactive<SingerQueryParams>({
        singerName: '',
        type: '',
        location: '',
        pageNum: 1,
        pageSize: 10
    })

    // 获取歌手列表
    const getList = async () => {
        try {
            loading.value = true
            const res = await getSingerList(queryParams)
            singerList.value = res.list
            total.value = res.total
        } catch (error) {
            console.error('获取歌手列表失败', error)
            ElMessage.error('获取歌手列表失败')
        } finally {
            loading.value = false
        }
    }

    // 重置查询条件
    const resetQuery = () => {
        queryParams.singerName = ''
        queryParams.type = ''
        queryParams.location = ''
        queryParams.pageNum = 1
        getList()
    }

    // 添加歌手
    const addSinger = async (singerData: Partial<SingerInfo>) => {
        try {
            await apiAddSinger(singerData)
            ElMessage.success('添加歌手成功')
            await getList() // 刷新列表
            return true
        } catch (error) {
            console.error('添加歌手失败', error)
            ElMessage.error('添加歌手失败')
            return false
        }
    }

    // 更新歌手信息
    const updateSinger = async (singerData: Partial<SingerInfo>) => {
        try {
            await apiUpdateSinger(singerData)
            ElMessage.success('更新歌手成功')
            await getList() // 刷新列表
            return true
        } catch (error) {
            console.error('更新歌手失败', error)
            ElMessage.error('更新歌手失败')
            return false
        }
    }

    // 删除歌手
    const deleteSinger = async (singerId: number) => {
        try {
            await apiDeleteSinger(singerId)
            ElMessage.success('删除歌手成功')
            await getList() // 刷新列表
            return true
        } catch (error) {
            console.error('删除歌手失败', error)
            ElMessage.error('删除歌手失败')
            return false
        }
    }

    // 批量删除歌手
    const deleteBatchSingers = async (singerIds: number[]) => {
        try {
            await batchDeleteSingers(singerIds)
            ElMessage.success('批量删除歌手成功')
            await getList() // 刷新列表
            return true
        } catch (error) {
            console.error('批量删除歌手失败', error)
            ElMessage.error('批量删除歌手失败')
            return false
        }
    }

    // 上传歌手头像
    const uploadSingerAvatar = async (singerId: number, file: File) => {
        try {
            const res = await apiUploadSingerAvatar(singerId, file)
            ElMessage.success('头像上传成功')
            await getList() // 刷新列表以显示新头像
            return res.avatarUrl
        } catch (error) {
            console.error('头像上传失败', error)
            ElMessage.error('头像上传失败')
            return null
        }
    }



    // 设置当前选中的歌手
    const setCurrentSinger = (singer: SingerInfo | null) => {
        currentSinger.value = singer
    }

    return {
        // 状态
        singerList,
        currentSinger,
        total,
        loading,
        queryParams,

        // 方法
        getList,
        resetQuery,
        addSinger,
        updateSinger,
        deleteSinger,
        deleteBatchSingers,
        uploadSingerAvatar,
        setCurrentSinger,
    }
})