<template>
  <el-dialog
    title="上传歌手头像"
    :model-value="modelValue"
    @close="handleClose"
    width="500px"
    class="avatar-upload-dialog"
  >
    <div class="text-center">
      <!-- 当前头像预览 -->
      <div class="flex flex-col items-center mb-4">
        <div class="text-sm text-gray-600 mb-2">当前头像：</div>
        <el-avatar :size="80" :src="currentAvatar || defaultAvatar">
          <el-icon><User /></el-icon>
        </el-avatar>
      </div>

      <!-- 上传区域 -->
      <el-upload
        ref="uploadRef"
        class="avatar-uploader w-full"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handleFileChange"
        accept="image/*"
        drag
      >
        <div class="flex flex-col items-center justify-center h-full">
          <el-icon class="text-3xl text-gray-400 mb-4" v-if="!uploading">
            <Plus />
          </el-icon>
          <div v-if="uploading" class="flex flex-col items-center">
            <el-progress type="circle" :percentage="uploadProgress" :width="60" />
            <div class="text-sm mt-2">上传中...</div>
          </div>
          <div v-else class="text-gray-600">
            <div class="text-base mb-1">点击或拖拽上传头像</div>
            <div class="text-sm text-gray-500">支持 JPG、PNG 格式，文件大小不超过 2MB</div>
          </div>
        </div>
      </el-upload>

      <!-- 预览新头像 -->
      <div v-if="previewUrl" class="flex flex-col items-center mt-4">
        <div class="text-sm text-gray-600 mb-2">预览新头像：</div>
        <el-avatar :size="80" :src="previewUrl">
          <el-icon><User /></el-icon>
        </el-avatar>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3 pt-4 border-t border-gray-200">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="!selectedFile || uploading"
          :loading="uploading"
        >
          确定上传
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { Plus, User } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import { useSingerStore } from '@/stores/singer'

defineOptions({
  name: 'AvatarUploadDialog',
})

const props = defineProps<{
  modelValue: boolean
  singerId: number
}>()

const emit = defineEmits(['update:modelValue', 'success'])

// Store
const singerStore = useSingerStore()

// 上传相关
const uploading = ref(false)
const uploadProgress = ref(0)
const previewUrl = ref('')
const currentAvatar = ref('')
const selectedFile = ref<File | null>(null)

// 默认头像
const defaultAvatar = '/favicon.ico'

// 监听歌手ID变化，获取当前头像
watch(
  () => props.singerId,
  async (newId) => {
    if (newId) {
      // 从当前歌手列表中找到对应歌手的头像
      const singer = singerStore.singerList.find((s) => s.singerId === newId)
      if (singer) {
        currentAvatar.value = singer.avatar || ''
      }
    }
  },
  { immediate: true },
)

// 文件选择处理
const handleFileChange = (file: UploadFile) => {
  const rawFile = file.raw
  if (!rawFile) return

  const isImage = rawFile.type.startsWith('image/')
  const isLt2M = rawFile.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return
  }

  selectedFile.value = rawFile
  // 创建预览URL
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
  }
  previewUrl.value = URL.createObjectURL(rawFile)
}

// 确认上传
const handleConfirm = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请先选择要上传的图片')
    return
  }

  try {
    uploading.value = true
    uploadProgress.value = 0

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10
      }
    }, 100)

    // 使用 store 中的上传方法
    const avatarUrl = await singerStore.uploadSingerAvatar(props.singerId, selectedFile.value)

    clearInterval(progressInterval)
    uploadProgress.value = 100

    if (avatarUrl) {
      emit('success')
      handleClose()
    }
  } catch (error) {
    console.error('上传失败:', error)
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
  // 清理预览URL
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
    previewUrl.value = ''
  }
  selectedFile.value = null
  uploading.value = false
  uploadProgress.value = 0
}
</script>

<style scoped>
.avatar-upload-dialog :deep(.el-dialog__body) {
  @apply p-6;
}

.avatar-uploader :deep(.el-upload) {
  @apply w-full;
}

.avatar-uploader :deep(.el-upload-dragger) {
  @apply w-full h-44 flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg cursor-pointer relative overflow-hidden transition-all duration-300;
}

.avatar-uploader :deep(.el-upload-dragger:hover) {
  @apply border-blue-500;
}
</style>
