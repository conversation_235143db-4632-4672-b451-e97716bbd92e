<template>
  <div class="song-container min-h-screen relative overflow-hidden">
    <!-- 背景装饰 -->
    <div
      class="absolute inset-0 bg-gradient-to-br from-green-50 via-white to-blue-50 pointer-events-none"
    ></div>
    <div
      class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-r from-green-400/10 to-blue-400/10 rounded-full blur-3xl pointer-events-none"
    ></div>

    <div class="relative z-10 p-6">
      <!-- 页面标题 -->
      <div class="mb-4">
        <div class="flex items-center space-x-3 mb-1">
          <div
            class="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center"
          >
            <Icon icon="mdi:music-note" class="text-white text-lg" />
          </div>
          <h1
            class="text-xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent"
          >
            歌曲管理
          </h1>
        </div>
        <p class="text-sm text-gray-600">管理音乐歌曲信息，包括歌曲上传、编辑和分类</p>
      </div>

      <div class="flex h-full gap-4">
        <!-- 左侧歌手列表 -->
        <div class="w-[240px] flex-shrink-0">
          <el-card class="h-full enhanced-glass-card" shadow="never">
            <template #header>
              <div class="flex items-center space-x-3 py-1">
                <div
                  class="w-6 h-6 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center"
                >
                  <Icon icon="mdi:account-music" class="text-white text-sm" />
                </div>
                <div>
                  <h3 class="text-base font-semibold text-gray-800">歌手列表</h3>
                  <p class="text-xs text-gray-500">选择歌手查看歌曲</p>
                </div>
              </div>

              <!-- 歌手搜索 -->
              <el-input
                v-model="singerSearchKeyword"
                placeholder="搜索歌手..."
                clearable
                class="enhanced-input mt-3"
                size="default"
              >
                <template #prefix>
                  <Icon icon="mdi:magnify" />
                </template>
              </el-input>
            </template>

            <!-- 歌手列表 -->
            <div class="singer-list max-h-[calc(100vh-300px)] overflow-y-auto">
              <div
                v-for="singer in filteredSingers"
                :key="singer.singerId"
                :class="[
                  'singer-item p-3 mb-2 rounded-lg cursor-pointer transition-all duration-300 enhanced-singer-item',
                  selectedSinger?.singerId === singer.singerId
                    ? 'bg-gradient-to-r from-green-50 to-blue-50 border-2 border-green-200 shadow-md'
                    : 'bg-white hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 border-2 border-transparent hover:border-gray-200 hover:shadow-sm',
                ]"
                @click="selectSinger(singer)"
              >
                <div class="flex items-center">
                  <el-avatar :size="40" :src="singer.avatar || defaultAvatar" class="shadow-sm">
                    <Icon icon="mdi:account-music" />
                  </el-avatar>
                  <div class="ml-3 flex-1 min-w-0">
                    <div class="flex items-center space-x-2 mb-1">
                      <span class="font-medium text-gray-900 truncate">{{
                        singer.singerName
                      }}</span>
                      <el-tag v-if="singer.type" type="info" size="small" class="text-xs">{{
                        singer.type
                      }}</el-tag>
                    </div>
                    <div class="text-sm text-gray-500 flex items-center space-x-1">
                      <Icon icon="mdi:map-marker" class="text-xs" />
                      <span>{{ singer.location }}</span>
                    </div>
                    <div class="text-xs text-gray-400 flex items-center space-x-1 mt-1">
                      <Icon icon="mdi:music-note-multiple" class="text-xs" />
                      <span>歌曲数: {{ singer.songsCount || 0 }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="filteredSingers.length === 0" class="text-center text-gray-500 py-8">
                <Icon icon="mdi:account-music-outline" class="text-4xl mb-2 opacity-50" />
                <p>暂无歌手数据</p>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 右侧歌曲管理 -->
        <div class="flex-1 min-w-0">
          <!-- 搜索区域 -->
          <el-card class="search-card enhanced-glass-card mb-4" shadow="never">
            <template #header>
              <div class="flex items-center space-x-3 py-1">
                <div
                  class="w-6 h-6 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center"
                >
                  <Icon icon="mdi:magnify" class="text-white text-sm" />
                </div>
                <div>
                  <h3 class="text-base font-semibold text-gray-800">歌曲搜索</h3>
                  <p class="text-xs text-gray-500">快速查找歌曲信息</p>
                </div>
              </div>
            </template>

            <el-form
              :model="songStore.queryParams"
              @submit.prevent
              class="enhanced-search-form"
              label-width="60px"
              label-position="left"
            >
              <el-row :gutter="16" class="items-end">
                <el-col :span="6">
                  <el-form-item label="歌曲名称：" class="enhanced-form-item" label-width="75">
                    <el-input
                      v-model="songStore.queryParams.name"
                      placeholder="请输入歌曲名称"
                      clearable
                      @keyup.enter="handleSearch"
                      class="enhanced-input"
                      size="default"
                    />
                  </el-form-item>
                </el-col>

                <el-col :span="6">
                  <el-form-item label="专辑：" class="enhanced-form-item" label-width="50">
                    <el-input
                      v-model="songStore.queryParams.album"
                      placeholder="请输入专辑名称"
                      clearable
                      @keyup.enter="handleSearch"
                      class="enhanced-input"
                      size="default"
                    />
                  </el-form-item>
                </el-col>

                <el-col :span="4">
                  <el-form-item label="风格：" class="enhanced-form-item" label-width="50">
                    <el-select
                      v-model="songStore.queryParams.style"
                      placeholder="请选择风格"
                      clearable
                      class="enhanced-select w-full"
                      size="default"
                    >
                      <el-option
                        v-for="style in SongStyles"
                        :key="style"
                        :label="style"
                        :value="style"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item class="enhanced-form-item">
                    <div class="flex items-center space-x-3">
                      <el-button
                        type="primary"
                        @click="handleSearch"
                        class="enhanced-btn enhanced-btn-primary"
                      >
                        <Icon icon="mdi:magnify" class="mr-2" />
                        搜索
                      </el-button>
                      <el-button @click="handleReset" class="enhanced-btn enhanced-btn-reset">
                        <Icon icon="mdi:refresh" class="mr-2" />
                        重置
                      </el-button>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-card>

          <!-- 操作按钮区域 -->
          <el-card class="table-card enhanced-glass-card" shadow="never">
            <template #header>
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div
                    class="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center"
                  >
                    <Icon icon="mdi:table-edit" class="text-white text-lg" />
                  </div>
                  <div>
                    <h3 class="text-lg font-semibold text-gray-800">
                      歌曲列表
                      <span v-if="selectedSinger" class="text-sm font-normal text-gray-500">
                        - {{ selectedSinger.singerName }}
                      </span>
                    </h3>
                    <p class="text-sm text-gray-500">共 {{ songStore.total }} 条记录</p>
                  </div>
                </div>

                <div class="flex items-center space-x-2">
                  <el-button
                    type="primary"
                    @click="handleAdd"
                    :disabled="!selectedSinger"
                    v-if="hasPermission('user:add')"
                    class="enhanced-btn-primary"
                  >
                    <Icon icon="mdi:music-note-plus" class="mr-2" />
                    新增歌曲
                  </el-button>

                  <el-button
                    type="danger"
                    :disabled="selectedSongs.length === 0"
                    @click="handleBatchDelete"
                    v-if="hasPermission('user:delete')"
                    class="enhanced-btn"
                  >
                    <el-icon><Delete /></el-icon>
                    批量删除 ({{ selectedSongs.length }})
                  </el-button>

                  <el-button type="success" @click="handleRefresh" class="enhanced-btn">
                    <Icon icon="mdi:refresh" class="mr-2" />
                    刷新
                  </el-button>
                </div>
              </div>
            </template>

            <!-- 表格区域 -->
            <el-table
              v-loading="songStore.loading"
              :data="songStore.songList"
              @selection-change="handleSelectionChange"
              highlight-current-row
              row-key="id"
              class="enhanced-table"
              stripe
            >
              <el-table-column type="selection" width="50" align="center" fixed="left" />
              <el-table-column label="歌曲编号" prop="id" align="center" width="85" />
              <el-table-column label="封面" align="center" width="100">
                <template #default="{ row }">
                  <div class="flex justify-center">
                    <el-image
                      :src="row.coverUrl || defaultCover"
                      class="w-12 h-12 rounded-lg cursor-pointer shadow-md hover:shadow-lg transition-shadow"
                      fit="cover"
                      @click="handlePreviewImage(row.coverUrl)"
                    >
                      <template #error>
                        <div
                          class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center"
                        >
                          <Icon icon="mdi:music" class="text-gray-400 text-xl" />
                        </div>
                      </template>
                    </el-image>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="歌名"
                prop="name"
                align="center"
                min-width="100"
                show-overflow-tooltip
              />
              <el-table-column label="专辑" prop="album" align="center" min-width="100" />
              <el-table-column label="风格" prop="style" align="center" width="100" />
              <el-table-column label="时长" prop="duration" align="center" width="100" />
              <el-table-column label="点赞数" align="center" width="100">
                <template #default="{ row }">
                  <span>{{ formatNumber(row.likeCount || 0) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="发行时间" prop="releaseTime" align="center" width="120" />
              <el-table-column label="操作" align="center" width="200" fixed="right">
                <template #default="{ row }">
                  <div class="flex items-center justify-center space-x-2">
                    <el-button

                      type="primary"
                      size="small"
                      class="enhanced-btn-small enhanced-btn-primary"
                      @click="handleEdit(row)"
                    >
                      <Icon icon="mdi:pencil" class="mr-1" />
                      编辑
                    </el-button>
                    <el-button

                      type="danger"
                      size="small"
                      class="enhanced-btn-small"
                      @click="handleDelete(row)"
                    >
                      <Icon icon="mdi:delete" class="mr-1" /> 删除
                    </el-button>
                    <el-dropdown @command="(command) => handleMoreCommand(command, row)">
                      <el-button  type="info" size="small" class="enhanced-btn-small">
                        <el-icon size="14" ><More /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="uploadCover">
                            <el-icon><Picture /></el-icon> 上传封面
                          </el-dropdown-item>
                          <el-dropdown-item command="uploadAudio">
                            <el-icon><Upload /></el-icon> 上传音频
                          </el-dropdown-item>
                          <el-dropdown-item command="preview">
                            <el-icon><View /></el-icon> 预览详情
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页区域 -->
            <div class="flex justify-center mt-6">
              <el-pagination
                v-model:current-page="songStore.queryParams.pageNum"
                v-model:page-size="songStore.queryParams.pageSize"
                :page-sizes="[10, 20, 30, 50]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="songStore.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                background
                class="enhanced-pagination"
              />
            </div>
          </el-card>
        </div>
      </div>

      <!-- 添加/编辑歌曲对话框 -->
      <SongDialog
        :dialog-props="dialogProps"
        @update:visible="dialogProps.visible = $event"
        @success="handleDialogSuccess"
      />

      <!-- 上传对话框 -->
      <SongUploadDialog
        v-model="showUploadDialog"
        :song-id="currentSong?.id"
        :upload-type="uploadType"
        @success="handleUploadSuccess"
      />

      <!-- 图片预览对话框 -->
      <el-dialog v-model="previewImageVisible" title="封面预览" width="60%" center>
        <div class="text-center">
          <el-image
            :src="previewImageUrl"
            fit="contain"
            style="max-width: 100%; max-height: 70vh"
          />
        </div>
      </el-dialog>

      <!-- 歌曲试听对话框 -->
      <SongPreviewDialog
        v-model="previewDialogVisible"
        :song-data="previewSong"
        :default-cover="defaultCover"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SongView',
})

import { useSongStore } from '@/stores/song'
import { useSingerStore } from '@/stores/singer'
import { SongStyles } from '@/types/song'
import type { SongInfo } from '@/types/song'
import type { SingerInfo } from '@/types/singer'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Delete,  Upload, Picture, More, View } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import usePermission from '@/hooks/usePermission'

// 使用权限hook
const { hasPermission } = usePermission()

// 默认图片
const defaultAvatar = ref('/favicon.ico')
const defaultCover = ref('/favicon.ico')

// Store
const songStore = useSongStore()
const singerStore = useSingerStore()

// 歌手相关
const selectedSinger = ref<SingerInfo | null>(null)
const singerSearchKeyword = ref('')

// 过滤后的歌手列表
const filteredSingers = computed(() => {
  if (!singerSearchKeyword.value) {
    return singerStore.singerList
  }
  return singerStore.singerList.filter((singer) =>
    singer.singerName.toLowerCase().includes(singerSearchKeyword.value.toLowerCase()),
  )
})

// 弹窗属性
interface DialogProps {
  visible: boolean
  title: string
  type: 'add' | 'edit'
  data?: Partial<SongInfo>
}

const dialogProps = reactive<DialogProps>({
  visible: false,
  title: '',
  type: 'add',
  data: undefined,
})

const selectedSongs = ref<SongInfo[]>([])
const showUploadDialog = ref(false)
const currentSong = ref<SongInfo | null>(null)
const uploadType = ref<'cover' | 'audio'>('cover')

// 选择歌手
const selectSinger = (singer: SingerInfo) => {
  selectedSinger.value = singer
  // 设置查询参数中的歌手ID
  songStore.queryParams.artistId = singer.singerId
  songStore.queryParams.pageNum = 1
  songStore.getList()
}

// 搜索
const handleSearch = () => {
  songStore.queryParams.pageNum = 1
  songStore.getList()
}

// 重置
const handleReset = () => {
  songStore.resetQuery()
  if (selectedSinger.value) {
    songStore.queryParams.artistId = selectedSinger.value.singerId
  }
}

// 刷新
const handleRefresh = () => {
  songStore.getList()
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  songStore.queryParams.pageSize = val
  songStore.getList()
}

// 页码变化
const handleCurrentChange = (val: number) => {
  songStore.queryParams.pageNum = val
  songStore.getList()
}

// 添加歌曲
const handleAdd = () => {
  if (!selectedSinger.value) {
    ElMessage.warning('请先选择歌手')
    return
  }
  dialogProps.visible = true
  dialogProps.title = '新增歌曲'
  dialogProps.type = 'add'
  dialogProps.data = {
    artistId: selectedSinger.value.singerId,
  }
  console.log(dialogProps.data);
  console.log(selectedSinger.value.singerId+"：歌手ID");

}

// 编辑歌曲
const handleEdit = (song: SongInfo) => {
  dialogProps.visible = true
  dialogProps.title = '编辑歌曲'
  dialogProps.type = 'edit'
  dialogProps.data = { ...song }
}

// 删除歌曲
const handleDelete = async (song: SongInfo) => {
  try {
    await ElMessageBox.confirm(`确认删除歌曲【${song.name}】吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await songStore.deleteSong(song.id)
  } catch (error) {
    console.log(error)
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedSongs.value.length === 0) {
    ElMessage.warning('请至少选择一首歌曲')
    return
  }

  const songNames = selectedSongs.value.map((item) => item.name).join('、')
  const songIds = selectedSongs.value.map((item) => item.id)

  try {
    await ElMessageBox.confirm(`确认删除以下歌曲吗？<br/>${songNames}`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
    })

    await songStore.batchDelete(songIds)
    selectedSongs.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败', error)
    }
  }
}

// 选择变化
const handleSelectionChange = (selection: SongInfo[]) => {
  selectedSongs.value = selection
}

// 对话框成功回调
const handleDialogSuccess = async (formData: Partial<SongInfo>, type: 'add' | 'edit') => {
  try {
    if (type === 'add') {
      await songStore.addSong(formData)
    } else {
      await songStore.updateSong(formData)
    }
    dialogProps.visible = false
    songStore.getList()
  } catch (error) {
    console.error('操作失败:', error)
  }
}

// 上传成功回调
const handleUploadSuccess = () => {
  showUploadDialog.value = false
  songStore.getList()
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toString()
}

// 更多操作处理
const handleMoreCommand = (command: string, song: SongInfo) => {
  switch (command) {
    case 'uploadCover':
      handleUploadCover(song)
      break
    case 'uploadAudio':
      handleUploadAudio(song)
      break
    case 'preview':
      handlePreview(song)
      break
  }
}

// 上传封面
const handleUploadCover = (song: SongInfo) => {
  currentSong.value = song
  uploadType.value = 'cover'
  showUploadDialog.value = true
}

// 上传音频
const handleUploadAudio = (song: SongInfo) => {
  currentSong.value = song
  uploadType.value = 'audio'
  showUploadDialog.value = true
}

const previewDialogVisible = ref(false)
const previewSong = ref<SongInfo | null>(null)

// 预览详情
const handlePreview = (song: SongInfo) => {
  previewSong.value = song
  previewDialogVisible.value = true
}

// 图片预览
const previewImageVisible = ref(false)
const previewImageUrl = ref('')

const handlePreviewImage = (url: string) => {
  if (url) {
    previewImageUrl.value = url
    previewImageVisible.value = true
  }
}

// 重置歌手选择状态
const resetSingerSelection = () => {
  selectedSinger.value = null
  songStore.resetQuery()
}

// 初始化
onMounted(() => {
  // 重置歌手选择状态，确保每次进入页面时都是干净的状态
  resetSingerSelection()
  singerStore.getList()
})

// 页面卸载时清理状态
onBeforeUnmount(() => {
  // 确保离开页面时重置歌手选择状态
  resetSingerSelection()
})
</script>

<style scoped>
.search-form {
  @apply flex flex-wrap items-center;
}

.search-card :deep(.el-form) {
  @apply flex flex-wrap items-center;
}

.search-card :deep(.el-form-item) {
  @apply mb-2 mr-4;
}

.search-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
}

.button-group {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  flex-shrink: 0 !important;
}



.singer-item {
  transition: all 0.2s ease;
}

.singer-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.singer-list::-webkit-scrollbar {
  width: 6px;
}

.singer-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.singer-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.singer-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 操作按钮对齐样式 */
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 隐藏下拉菜单的箭头指示器和边框 */
.action-buttons :deep(.el-dropdown) {
  outline: none;
}

.action-buttons :deep(.el-dropdown .el-button) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.action-buttons :deep(.el-dropdown .el-button:hover) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.action-buttons :deep(.el-dropdown .el-button:focus) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

.action-buttons :deep(.el-dropdown .el-button:active) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 隐藏下拉菜单的默认箭头 */
.action-buttons :deep(.el-dropdown .el-dropdown__caret-button) {
  display: none !important;
}

.action-buttons :deep(.el-dropdown .el-dropdown__caret-button::before) {
  display: none !important;
}

/* 完全去掉表格边框 */
.el-table :deep(.el-table__border) {
  display: none;
}

/* 去掉表头边框 */
.el-table :deep(.el-table__header-wrapper .el-table__border) {
  display: none;
}

/* 去掉单元格边框 */
.el-table :deep(.el-table__cell) {
  border: none;
}

/* 完全去掉表格边框 */
.el-table :deep(.el-table__border) {
  display: none;
}

/* 去掉表头边框 */
.el-table :deep(.el-table__header-wrapper .el-table__border) {
  display: none;
}

/* 去掉单元格边框 */
.el-table :deep(.el-table__cell) {
  border: none;
}

/* 🎵 歌曲管理页面增强样式 */

/* 搜索表单样式 */
.enhanced-search-form {
  background: transparent;
}

.search-card {
  border-radius: 12px;
}

.search-card :deep(.el-card__header) {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.search-card :deep(.el-card__body) {
  padding: 16px 20px;
}


.enhanced-form-item :deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0 !important;
  line-height: 32px; /* 与输入框高度对齐 */
  display: flex;
  align-items: center;
  font-size: 14px;
  height: 32px; /* 固定标签高度 */
  padding-right: 4px; /* 减少右侧间距 */
}

.enhanced-form-item {
  margin-bottom: 16px !important;
  align-items: center;
  display: flex;
}

.enhanced-form-item :deep(.el-form-item__content) {
  line-height: 32px;
  min-height: 32px; /* 确保内容区域最小高度 */
  display: flex;
  align-items: center;
  flex: 1;
}

/* 确保输入框和选择框的高度一致 */
.enhanced-input :deep(.el-input__inner),
.enhanced-select :deep(.el-input__inner) {
  height: 32px !important;
  line-height: 32px !important;
}

/* 日期选择器对齐 */
.enhanced-date-picker :deep(.el-input__inner) {
  height: 32px !important;
  line-height: 32px !important;
}

/* 选择框下拉箭头对齐 */
.enhanced-select :deep(.el-input__suffix) {
  height: 32px !important;
  display: flex;
  align-items: center;
}

/* 选择框选项样式优化 */
.enhanced-select :deep(.el-select-dropdown__item) {
  padding: 8px 12px;
  min-height: 36px;
  display: flex;
  align-items: center;
}

/* 确保选择框有足够的宽度显示选项文字 */
.enhanced-select {
  min-width: 120px;
}

.enhanced-input :deep(.el-input__wrapper) {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.enhanced-input :deep(.el-input__wrapper):hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.enhanced-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.enhanced-select :deep(.el-select__wrapper) {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.enhanced-select :deep(.el-select__wrapper):hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}
/* 歌手列表项样式 */
.enhanced-singer-item {
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-singer-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 玻璃态卡片样式 */
.enhanced-glass-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.enhanced-glass-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.95);
}

/* 按钮样式 */
.enhanced-btn {
  border-radius: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.enhanced-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.enhanced-btn-primary {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white !important;
}
.enhanced-btn-small {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 4px 8px;
  font-size: 12px;
}

.enhanced-btn-reset {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white !important;
}

/* 表格样式 */
.enhanced-table {
  border-radius: 12px;
  overflow: hidden;
}

.enhanced-table :deep(.el-table__header) {
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
}

.enhanced-table :deep(.el-table__row:hover) {
  background-color: rgba(16, 185, 129, 0.05) !important;
}

/* 分页样式 */
.enhanced-pagination :deep(.el-pager li) {
  border-radius: 8px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.enhanced-pagination :deep(.el-pager li:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(83, 181, 144, 0.2);
}

.enhanced-pagination :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  font-weight: 600;
}
</style>
