package com.jpzz.pojo.vo.client;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 客户端用户信息VO
 * <AUTHOR>
 */
@Data
public class ClientUserVO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 会员等级
     */
    private String memberLevel;

    /**
     * 注册时间
     */
    private LocalDateTime joinDate;

    /**
     * 是否VIP
     */
    private Boolean isVip;

    /**
     * VIP到期时间
     */
    private LocalDateTime vipExpireTime;

    /**
     * 用户状态
     */
    private String status;
}
