import { useUserStore } from "@/stores/user";
import type { ApiResponse } from "@/types/api";
import axios from "axios";
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import { ElMessage, ElMessageBox } from "element-plus";

const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 5000,
})

service.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

service.interceptors.response.use(
  <T>(response: AxiosResponse<ApiResponse<T>>) => {
    const res = response.data
    if (res.code !== 200) {
      ElMessage({
        message: res.message || '系统错误',
        type: 'error',
        duration: 5000,
      })
      // 如果状态码为401，则提示用户重新登录或Token过期
      if (res.code === 401) {
        ElMessageBox.confirm(
          '您的登录状态已过期，请重新登录',
          '系统提示',
          {
            confirmButtonText: '重新登录',
            cancelButtonText: '取消',
            type: 'warning',
          }
        ).then(() => {
          const userStore = useUserStore()
          userStore.logout()
          location.reload()
        })
      }
      return Promise.reject(new Error(res.message || '系统错误'))
    } else {
      return res.data as T
    }
  },
  (error) => {
    let message = '请求失败'
    if (error.response) {
      const status = error.response.status
      switch (status) {
        case 400: message = '请求错误'; break
        case 401: message = '未授权，请重新登录'; break
        case 403: message = '拒绝访问'; break
        case 404: message = '请求地址不存在'; break
        case 500: message = '服务器内部错误'; break
        default: message = `请求失败(${status})`
      }
    } else if (error.request) {

      if (error.message.includes('timeout')) {
        message = '请求超时'
      } else {
        message = '网络错误，请检查您的网络连接'
      }
      ElMessage({
        message,
        type: 'error',
        duration: 5000,
      })
      return Promise.reject(error)
    }
  }
)

const request = {
  get<T = unknown>(url: string, params?: object, config?: AxiosRequestConfig): Promise<T> {
    return service.get(url, { params, ...config })
  },
  post<T = unknown>(url: string, data?: object, config?: AxiosRequestConfig): Promise<T> {
    return service.post(url, data, config)
  },
  put<T = unknown>(url: string, data?: object, config?: AxiosRequestConfig): Promise<T> {
    return service.put(url, data, config)
  },
  delete<T = unknown>(url: string, params?: object, config?: AxiosRequestConfig): Promise<T> {
    return service.delete(url, config)
  }
}

export default request
