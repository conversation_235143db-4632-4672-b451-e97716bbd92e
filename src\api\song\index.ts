import type { SongInfo, SongQueryParams } from "@/types/song";
import request from "@/utils/request";

export interface PageResult<T> {
    total: number;
    list: T[];
}

/**
 * 获取歌曲列表（分页）
 * @param params 查询参数
 * @returns 歌曲列表分页数据
 */
export function getSongList(params: SongQueryParams): Promise<PageResult<SongInfo>> {
    return request.get('/song/list', params)
}

/**
 * 获取歌曲详情
 * @param songId 歌曲ID
 * @returns 歌曲详情
 */
export function getSongInfo(songId: number): Promise<SongInfo> {
    return request.get(`/song/${songId}`)
}

/**
 * 添加歌曲
 * @param data 歌曲数据
 * @returns 操作结果
 */
export function addSong(data: Partial<SongInfo>): Promise<unknown> {
    return request.post('/song', data)
}

/**
 * 更新歌曲信息
 * @param data 歌曲数据
 * @returns 操作结果
 */
export function updateSong(data: Partial<SongInfo>): Promise<unknown> {
    return request.put('/song', data)
}

/**
 * 删除歌曲
 * @param songId 歌曲ID
 * @returns 操作结果
 */
export function deleteSong(songId: number): Promise<unknown> {
    return request.delete(`/song/${songId}`)
}

/**
 * 批量删除歌曲
 * @param songIds 歌曲ID数组
 * @returns 操作结果
 */
export function batchDeleteSongs(songIds: number[]): Promise<unknown> {
    return request.post('/song/batch-delete', songIds )
}

/**
 * 上传歌曲封面
 * @param songId 歌曲ID
 * @param file 封面文件
 * @returns 上传结果
 */
export function uploadSongCover(songId: number, file: File): Promise<{ url: string }> {
    const formData = new FormData()
    formData.append('file', file)
    return request.post(`/song/${songId}/cover`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
    })
}

/**
 * 上传歌曲音频文件
 * @param songId 歌曲ID
 * @param file 音频文件
 * @returns 上传结果
 */
export function uploadSongAudio(songId: number, file: File): Promise<{ url: string }> {
    const formData = new FormData()
    formData.append('file', file)
    return request.post(`/song/${songId}/audio`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
    })
}
