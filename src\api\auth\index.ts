// src/api/auth/index.ts
import request from '@/utils/request'
import type { UserInfo } from '@/types/user'
import { mockLogin, mockGetUserInfo, mockLogout } from '@/utils/mockApi'
import type { ApiResponse } from '@/types/api'
// 判断是否使用模拟数据（可以通过环境变量控制）
const USE_MOCK = false

/**
 * 登录接口
 * @param username 用户名
 * @param password 密码
 * @returns Promise<{token: string}>
 */
export async function login(username: string, password: string) {
  if (USE_MOCK) {
    try {
      const res = await mockLogin(username, password) as ApiResponse<{ token: string }>
      return res.data
    } catch (error) {
      return Promise.reject(error)
    }
  }
  try {
    return await request.post<{ token: string }>('/auth/login', {
      username,
      password
    })
  } catch (error) {
    // 确保错误被正确传递
    return Promise.reject(error)
  }
}

/**
 * 获取当前登录用户信息
 * @returns Promise<UserInfo>
 */
export async function getUserInfo() {
  if (USE_MOCK) {
    try {
      const token = localStorage.getItem('token') || ''
      const res = await mockGetUserInfo(token) as ApiResponse<UserInfo>
      return res.data
    } catch (error) {
      return Promise.reject(error)
    }
  }
  return request.get<UserInfo>('/auth/info')
}

/**
 * 退出登录
 * @returns Promise<any>
 */
export async function logout() {
  if (USE_MOCK) {
    try {
      const res = await mockLogout() as ApiResponse<null>
      return res.data
    } catch (error) {
      return Promise.reject(error)
    }
  }
  return request.post('/auth/logout')
}


/**
 * 刷新token
 * @returns Promise<{token: string}>
 */
/* export function refreshToken() {
  // 模拟数据中不实现此功能
  return request.post<{ token: string }>('/auth/refresh')
}
  */