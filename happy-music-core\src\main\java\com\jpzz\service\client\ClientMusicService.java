package com.jpzz.service.client;

import com.jpzz.pojo.dto.client.PlayRecordDTO;
import com.jpzz.pojo.vo.client.ClientSongVO;
import com.jpzz.pojo.vo.client.ClientPlaylistVO;
import com.jpzz.result.PageResult;

import java.util.List;

/**
 * 客户端音乐服务接口
 * <AUTHOR>
 */
public interface ClientMusicService {

    /**
     * 获取推荐歌曲
     * @param userId 用户ID(可为空，游客模式)
     * @param page 页码
     * @param size 每页大小
     * @return 推荐歌曲列表
     */
    PageResult<ClientSongVO> getRecommendSongs(Long userId, Integer page, Integer size);

    /**
     * 获取热门歌曲
     * @param page 页码
     * @param size 每页大小
     * @return 热门歌曲列表
     */
    PageResult<ClientSongVO> getHotSongs(Integer page, Integer size);

    /**
     * 获取新歌推荐
     * @param page 页码
     * @param size 每页大小
     * @return 新歌列表
     */
    PageResult<ClientSongVO> getNewSongs(Integer page, Integer size);

    /**
     * 获取热门歌单
     * @param page 页码
     * @param size 每页大小
     * @return 热门歌单列表
     */
    PageResult<ClientPlaylistVO> getHotPlaylists(Integer page, Integer size);

    /**
     * 根据ID获取歌曲详情
     * @param songId 歌曲ID
     * @param userId 用户ID(用于获取用户相关状态)
     * @return 歌曲详情
     */
    ClientSongVO getSongDetail(Long songId, Long userId);

    /**
     * 根据ID获取歌单详情
     * @param playlistId 歌单ID
     * @param userId 用户ID
     * @return 歌单详情
     */
    ClientPlaylistVO getPlaylistDetail(Long playlistId, Long userId);

    /**
     * 获取歌单中的歌曲列表
     * @param playlistId 歌单ID
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 歌曲列表
     */
    PageResult<ClientSongVO> getPlaylistSongs(Long playlistId, Long userId, Integer page, Integer size);

    /**
     * 搜索歌曲
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    PageResult<ClientSongVO> searchSongs(String keyword, Long userId, Integer page, Integer size);

    /**
     * 搜索歌单
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    PageResult<ClientPlaylistVO> searchPlaylists(String keyword, Long userId, Integer page, Integer size);

    /**
     * 记录播放行为
     * @param playRecordDTO 播放记录
     */
    void recordPlay(PlayRecordDTO playRecordDTO);

    /**
     * 获取用户最近播放
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 最近播放列表
     */
    PageResult<ClientSongVO> getRecentPlays(Long userId, Integer page, Integer size);

    /**
     * 喜欢/取消喜欢歌曲
     * @param userId 用户ID
     * @param songId 歌曲ID
     * @param isLike true-喜欢, false-取消喜欢
     */
    void likeSong(Long userId, Long songId, Boolean isLike);

    /**
     * 收藏/取消收藏歌曲
     * @param userId 用户ID
     * @param songId 歌曲ID
     * @param isCollect true-收藏, false-取消收藏
     */
    void collectSong(Long userId, Long songId, Boolean isCollect);

    /**
     * 喜欢/取消喜欢歌单
     * @param userId 用户ID
     * @param playlistId 歌单ID
     * @param isLike true-喜欢, false-取消喜欢
     */
    void likePlaylist(Long userId, Long playlistId, Boolean isLike);

    /**
     * 收藏/取消收藏歌单
     * @param userId 用户ID
     * @param playlistId 歌单ID
     * @param isCollect true-收藏, false-取消收藏
     */
    void collectPlaylist(Long userId, Long playlistId, Boolean isCollect);

    /**
     * 获取用户喜欢的歌曲
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 喜欢的歌曲列表
     */
    PageResult<ClientSongVO> getUserLikedSongs(Long userId, Integer page, Integer size);

    /**
     * 获取用户收藏的歌曲
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 收藏的歌曲列表
     */
    PageResult<ClientSongVO> getUserCollectedSongs(Long userId, Integer page, Integer size);

    /**
     * 获取用户收藏的歌单
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 收藏的歌单列表
     */
    PageResult<ClientPlaylistVO> getUserCollectedPlaylists(Long userId, Integer page, Integer size);

    /**
     * 根据风格获取歌曲
     * @param style 音乐风格
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 歌曲列表
     */
    PageResult<ClientSongVO> getSongsByStyle(String style, Long userId, Integer page, Integer size);

    /**
     * 获取歌手的热门歌曲
     * @param singerId 歌手ID
     * @param userId 用户ID
     * @param limit 数量限制
     * @return 热门歌曲列表
     */
    List<ClientSongVO> getSingerHotSongs(Long singerId, Long userId, Integer limit);
}
