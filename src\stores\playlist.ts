import { defineStore } from 'pinia'
import type { PlaylistInfo, PlaylistQueryParams } from '@/types/playlist'
import {
    getPlaylistList,
    addPlaylist as apiAddPlaylist,
    updatePlaylist as apiUpdatePlaylist,
    deletePlaylist as apiDeletePlaylist,
    batchDeletePlaylists,
    uploadPlaylistCover as apiUploadPlaylistCover,
    getPlaylistInfo,
    getPlaylistSongs,
    addSongsToPlaylist,
    removeSongFromPlaylist,
    batchRemoveSongsFromPlaylist,
    type PlaylistSongQueryParams,
} from '@/api/playlist'
import { ElMessage } from 'element-plus'

export const usePlaylistStore = defineStore('playlist', () => {
    // 状态定义
    const playlistList = ref<PlaylistInfo[]>([])
    const currentPlaylist = ref<PlaylistInfo | null>(null)
    const total = ref(0)
    const loading = ref(false)

    // 查询参数
    const queryParams = reactive<PlaylistQueryParams>({
        name: '',
        style: '',
        pageNum: 1,
        pageSize: 10
    })

    // 获取歌单列表
    const getList = async () => {
        try {
            loading.value = true
            const res = await getPlaylistList(queryParams)
            playlistList.value = res.list
            total.value = res.total
        } catch (error) {
            console.error('获取歌单列表失败', error)
            ElMessage.error('获取歌单列表失败')
        } finally {
            loading.value = false
        }
    }

    // 重置查询条件
    const resetQuery = () => {
        queryParams.name = ''
        queryParams.style = ''
        queryParams.pageNum = 1
        getList()
    }

    // 添加歌单
    const addPlaylist = async (playlistData: Partial<PlaylistInfo>) => {
        try {
            await apiAddPlaylist(playlistData)
            ElMessage.success('添加歌单成功')
            await getList() // 刷新列表
            return true
        } catch (error) {
            console.error('添加歌单失败', error)
            ElMessage.error('添加歌单失败')
            return false
        }
    }

    // 更新歌单信息
    const updatePlaylist = async (playlistData: Partial<PlaylistInfo>) => {
        try {
            await apiUpdatePlaylist(playlistData)
            ElMessage.success('更新歌单成功')
            await getList() // 刷新列表
            return true
        } catch (error) {
            console.error('更新歌单失败', error)
            ElMessage.error('更新歌单失败')
            return false
        }
    }

    // 删除歌单
    const deletePlaylist = async (playlistId: number) => {
        try {
            await apiDeletePlaylist(playlistId)
            ElMessage.success('删除歌单成功')
            await getList() // 刷新列表
            return true
        } catch (error) {
            console.error('删除歌单失败', error)
            ElMessage.error('删除歌单失败')
            return false
        }
    }

    // 批量删除歌单
    const deleteBatchPlaylists = async (playlistIds: number[]) => {
        try {
            await batchDeletePlaylists(playlistIds)
            ElMessage.success('批量删除歌单成功')
            await getList() // 刷新列表
            return true
        } catch (error) {
            console.error('批量删除歌单失败', error)
            ElMessage.error('批量删除歌单失败')
            return false
        }
    }

    // 上传歌单封面
    const uploadPlaylistCover = async (playlistId: number, file: File) => {
        try {
            const res = await apiUploadPlaylistCover(playlistId, file)
            ElMessage.success('封面上传成功')
            await getList() // 刷新列表以显示新封面
            return res.url
        } catch (error) {
            console.error('封面上传失败', error)
            ElMessage.error('封面上传失败')
            return null
        }
    }

    // 获取歌单详情
    const getPlaylistDetail = async (playlistId: number) => {
        try {
            const res = await getPlaylistInfo(playlistId)
            currentPlaylist.value = res
            return res
        } catch (error) {
            console.error('获取歌单详情失败', error)
            ElMessage.error('获取歌单详情失败')
            return null
        }
    }

    // 获取歌单中的歌曲
    const getPlaylistSongList = async (playlistId: number, params: PlaylistSongQueryParams) => {
        try {
            const res = await getPlaylistSongs(playlistId, params)
            return res
        } catch (error) {
            console.error('获取歌单歌曲失败', error)
            ElMessage.error('获取歌单歌曲失败')
            return { list: [], total: 0 }
        }
    }

    // 向歌单添加歌曲
    const addSongsToPlaylistAction = async (playlistId: number, songIds: number[]) => {
        try {
            await addSongsToPlaylist(playlistId, songIds)
            ElMessage.success('添加歌曲成功')
            return true
        } catch (error) {
            console.error('添加歌曲失败', error)
            ElMessage.error('添加歌曲失败')
            return false
        }
    }

    // 从歌单移除歌曲
    const removeSongFromPlaylistAction = async (playlistId: number, songId: number) => {
        try {
            await removeSongFromPlaylist(playlistId, songId)
            ElMessage.success('移除歌曲成功')
            return true
        } catch (error) {
            console.error('移除歌曲失败', error)
            ElMessage.error('移除歌曲失败')
            return false
        }
    }

    // 批量从歌单移除歌曲
    const batchRemoveSongsFromPlaylistAction = async (playlistId: number, songIds: number[]) => {
        try {
            await batchRemoveSongsFromPlaylist(playlistId, songIds)
            ElMessage.success('批量移除歌曲成功')
            return true
        } catch (error) {
            console.error('批量移除歌曲失败', error)
            ElMessage.error('批量移除歌曲失败')
            return false
        }
    }

    // 设置当前选中的歌单
    const setCurrentPlaylist = (playlist: PlaylistInfo | null) => {
        currentPlaylist.value = playlist
    }

    return {
        // 状态
        playlistList,
        currentPlaylist,
        total,
        loading,
        queryParams,

        // 方法
        getList,
        resetQuery,
        addPlaylist,
        updatePlaylist,
        deletePlaylist,
        deleteBatchPlaylists,
        uploadPlaylistCover,
        getPlaylistDetail,
        getPlaylistSongList,
        addSongsToPlaylistAction,
        removeSongFromPlaylistAction,
        batchRemoveSongsFromPlaylistAction,
        setCurrentPlaylist,
    }
})
