package com.jpzz.service.admin.Impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jpzz.constant.*;
import com.jpzz.exception.SingerNotFoundException;
import com.jpzz.exception.UserExistedException;
import com.jpzz.exception.UserNotFoundException;
import com.jpzz.mapper.admin.UserMapper;
import com.jpzz.pojo.dto.admin.UserDTO;
import com.jpzz.pojo.dto.admin.UserQueryDTO;
import com.jpzz.pojo.entity.User;
import com.jpzz.pojo.vo.admin.UserVO;
import com.jpzz.result.PageResult;
import com.jpzz.service.admin.UserService;
import com.jpzz.utils.DateTimeUtils;
import com.jpzz.utils.PageUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    @Resource
    private UserMapper userMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "queryResult", allEntries = true)
    public Long addUser(UserDTO userDTO) {
        User existUser = this.lambdaQuery().eq(User::getUsername, userDTO.getUsername()).one();
        if (ObjectUtil.isNotNull(existUser)) {
            throw new UserExistedException(MessageConstant.USER_EXISTS);
        }

        User user = BeanUtil.copyProperties(userDTO, User.class);

        if (StrUtil.isBlank(userDTO.getPassword())) {
            user.setPassword(DigestUtils.md5DigestAsHex(PasswordConstant.DEFAULT_PASSWORD.getBytes()));
        } else {
            user.setPassword(DigestUtils.md5DigestAsHex(userDTO.getPassword().getBytes()));
        }

        if (StrUtil.isBlank(user.getStatus())) {
            user.setStatus(StatusConstant.ENABLE);
        }
        if (StrUtil.isBlank(user.getRole())) {
            user.setRole(UserRoleConstant.USER);
        }

        String password = DigestUtils.md5DigestAsHex(user.getPassword().getBytes());
        user.setPassword(password);
        this.save(user);
        return user.getUserId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = {"userInfo", "queryResult"}, allEntries = true)
    public void updateUser(UserDTO userDTO) {
        User existUser = userMapper.selectById(userDTO.getUserId());
        log.info("用户ID：{}", existUser);
        if (ObjectUtil.isNull(existUser)) {
            throw new UserNotFoundException(MessageConstant.USER_NOT_FOUND);
        }
        if (!StrUtil.equals(existUser.getUsername(), userDTO.getUsername())) {
            User user = this.lambdaQuery().eq(User::getUsername, userDTO.getUsername()).one();
            if (ObjectUtil.isNotNull(user)) {
                throw new UserExistedException(MessageConstant.USER_EXISTS);
            }
        }

        User user = BeanUtil.copyProperties(userDTO, User.class);

        // 如果密码为空或null，则不更新密码字段
        if (StrUtil.isBlank(userDTO.getPassword())) {
            user.setPassword(null);
        } else {
            // 如果提供了密码，则加密后更新
            user.setPassword(DigestUtils.md5DigestAsHex(userDTO.getPassword().getBytes()));
        }

        updateById(user);
        log.info("用户更新成功，用户ID：{}", user.getUserId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long userId) {
        User user = getById(userId);
        if (ObjectUtil.isNull(user)) {
            throw new UserNotFoundException(MessageConstant.USER_NOT_FOUND);
        }
        this.removeById(userId);
    }

    @Override
    public void deleteByIds(List<Long> userIds) {
        List<User> userList = this.listByIds(userIds);
        if (userList.size() != userIds.size()) {
            throw new SingerNotFoundException(MessageConstant.USER_NOT_FOUND);
        }

        this.removeByIds(userIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPassword(Long userId) {
        // 验证用户是否存在
        User user = getById(userId);
        if (ObjectUtil.isNull(user)) {
            throw new UserNotFoundException(MessageConstant.USER_NOT_FOUND);
        }
        // 重置密码
        User updateUser = new User();
        updateUser.setUserId(userId);
        updateUser.setPassword(DigestUtils.md5DigestAsHex(PasswordConstant.DEFAULT_PASSWORD.getBytes()));
        updateById(updateUser);
        log.info("重置用户密码成功：{}", user.getUsername());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeStatus(Long userId, String status) {
        // 验证用户是否存在
        User user = getById(userId);
        if (user == null) {
            throw new UserNotFoundException(MessageConstant.USER_NOT_FOUND);
        }
        // 修改状态
        User updateUser = new User();
        updateUser.setUserId(userId);
        updateUser.setStatus(status);
        updateById(updateUser);
        log.info("修改用户状态成功：{}，状态：{}", user.getUsername(), status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPasswordByUsername(String username, String newPassword) {


        User user = userMapper.selectByUserName(username);
        if (ObjectUtil.isNull(user)) {
           throw new UserNotFoundException(MessageConstant.USER_NOT_FOUND);
        }
        // 加密新密码并更新
        String encryptedPassword = DigestUtils.md5DigestAsHex(newPassword.getBytes());
        User updateUser = new User();
        updateUser.setUserId(user.getUserId());
        updateUser.setPassword(encryptedPassword);
        updateById(updateUser);


    }


    @Override
    @Cacheable(value = "queryResult", key = "'userList:' + #userQueryDTO.pageNum + ':' + #userQueryDTO.pageSize + ':' + #userQueryDTO.username + ':' + #userQueryDTO.status")
    public PageResult<UserVO> pageUserList(UserQueryDTO userQueryDTO) {

        LocalDateTime[] times = DateTimeUtils.parseTimeRange(userQueryDTO.getBeginTime(), userQueryDTO.getEndTime());
        LocalDateTime beginTime = times[0];
        LocalDateTime endTime = times[1];
        Page<User> p = Page.of(userQueryDTO.getPageNum(), userQueryDTO.getPageSize());
        Page<User> page = this.lambdaQuery()
                .like(StrUtil.isNotBlank(userQueryDTO.getUsername()), User::getUsername, StrUtil.trim(userQueryDTO.getUsername()))
                .like(StrUtil.isNotBlank(userQueryDTO.getPhone()), User::getPhone, StrUtil.trim(userQueryDTO.getPhone()))
                .eq(StrUtil.isNotBlank(userQueryDTO.getStatus()), User::getStatus, StrUtil.trim(userQueryDTO.getStatus()))
                .eq(StrUtil.isNotBlank(userQueryDTO.getRole()), User::getRole, StrUtil.trim(userQueryDTO.getRole()))
                .ge(StrUtil.isNotBlank(userQueryDTO.getBeginTime()), User::getCreateTime, beginTime)
                .le(StrUtil.isNotBlank(userQueryDTO.getEndTime()), User::getCreateTime, endTime)
                .page(p);

        return PageUtils.convertToPageResult(page, this::convertToUserVO, userQueryDTO.getPageNum(), userQueryDTO.getPageSize());

    }

    /**
     * 将User实体转换为UserVO
     *
     * @param user 用户实体
     * @return 用户VO
     */
    public UserVO convertToUserVO(User user) {
        UserVO userVO = new UserVO();
        BeanUtil.copyProperties(user, userVO);
        return userVO;
    }
}
