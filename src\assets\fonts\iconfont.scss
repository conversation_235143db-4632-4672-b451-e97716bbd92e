@font-face {
  font-family: 'iconfont'; /* Project id 4971909 */
  src:
    url('iconfont.woff2?t=1752332690290') format('woff2'),
    url('iconfont.woff?t=1752332690290') format('woff'),
    url('iconfont.ttf?t=1752332690290') format('truetype');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-login-music:before {
  content: '\e6a0';
  @apply text-[60px] text-blue-300;
}

.icon-columnSetting:before {
  content: '\e606';
}

.icon-add:before {
  content: '\e711';
}

.icon-export:before {
  content: '\e657';
}

.icon-search:before {
  content: '\e604';
}

.icon-download:before {
  content: '\e63e';
}

.icon-Home:before {
  content: '\e65d';
}

.icon-user:before {
  content: '\e605';
}

.icon-singer:before {
  content: '\e618';
}

.icon-userInfo:before {
  content: '\e612';
  @apply text-[53px] text-gray-600;
}

.icon-music:before {
  content: '\e603';
  @apply text-blue-300 text-3xl;
}

.icon-download:before {
  content: '\e63e';
}
.icon-columnSetting:before {
  content: '\e606';
}

.icon-feedback:before {
  content: '\e601';
}

.icon-playlist:before {
  content: '\e600';
}

.icon-song:before {
  content: '\e602';
}
