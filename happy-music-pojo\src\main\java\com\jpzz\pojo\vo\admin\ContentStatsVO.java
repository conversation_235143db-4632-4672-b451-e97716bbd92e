package com.jpzz.pojo.vo.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 内容统计数据DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "内容统计数据")
public class ContentStatsVO {

    @Schema(description = "月份")
    private String month;

    @Schema(description = "歌曲上传数")
    private Long songUploads;

    @Schema(description = "歌手注册数")
    private Long singerRegistrations;
}