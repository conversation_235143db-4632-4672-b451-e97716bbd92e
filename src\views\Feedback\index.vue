<template>
  <div class="min-h-screen relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-gradient-to-br from-orange-50 via-white to-red-50 pointer-events-none"></div>
    <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-r from-orange-400/10 to-red-400/10 rounded-full blur-3xl pointer-events-none"></div>

    <div class="relative z-10 p-6">
      <!-- 页面标题 -->
      <div class="mb-4">
        <div class="flex items-center space-x-3 mb-1">
          <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
            <Icon icon="mdi:message-alert" class="text-white text-lg" />
          </div>
          <h1 class="text-xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
            反馈管理
          </h1>
        </div>
        <p class="text-sm text-gray-600">管理用户反馈信息，及时处理用户建议和问题</p>
      </div>

      <!-- 搜索区域 -->
      <el-card class="search-card enhanced-glass-card mb-4" shadow="never">
        <template #header>
          <div class="flex items-center space-x-3 py-1">
            <div class="w-6 h-6 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
              <Icon icon="mdi:magnify" class="text-white text-sm" />
            </div>
            <div>
              <h3 class="text-base font-semibold text-gray-800">搜索筛选</h3>
              <p class="text-xs text-gray-500">快速查找反馈信息</p>
            </div>
          </div>
        </template>

        <el-form :model="feedbackStore.queryParams" @submit.prevent class="enhanced-search-form" label-width="60px" label-position="left">
          <el-row :gutter="16" class="items-end">
            <el-col :span="6">
              <el-form-item label="反馈内容：" class="enhanced-form-item" label-width="75px">
                <el-input
                  v-model="feedbackStore.queryParams.content"
                  placeholder="请输入反馈内容关键词"
                  clearable
                  @keyup.enter="handleSearch"
                  class="enhanced-input"
                  size="default"
                />
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="反馈类型：" class="enhanced-form-item" label-width="75">
                <el-select
                  v-model="feedbackStore.queryParams.type"
                  placeholder="请选择类型"
                  clearable
                  class="enhanced-select w-full"
                  size="default"
                >
                  <el-option
                    v-for="type in FeedbackTypes"
                    :key="type.value"
                    :label="type.label"
                    :value="type.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="处理状态：" class="enhanced-form-item" label-width="75">
                <el-select
                  v-model="feedbackStore.queryParams.status"
                  placeholder="请选择状态"
                  clearable
                  class="enhanced-select w-full"
                  size="default"
                >
                  <el-option
                    v-for="status in FeedbackStatuses"
                    :key="status.value"
                    :label="status.label"
                    :value="status.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="10">
              <el-form-item class="enhanced-form-item">
                <div class="flex items-center space-x-3">
                  <el-button type="primary" @click="handleSearch" class="enhanced-btn enhanced-btn-primary">
                    <Icon icon="mdi:magnify" class="mr-2" />
                    搜索
                  </el-button>
                  <el-button @click="handleReset" class="enhanced-btn enhanced-btn-reset">
                    <Icon icon="mdi:refresh" class="mr-2" />
                    重置
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 表格区域 -->
      <el-card class="table-card enhanced-glass-card" shadow="never">
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                <Icon icon="mdi:table-edit" class="text-white text-lg" />
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-800">反馈列表</h3>
                <p class="text-sm text-gray-500">共 {{ feedbackStore.total }} 条记录</p>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <el-button
                type="danger"
                :disabled="selectedFeedbacks.length === 0"
                @click="handleBatchDelete"
                class="enhanced-btn"
              >
                <el-icon><Delete/></el-icon>
                批量删除 ({{ selectedFeedbacks.length }})
              </el-button>

              <el-button type="success" @click="handleRefresh" class="enhanced-btn">
                <Icon icon="mdi:refresh" class="mr-2" />
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <!-- 表格 -->
        <el-table
          v-loading="feedbackStore.loading"
          :data="feedbackStore.feedbackList"
          @selection-change="handleSelectionChange"
          highlight-current-row
          row-key="id"
          class="enhanced-table"
          stripe
        >
          <el-table-column type="selection" width="50" align="center" fixed="left" />
          <el-table-column label="反馈编号" prop="id" align="center" width="100" />
          <el-table-column label="用户编号" prop="userId" align="center" width="100" />
          <el-table-column label="用户名" prop="username" align="center" width="120" show-overflow-tooltip />
          <el-table-column label="反馈内容" prop="content" align="center" min-width="200" show-overflow-tooltip />
          <el-table-column label="反馈类型" prop="type" align="center" width="120">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.type)" size="small" effect="light" class="enhanced-tag">
                <Icon :icon="getTypeIcon(row.type)" class="mr-1" />
                {{ getTypeLabel(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="处理状态" prop="status" align="center" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small" effect="light" class="enhanced-tag">
                <Icon :icon="getStatusIcon(row.status)" class="mr-1" />
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="反馈时间" prop="createTime" align="center" width="160" />
          <el-table-column label="操作" align="center" width="200" fixed="right">
            <template #default="{ row }">
              <div class="flex items-center justify-start space-x-2">
                <el-button type="primary" size="small" @click="handleView(row)" class="enhanced-btn-small enhanced-btn-primary">
                  <Icon icon="mdi:eye" class="mr-1" />
                  查看
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  @click="handleReply(row)"
                  v-if="row.status !== 'RESOLVED' && row.status !== 'CLOSED'"
                  class="enhanced-btn-small"
                >
                  <Icon icon="mdi:reply" class="mr-1" />
                  回复
                </el-button>
                <el-dropdown @command="(command) => handleMoreCommand(command, row)">
                  <el-button type="info" size="small" class="enhanced-btn-small">
                    <Icon icon="mdi:dots-horizontal" />
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="updateStatus">
                        <Icon icon="mdi:pencil" class="mr-2" /> 更新状态
                      </el-dropdown-item>
                      <el-dropdown-item command="delete" divided>
                        <Icon icon="mdi:delete" class="mr-2" /> 删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="flex justify-center mt-6">
          <el-pagination
            v-model:current-page="feedbackStore.queryParams.pageNum"
            v-model:page-size="feedbackStore.queryParams.pageSize"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="feedbackStore.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
            class="enhanced-pagination"
          />
        </div>
      </el-card>
    </div>

    <!-- 反馈详情对话框 -->
    <FeedbackDetailDialog
      v-model="showDetailDialog"
      :feedback-data="currentFeedback"
      @success="handleDialogSuccess"
    />

    <!-- 回复对话框 -->
    <FeedbackReplyDialog
      v-model="showReplyDialog"
      :feedback-data="currentFeedback"
      @success="handleDialogSuccess"
    />

    <!-- 状态更新对话框 -->
    <FeedbackStatusDialog
      v-model="showStatusDialog"
      :feedback-data="currentFeedback"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useFeedbackStore } from '@/stores/feedback'
import { FeedbackTypes, FeedbackStatuses, FeedbackType, FeedbackStatus } from '@/types/feedback'
import type { FeedbackInfo } from '@/types/feedback'
import { ElMessageBox, ElMessage } from 'element-plus'
import {Delete} from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import FeedbackDetailDialog from './components/FeedbackDetailDialog.vue'
import FeedbackReplyDialog from './components/FeedbackReplyDialog.vue'
import FeedbackStatusDialog from './components/FeedbackStatusDialog.vue'

defineOptions({
  name: 'FeedbackView'
})

// Store
const feedbackStore = useFeedbackStore()

// 状态
const selectedFeedbacks = ref<FeedbackInfo[]>([])
const currentFeedback = ref<FeedbackInfo | null>(null)
const showDetailDialog = ref(false)
const showReplyDialog = ref(false)
const showStatusDialog = ref(false)

// 获取类型标签样式
const getTypeTagType = (type: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeMap: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    [FeedbackType.BUG]: 'danger',
    [FeedbackType.FEATURE]: 'primary',
    [FeedbackType.COMPLAINT]: 'warning',
    [FeedbackType.SUGGESTION]: 'success',
    [FeedbackType.OTHER]: 'info'
  }
  return typeMap[type] || 'info'
}

// 获取类型标签文本
const getTypeLabel = (type: string) => {
  const typeItem = FeedbackTypes.find(item => item.value === type)
  return typeItem?.label || type
}

// 获取类型图标
const getTypeIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    [FeedbackType.BUG]: 'mdi:bug',
    [FeedbackType.FEATURE]: 'mdi:lightbulb',
    [FeedbackType.COMPLAINT]: 'mdi:alert-circle',
    [FeedbackType.SUGGESTION]: 'mdi:comment-text',
    [FeedbackType.OTHER]: 'mdi:help-circle'
  }
  return iconMap[type] || 'mdi:help-circle'
}

// 获取状态标签样式
const getStatusTagType = (status: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const statusMap: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    [FeedbackStatus.PENDING]: 'warning',
    [FeedbackStatus.PROCESSING]: 'primary',
    [FeedbackStatus.RESOLVED]: 'success',
    [FeedbackStatus.CLOSED]: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态标签文本
const getStatusLabel = (status: string) => {
  const statusItem = FeedbackStatuses.find(item => item.value === status)
  return statusItem?.label || status
}

// 获取状态图标
const getStatusIcon = (status: string) => {
  const iconMap: Record<string, string> = {
    [FeedbackStatus.PENDING]: 'mdi:clock-outline',
    [FeedbackStatus.PROCESSING]: 'mdi:cog',
    [FeedbackStatus.RESOLVED]: 'mdi:check-circle',
    [FeedbackStatus.CLOSED]: 'mdi:close-circle'
  }
  return iconMap[status] || 'mdi:help-circle'
}

// 搜索
const handleSearch = () => {
  feedbackStore.queryParams.pageNum = 1
  feedbackStore.getList()
}

// 重置
const handleReset = () => {
  feedbackStore.resetQuery()
}

// 刷新
const handleRefresh = () => {
  feedbackStore.getList()
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  feedbackStore.queryParams.pageSize = val
  feedbackStore.getList()
}

// 页码变化
const handleCurrentChange = (val: number) => {
  feedbackStore.queryParams.pageNum = val
  feedbackStore.getList()
}

// 选择变化
const handleSelectionChange = (selection: FeedbackInfo[]) => {
  selectedFeedbacks.value = selection
}

// 查看详情
const handleView = (feedback: FeedbackInfo) => {
  currentFeedback.value = feedback
  showDetailDialog.value = true
}

// 回复反馈
const handleReply = (feedback: FeedbackInfo) => {
  currentFeedback.value = feedback
  showReplyDialog.value = true
}

// 更多操作
const handleMoreCommand = (command: string, feedback: FeedbackInfo) => {
  currentFeedback.value = feedback
  switch (command) {
    case 'updateStatus':
      showStatusDialog.value = true
      break
    case 'delete':
      handleDelete(feedback)
      break
  }
}

// 删除反馈
const handleDelete = async (feedback: FeedbackInfo) => {
  try {
    await ElMessageBox.confirm(`确认删除反馈【${feedback.id}】吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await feedbackStore.deleteFeedbackAction(feedback.id)
  } catch (error) {
    console.log(error)
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedFeedbacks.value.length === 0) {
    ElMessage.warning('请至少选择一条反馈')
    return
  }

  const feedbackIds = selectedFeedbacks.value.map(item => item.id)

  try {
    await ElMessageBox.confirm(`确认删除选中的 ${selectedFeedbacks.value.length} 条反馈吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await feedbackStore.batchDelete(feedbackIds)
    selectedFeedbacks.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败', error)
    }
  }
}

// 对话框成功回调
const handleDialogSuccess = () => {
  showDetailDialog.value = false
  showReplyDialog.value = false
  showStatusDialog.value = false
  feedbackStore.getList()
}

// 初始化
onMounted(() => {
  feedbackStore.getList()
})
</script>

<style scoped>
/* 🎵 反馈管理页面增强样式 */

/* 搜索表单样式 */
.enhanced-search-form {
  background: transparent;
}

.search-card {
  border-radius: 12px;
}

.search-card :deep(.el-card__header) {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.search-card :deep(.el-card__body) {
  padding: 16px 20px;
}

.enhanced-form-item :deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0 !important;
  line-height: 32px; /* 与输入框高度对齐 */
  display: flex;
  align-items: center;
  font-size: 14px;
  height: 32px; /* 固定标签高度 */
  padding-right: 4px; /* 减少右侧间距 */
}

.enhanced-form-item {
  margin-bottom: 16px !important;
  align-items: center;
  display: flex;
}

.enhanced-form-item :deep(.el-form-item__content) {
  line-height: 32px;
  min-height: 32px; /* 确保内容区域最小高度 */
  display: flex;
  align-items: center;
  flex: 1;
}

/* 确保输入框和选择框的高度一致 */
.enhanced-input :deep(.el-input__inner),
.enhanced-select :deep(.el-input__inner) {
  height: 32px !important;
  line-height: 32px !important;
}

/* 日期选择器对齐 */
.enhanced-date-picker :deep(.el-input__inner) {
  height: 32px !important;
  line-height: 32px !important;
}

/* 选择框下拉箭头对齐 */
.enhanced-select :deep(.el-input__suffix) {
  height: 32px !important;
  display: flex;
  align-items: center;
}

/* 选择框选项样式优化 */
.enhanced-select :deep(.el-select-dropdown__item) {
  padding: 8px 12px;
  min-height: 36px;
  display: flex;
  align-items: center;
}

/* 确保选择框有足够的宽度显示选项文字 */
.enhanced-select {
  min-width: 120px;
}

.enhanced-input :deep(.el-input__wrapper) {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.enhanced-input :deep(.el-input__wrapper):hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.enhanced-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.enhanced-select :deep(.el-select__wrapper) {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.enhanced-select :deep(.el-select__wrapper):hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}
/* 玻璃态卡片样式 */
.enhanced-glass-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.enhanced-glass-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.95);
}

/* 按钮样式 */
.enhanced-btn {
  border-radius: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.enhanced-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.enhanced-btn-primary {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  color: white !important;
}

.enhanced-btn-reset {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white !important;
}

.enhanced-btn-small {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 4px 8px;
  font-size: 12px;
}

/* 表格样式 */
.enhanced-table {
  border-radius: 12px;
  overflow: hidden;
}

.enhanced-table :deep(.el-table__header) {
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
}

.enhanced-table :deep(.el-table__row:hover) {
  background-color: rgba(249, 115, 22, 0.05) !important;
}

/* 标签样式 */
.enhanced-tag {
  border-radius: 8px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 分页样式 */
.enhanced-pagination :deep(.el-pager li) {
  border-radius: 8px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.enhanced-pagination :deep(.el-pager li:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(249, 115, 22, 0.2);
}

.enhanced-pagination :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
  color: white;
  font-weight: 600;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
}

.action-buttons :deep(.el-dropdown .el-button) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

</style>
