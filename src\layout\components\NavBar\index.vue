<template>
  <header class="app-header">
    <div class="header-left">
      <span>{{ ContentTitle }}</span>
    </div>
    <div class="header-right">
      <el-input class="search-input" placeholder="搜索..." :prefix-icon="Search" />
      <el-tooltip content="全屏">
        <ElButton class="btn-icon" @click="toggleFullscreen">
          <el-icon size="20">
            <FullScreen v-if="!isFullscreen" />
            <Aim v-else />
          </el-icon>
        </ElButton>
      </el-tooltip>
      <el-dropdown trigger="click">
        <div class="user-info">
          <!-- 后期需要修改，需上传头像 -->
          <el-avatar :size="40">
            <span class="iconfont icon-userInfo"></span>
          </el-avatar>
          <span class="username">{{ userStore.userInfo?.username || '未登录' }}</span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>个人信息</el-dropdown-item>
            <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-tooltip content="设置">
        <el-button class="btn-icon">
          <el-icon size="20"><Setting /></el-icon>
        </el-button>
      </el-tooltip>
    </div>
  </header>
</template>

<script setup lang="ts">
defineOptions({
  name: 'NavBar',
})
import { FullScreen, Aim, Setting, Search } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { useRoute } from 'vue-router'

const isFullscreen = ref(false)
const userStore = useUserStore()

const route = useRoute()

onMounted(async () => {
  const token = localStorage.getItem('token')
  if (!userStore.userInfo && token) {
    await userStore.getUserInfo()
  }
})

const ContentTitle = computed(() => {
  const routeNameMap: Record<string, string> = {
    home: '控制台',
    user: '用户管理',
    singer: '歌手管理',
    song: '歌曲管理',
    playlist: '歌单管理',
    feedback: '反馈管理'
  }
  return routeNameMap[route.name as keyof typeof routeNameMap] || '首页'
})

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
      isFullscreen.value = false
    }
  }
}
// 退出登录
const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        await userStore.logout()
      } catch (error) {
        console.error('注销失败:', error)
        ElMessage.error('注销失败，请重试')
      }
    })
    .catch(() => {
      // 用户取消操作，不做任何处理
    })
}
</script>

<style scoped>
.app-header {
  @apply h-[60px] bg-white px-5 flex items-center justify-between border-b border-[#ebeef5];
}
.header-left {
  @apply text-lg font-medium;
}
.header-right {
  @apply flex items-center gap-4;
}
.search-input {
  @apply w-[200px] mr-4;
}
.btn-icon {
  @apply p-2 border-none;
}
.user-info {
  @apply flex items-center gap-2 cursor-pointer;
}
.username {
  @apply hidden sm:inline text-xl;
}
</style>

