package com.jpzz.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 歌单实体类
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_playlist")
@Schema(description = "歌单实体")
public class Playlist implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "歌单ID")
    private Long id;

    @Schema(description = "歌单名称")
    private String name;

    @Schema(description = "歌单描述")
    private String description;

    @Schema(description = "歌单风格")
    private String style;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "歌曲数量")
    private Integer songCount;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @TableLogic
    @Schema(description = "逻辑删除标记(0-未删除,1-已删除)", hidden = true)
    private Integer deleted;
}