import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '@/layout/index.vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import { createAsyncComponent, setupIntelligentPreload } from '@/utils/routeLoader'
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/Login/index.vue'),
      meta: {
        title: '登录',
        requiresAuth: false
      }
    },
    {
      path: '/',
      component: MainLayout,
      children: [
        {
          path: '',
          name: 'home',
          component: createAsyncComponent(() => import('@/views/Dashboard/index.vue')),
          meta: {
            title: '首页',
            requiresAuth: true
          }
        },
        {
          path: '/user',
          name: 'user',
          component: createAsyncComponent(() => import('@/views/User/index.vue')),
          meta: {
            title: '用户管理',
            requiresAuth: true
          }
        },
        {
          path: '/singer',
          name: 'singer',
          component: createAsyncComponent(() => import('@/views/Singer/index.vue')),
          meta: {
            title: '歌手管理',
            requiresAuth: true
          }
        },
        {
          path: '/song',
          name: 'song',
          component: createAsyncComponent(() => import('@/views/Song/index.vue')),
          meta: {
            title: '歌曲管理',
            requiresAuth: true
          }
        },
        {
          path: '/playlist',
          name: 'playlist',
          component: createAsyncComponent(() => import('@/views/Playlist/index.vue')),
          meta: { title: '歌单管理', icon: 'playlist', requiresAuth: true }
        },
        {
          path: '/playlist/edit/:id',
          name: 'PlaylistEdit',
          component: () => import('@/views/Playlist/components/edit.vue'),
          meta: { title: '编辑歌单', icon: 'edit', requiresAuth: true }
        },
        {
          path: '/feedback',
          name: 'feedback',
          component: () => import('@/views/Feedback/index.vue'),
          meta: {
            title: '反馈管理',
            requiresAuth: true
          }
        },
      ],
    },
  ],
})

router.beforeEach(async (to, from, next) => {

  document.title = `${to.meta.title as string || '默认标题'} - HappyMusic`

  const userStore = useUserStore()
  const hasToken = localStorage.getItem('token')

  // 如果路由需要身份验证
  if (to.meta.requiresAuth) {
    // 如果有token
    if (hasToken) {
      // 确保有用户信息，如果没有则重新获取
      if (!userStore.userInfo) {
        try {
          await userStore.getUserInfo()
          next()
        } catch (error) {
          console.log(error)
          // 获取用户信息失败，可能是token失效
          userStore.logout()
          ElMessage.error('登录状态已过期，请重新登录')
          next(`/login?redirect=${to.path}`)
        }
      } else {
        next()
      }
    } else {
      // 无token，跳转到登录页面
      ElMessage.warning('请先登录')
      next(`/login?redirect=${to.path}`)
    }
  } else {
    // 不需要身份验证的路由
    if (to.path === '/login' && hasToken) {
      // 如果已登录，访问登录页会重定向到首页
      next('/')
    } else {
      next()
    }
  }
})

// 初始化智能预加载
setupIntelligentPreload()

export default router





