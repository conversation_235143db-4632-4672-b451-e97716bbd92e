<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jpzz.mapper.client.UserBehaviorMapper">

    <!-- 插入播放记录 -->
    <insert id="insertPlayRecord" parameterType="com.jpzz.pojo.entity.PlayRecord">
        INSERT INTO tb_play_record (
            user_id, song_id, play_duration, play_progress,
            device_type, region_code, create_time
        ) VALUES (
                     #{userId}, #{songId}, #{playDuration}, #{playProgress},
                     #{deviceType}, #{regionCode}, #{createTime}
                 )
    </insert>

    <!-- 插入或更新最近播放 -->
    <insert id="insertOrUpdateRecentPlay" parameterType="com.jpzz.pojo.entity.RecentPlay">
        INSERT INTO tb_recent_play (user_id, song_id, play_time, update_time)
        VALUES (#{userId}, #{songId}, #{playTime}, #{updateTime})
        ON DUPLICATE KEY UPDATE
                             play_time = #{playTime},
                             update_time = #{updateTime}
    </insert>

    <!-- 插入用户喜欢记录 -->
    <insert id="insertUserLike" parameterType="com.jpzz.pojo.entity.UserLike">
        INSERT IGNORE INTO tb_user_like (user_id, target_id, target_type, create_time)
        VALUES (#{userId}, #{targetId}, #{targetType}, #{createTime})
    </insert>

    <!-- 删除用户喜欢记录 -->
    <delete id="deleteUserLike">
        DELETE FROM tb_user_like
        WHERE user_id = #{userId} AND target_id = #{targetId} AND target_type = #{targetType}
    </delete>

    <!-- 插入用户收藏记录 -->
    <insert id="insertUserCollection" parameterType="com.jpzz.pojo.entity.UserCollection">
        INSERT IGNORE INTO tb_user_collection (user_id, target_id, target_type, folder_name, create_time)
        VALUES (#{userId}, #{targetId}, #{targetType}, #{folderName}, #{createTime})
    </insert>

    <!-- 删除用户收藏记录 -->
    <delete id="deleteUserCollection">
        DELETE FROM tb_user_collection
        WHERE user_id = #{userId} AND target_id = #{targetId} AND target_type = #{targetType}
    </delete>

    <!-- 更新歌曲喜欢次数 -->
    <update id="updateSongLikeCount">
        UPDATE tb_song
        SET like_count = GREATEST(0, like_count + #{increment})
        WHERE id = #{songId}
    </update>

    <!-- 更新歌曲播放次数 -->
    <update id="updateSongPlayCount">
        UPDATE tb_song
        SET play_count = play_count + #{increment}
        WHERE id = #{songId}
    </update>

    <!-- 更新歌单喜欢次数 -->
    <update id="updatePlaylistLikeCount">
        UPDATE tb_playlist
        SET like_count = GREATEST(0, like_count + #{increment})
        WHERE id = #{playlistId}
    </update>

    <!-- 更新歌单播放次数 -->
    <update id="updatePlaylistPlayCount">
        UPDATE tb_playlist
        SET play_count = play_count + #{increment}
        WHERE id = #{playlistId}
    </update>

    <!-- 获取用户播放总时长 -->
    <select id="getUserTotalPlayDuration" resultType="java.lang.Long">
        SELECT COALESCE(SUM(play_duration), 0)
        FROM tb_play_record
        WHERE user_id = #{userId}
    </select>

    <!-- 获取用户播放歌曲总数 -->
    <select id="getUserPlaySongCount" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT song_id)
        FROM tb_play_record
        WHERE user_id = #{userId}
    </select>

    <!-- 获取用户最常听的风格 -->
    <select id="getUserPreferredStyles" resultType="java.lang.String">
        SELECT s.style
        FROM tb_play_record pr
                 JOIN tb_song s ON pr.song_id = s.id
        WHERE pr.user_id = #{userId} AND s.style IS NOT NULL
        GROUP BY s.style
        ORDER BY COUNT(*) DESC
        LIMIT #{limit}
    </select>

    <!-- 获取用户最常听的歌手 -->
    <select id="getUserPreferredSingers" resultType="java.lang.Long">
        SELECT s.artist_id
        FROM tb_play_record pr
                 JOIN tb_song s ON pr.song_id = s.id
        WHERE pr.user_id = #{userId}
        GROUP BY s.artist_id
        ORDER BY COUNT(*) DESC
        LIMIT #{limit}
    </select>

    <!-- 清理用户最近播放记录(保留最新的N条) -->
    <delete id="cleanUserRecentPlays">
        DELETE FROM tb_recent_play
        WHERE user_id = #{userId}
          AND id NOT IN (
            SELECT id FROM (
                               SELECT id
                               FROM tb_recent_play
                               WHERE user_id = #{userId}
                               ORDER BY update_time DESC
                               LIMIT #{keepCount}
                           ) AS temp
        )
    </delete>

</mapper>
