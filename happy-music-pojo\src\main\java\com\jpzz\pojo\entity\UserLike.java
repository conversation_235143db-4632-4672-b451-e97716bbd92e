package com.jpzz.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户喜欢实体
 * <AUTHOR>
 */
@Data
@TableName("tb_user_like")
public class UserLike {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 目标ID(歌曲/歌单/歌手)
     */
    private Long targetId;

    /**
     * 目标类型(SONG/PLAYLIST/SINGER)
     */
    private String targetType;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
