<template>
  <div class="relative min-h-screen overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-gradient-to-br from-purple-100 via-white to-blue-100 pointer-events-none"></div>
    <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl pointer-events-none"></div>

    <div class="relative z-10 p-6">
      <!-- 页面标题 -->
      <div class="mb-4">
        <div class="flex items-center space-x-3 mb-1">
          <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
            <Icon icon="mdi:account-group" class="text-white text-lg" />
          </div>
          <h1 class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            用户管理
          </h1>
        </div>
        <p class="text-sm text-gray-600">管理系统用户信息，包括用户权限、状态等</p>
      </div>

      <!-- 搜索区域 -->
      <el-card class="search-card enhanced-glass-card mb-4" shadow="never">
        <template #header>
          <div class="flex items-center space-x-3 py-1">
            <div class="w-6 h-6 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
              <Icon icon="mdi:magnify" class="text-white text-sm" />
            </div>
            <div>
              <h3 class="text-base font-semibold text-gray-800">搜索筛选</h3>
              <p class="text-xs text-gray-500">快速查找用户信息</p>
            </div>
          </div>
        </template>

        <el-form :model="queryParams" @submit.prevent class="enhanced-search-form" label-width="60px" label-position="left">
          <el-row :gutter="16" class="items-end">
            <el-col :span="5">
              <el-form-item label="用户名：" class="enhanced-form-item">
                <el-input
                  v-model="queryParams.username"
                  placeholder="请输入用户名"
                  clearable
                  @keyup.enter="handleQuery"
                  class="enhanced-input"
                  :prefix-icon="User"
                  size="default"
                />
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="手机号：" class="enhanced-form-item">
                <el-input
                  v-model="queryParams.phone"
                  placeholder="请输入手机号码"
                  clearable
                  @keyup.enter="handleQuery"
                  class="enhanced-input"
                  :prefix-icon="Phone"
                  size="default"
                />
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="状态：" class="enhanced-form-item" label-width="50">
                <el-select
                  v-model="queryParams.status"
                  placeholder="请选择状态"
                  clearable
                  class="enhanced-select w-full"
                  size="default"
                >
                  <el-option label="启用" value="1">
                    <div class="flex items-center">
                      <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                      启用
                    </div>
                  </el-option>
                  <el-option label="禁用" value="0">
                    <div class="flex items-center">
                      <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                      禁用
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="角色：" class="enhanced-form-item" label-width="50">
                <el-select
                  v-model="queryParams.role"
                  placeholder="请选择角色"
                  clearable
                  class="enhanced-select w-full"
                  size="default"
                >
                  <el-option label="管理员" :value="UserRoles.ADMIN">
                    <div class="flex items-center">
                      <Icon icon="mdi:shield-crown" class="text-orange-500 mr-2" />
                      管理员
                    </div>
                  </el-option>
                  <el-option label="普通用户" :value="UserRoles.USER">
                    <div class="flex items-center">
                      <Icon icon="mdi:account" class="text-blue-500 mr-2" />
                      普通用户
                    </div>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="创建时间" class="enhanced-form-item">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  :shortcuts="dateShortcuts"
                  class="enhanced-date-picker w-full"
                  size="default"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 搜索按钮区域 -->
          <el-row class="mt-2">
            <el-col :span="24" class="text-right">
              <el-button type="primary" @click="handleQuery" class="enhanced-btn enhanced-btn-primary">
                <Icon icon="mdi:magnify" class="mr-2" />
                搜索
              </el-button>
              <el-button @click="handleReset" class="enhanced-btn enhanced-btn-reset">
                <Icon icon="mdi:refresh" class="mr-2" />
                重置
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 操作按钮区域 -->
      <el-card class="table-card enhanced-glass-card" shadow="never">
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <Icon icon="mdi:table-edit" class="text-white text-lg" />
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-800">用户列表</h3>
                <p class="text-sm text-gray-500">共 {{ total }} 条记录</p>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <!-- 主要操作按钮 -->
              <el-button type="primary" @click="handleAdd" v-if="hasPermission('user:add')" class="enhanced-btn">
                <Icon icon="mdi:account-plus" class="mr-2" />
                新增用户
              </el-button>

              <el-button
                type="danger"
                :disabled="selectedUsers.length === 0"
                @click="handleBatchDelete"
                v-if="hasPermission('user:delete')"
                class="enhanced-btn"
              >
                <el-icon><Delete/></el-icon>
                批量删除 ({{ selectedUsers.length }})
              </el-button>

              <el-button type="success" @click="handleExport" class="enhanced-btn">
                <Icon icon="mdi:file-export" class="mr-2" />
                导出
              </el-button>

              <!-- 更多操作下拉菜单 -->
              <el-dropdown @command="handleCommand" class="enhanced-dropdown">
                <el-button class="enhanced-btn enhanced-btn-secondary">
                  <Icon icon="mdi:dots-horizontal" class="mr-2" />
                  更多操作
                  <Icon icon="mdi:chevron-down" class="ml-1" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu class="enhanced-dropdown-menu">
                    <el-dropdown-item command="importUsers" class="enhanced-dropdown-item">
                      <Icon icon="mdi:file-import" class="mr-2" />
                      导入用户
                    </el-dropdown-item>
                    <el-dropdown-item command="printTable" class="enhanced-dropdown-item">
                      <Icon icon="mdi:printer" class="mr-2" />
                      打印表格
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>

              <!-- 工具按钮 -->
              <div class="flex items-center space-x-1 ml-2 pl-2 border-l border-gray-200">
                <el-tooltip content="刷新数据" placement="top">
                  <el-button text @click="getList" class="enhanced-tool-btn">
                    <Icon icon="mdi:refresh" class="text-lg" />
                  </el-button>
                </el-tooltip>

                <el-tooltip content="列设置" placement="top">
                  <el-button text @click="showColumnSetting = true" class="enhanced-tool-btn">
                    <Icon icon="mdi:view-column" class="text-lg" />
                  </el-button>
                </el-tooltip>

                <el-tooltip content="全屏显示" placement="top">
                  <el-button text class="enhanced-tool-btn">
                    <Icon icon="mdi:fullscreen" class="text-lg" />
                  </el-button>
                </el-tooltip>
              </div>
            </div>
          </div>
        </template>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="userList"

        @selection-change="handleSelectionChange"
        highlight-current-row
        row-key="userId"
      >
        <el-table-column type="selection" width="50" align="center" fixed="left" />
        <el-table-column type="index" label="序号" width="60" align="center" fixed="left" />
        <el-table-column
          label="用户编号"
          prop="userId"
          align="center"
          width="85"
          v-if="columns.userId.visible"
        />
        <el-table-column label="用户头像" align="center" width="85" v-if="columns.avatar.visible">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar || defaultAvatar">
              <el-icon><UserFilled /></el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column
          label="用户名"
          prop="username"
          align="center"
          min-width="90"
          show-overflow-tooltip
          v-if="columns.username.visible"
        />
        <el-table-column
          label="手机号码"
          prop="phone"
          align="center"
          min-width="120"
          show-overflow-tooltip
          v-if="columns.phone.visible"
        />
        <el-table-column
          label="邮箱"
          prop="email"
          align="center"
          min-width="180"
          show-overflow-tooltip
          v-if="columns.email.visible"
        />
        <el-table-column label="角色" align="center" width="90" v-if="columns.role.visible">
          <template #default="{ row }">
            <el-tag :type="row.role === UserRoles.ADMIN ? 'danger' : 'info'" effect="plain" class="w-[60px]">
              {{ row.role === UserRoles.ADMIN ? '管理员' : '普通用户' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="简介"
          prop="introduction"
          align="center"
          min-width="120"
          show-overflow-tooltip
          v-if="columns.introduction.visible"
        />
        <el-table-column label="状态" align="center" width="100" v-if="columns.status.visible">
          <template #default="{ row }">
            <div class="flex items-center justify-center">
              <el-switch
                v-model="row.status"
                :active-value="'1'"
                :inactive-value="'0'"
                @change="handleStatusChange(row)"
                :disabled="!hasPermission('user:edit') || isUpdatingStatus(row.userId)"
                :loading="isUpdatingStatus(row.userId)"
                class="enhanced-switch"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          prop="createTime"
          align="center"
          min-width="160"
          sortable
          v-if="columns.createTime.visible"
        />
        <el-table-column
          label="修改时间"
          prop="updateTime"
          align="center"
          min-width="160"
          sortable
          v-if="columns.updateTime.visible"
        />
        <el-table-column label="操作" align="center" width="220" fixed="right">
          <template #default="{ row }">
           <div class="flex items-center justify-center space-x-2">
            <el-button
              type="primary"
              @click="handleEdit(row)"
              v-if="hasPermission('user:edit')"
              size="small"
              class="enhanced-btn-small enhanced-btn-primary"
            >
              <el-icon><Edit /></el-icon> 编辑
            </el-button>
            <el-button
              type="danger"
              @click="handleDelete(row)"
              v-if="hasPermission('user:delete')"
              class="enhanced-btn-small"
              size="small"
            >
              <el-icon><Delete /></el-icon> 删除
            </el-button>
            <el-button class="enhanced-btn-small" size="small"
  type="warning" @click="handleResetPassword(row)">
              <el-icon><Key /></el-icon> 重置密码
            </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="flex justify-center mt-6">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
          class="enhanced-pagination"
        />
      </div>
    </el-card>

    <!-- 用户表单对话框 -->
    <user-dialog
      :dialog-props="dialogProps"
      @update:visible="dialogProps.visible = $event"
      @success="handleDialogSuccess"
    />

    <!-- 重置密码确认框 -->
    <el-dialog v-model="resetPwdDialog.visible" title="重置密码" width="400px" append-to-body>
      <p>确定重置该用户密码吗？重置后密码将变为默认密码。</p>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetPwdDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmResetPassword">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入用户对话框 -->
    <el-dialog v-model="importDialog.visible" title="导入用户" width="400px" append-to-body>
      <el-upload
        class="upload-demo"
        :action="importDialog.uploadUrl"
        :headers="importDialog.headers"
        :on-success="handleImportSuccess"
        :on-error="handleImportError"
        accept=".xlsx, .xls"
      >
        <el-button type="primary">点击上传</el-button>
        <template #tip>
          <div class="el-upload__tip">请上传Excel文件，文件大小不超过2MB</div>
        </template>
      </el-upload>
      <div class="mt-3">
        <el-button type="text" @click="handleDownloadTemplate">
          <el-icon><Download /></el-icon> 下载导入模板
        </el-button>
      </div>
    </el-dialog>

    <!-- 列设置抽屉 -->
    <el-drawer v-model="showColumnSetting" title="列设置" direction="rtl" size="300px">
      <el-divider>表格显示列</el-divider>
      <el-checkbox-group v-model="checkedColumns">
        <div v-for="(col, key) in columns" :key="key" class="column-item">
          <el-checkbox :label="key">{{ col.label }}</el-checkbox>
        </div>
      </el-checkbox-group>
      <template #footer>
        <div class="drawer-footer">
          <el-button @click="resetColumns">重置</el-button>
          <el-button type="primary" @click="saveColumns">确定</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 开发环境下的状态变更调试面板 -->
    <el-drawer
      v-if="isDev"
      v-model="showDebugPanel"
      title="状态变更调试面板"
      direction="rtl"
      size="400px"
    >
      <div class="debug-panel">
        <div class="mb-4">
          <h4 class="text-lg font-semibold mb-2">当前更新中的用户</h4>
          <div v-if="updatingStatusUserIds.size === 0" class="text-gray-500">
            暂无用户正在更新状态
          </div>
          <div v-else>
            <el-tag v-for="userId in Array.from(updatingStatusUserIds)" :key="userId" class="mr-2 mb-2">
              用户ID: {{ userId }}
            </el-tag>
          </div>
        </div>

        <div class="mb-4">
          <h4 class="text-lg font-semibold mb-2">状态变更日志</h4>
          <div class="max-h-96 overflow-y-auto">
            <div v-if="statusChangeLog.length === 0" class="text-gray-500">
              暂无状态变更记录
            </div>
            <div v-else class="space-y-2">
              <div
                v-for="log in statusChangeLog"
                :key="`${log.userId}-${log.timestamp}`"
                class="p-3 border rounded-lg"
                :class="log.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'"
              >
                <div class="flex items-center justify-between mb-1">
                  <span class="font-medium">{{ log.username }}</span>
                  <span class="text-xs text-gray-500">{{ log.timestamp }}</span>
                </div>
                <div class="text-sm">
                  <span>状态变更: </span>
                  <el-tag size="small" :type="log.oldStatus === '1' ? 'success' : 'danger'">
                    {{ log.oldStatus === '1' ? '启用' : '禁用' }}
                  </el-tag>
                  <span class="mx-2">→</span>
                  <el-tag size="small" :type="log.newStatus === '1' ? 'success' : 'danger'">
                    {{ log.newStatus === '1' ? '启用' : '禁用' }}
                  </el-tag>
                </div>
                <div class="text-xs mt-1">
                  <el-tag size="small" :type="log.success ? 'success' : 'danger'">
                    {{ log.success ? '成功' : '失败' }}
                  </el-tag>
                  <span v-if="log.error" class="ml-2 text-red-600">{{ log.error }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-4">
          <el-button @click="statusChangeLog = []" type="warning" size="small">
            清空日志
          </el-button>
        </div>
      </div>
    </el-drawer>

    <!-- 开发环境下的调试按钮 -->
    <el-button
      v-if="isDev"
      class="fixed bottom-4 right-4 z-50"
      type="info"
      circle
      @click="showDebugPanel = true"
      title="打开调试面板"
    >
      <Icon icon="mdi:bug" />
    </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'UserView',
})
import {
  Delete,
  Download,
  Edit,
  Key,
  UserFilled,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { UserRoles } from '@/types/user'
import type { UserInfo } from '@/types/user'
import usePermission from '@/hooks/usePermission'
import { exportExcel } from '@/utils/export'
import { Icon } from '@iconify/vue'
import {User,Phone} from '@element-plus/icons-vue'

// 默认头像
const defaultAvatar = ref('/favicon.ico')

// 使用权限hook
const { hasPermission } = usePermission()

// 使用用户store
const userStore = useUserStore()
const { userList, total, loading, queryParams } = storeToRefs(userStore)
const getList = () => userStore.getList()

// 选中的用户
const selectedUsers = ref<UserInfo[]>([])

// 正在更新状态的用户ID集合
const updatingStatusUserIds = ref<Set<number>>(new Set())

// 调试面板显示状态
const showDebugPanel = ref(false)

// 是否为开发环境
const isDev = import.meta.env.DEV

// 检查用户是否正在更新状态
const isUpdatingStatus = (userId: number): boolean => {
  return updatingStatusUserIds.value.has(userId)
}

// 状态变更日志（开发环境下显示）
const statusChangeLog = ref<Array<{
  userId: number
  username: string
  oldStatus: string
  newStatus: string
  timestamp: string
  success: boolean
  error?: string
}>>([])

// 添加状态变更日志
const addStatusChangeLog = (userId: number, username: string, oldStatus: string, newStatus: string, success: boolean, error?: string) => {
  if (isDev) {
    statusChangeLog.value.unshift({
      userId,
      username,
      oldStatus,
      newStatus,
      timestamp: new Date().toLocaleTimeString(),
      success,
      error
    })
    // 只保留最近20条记录
    if (statusChangeLog.value.length > 20) {
      statusChangeLog.value = statusChangeLog.value.slice(0, 20)
    }
  }
}

// 日期范围
const dateRange = ref<string[]>([])

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

// 弹窗属性
interface DialogProps {
  visible: boolean
  title: string
  type: 'add' | 'edit'
  data?: Partial<UserInfo>
}

const dialogProps = reactive<DialogProps>({
  visible: false,
  title: '',
  type: 'add',
  data: undefined,
})

// 重置密码对话框
const resetPwdDialog = reactive({
  visible: false,
  userId: undefined as undefined | number,
})

// 导入 对话框
const importDialog = reactive({
  visible: false,
  uploadUrl: import.meta.env.VITE_API_BASE_URL + '/user/import',
  headers: {
    Authorization: 'Bearer ' + localStorage.getItem('token'),
  },
})

// 列设置
const showColumnSetting = ref(false)

interface ColumnItem {
  visible: boolean
  label: string
}

type ColumnsType = {
  [key: string]: ColumnItem
}

const columns = reactive<ColumnsType>({
  userId: { visible: true, label: '用户编号' },
  avatar: { visible: true, label: '用户头像' },
  username: { visible: true, label: '用户名' },
  phone: { visible: true, label: '手机号码' },
  email: { visible: true, label: '邮箱' },
  role: { visible: true, label: '角色' },
  introduction: { visible: true, label: '简介' },
  status: { visible: true, label: '状态' },
  createTime: { visible: true, label: '创建时间' },
  updateTime: { visible: true, label: '修改时间' },
})

// 选中的列
const checkedColumns = ref(Object.keys(columns).filter((key) => columns[key].visible))

// 监听日期范围变化
watch(dateRange, (val) => {
  queryParams.value.beginTime = val && val.length > 0 ? val[0] + ' 00:00:00' : undefined
  queryParams.value.endTime = val && val.length > 1 ? val[1] + ' 23:59:59' : undefined
})

const router = useRouter()
// 初始化加载数据
onMounted(async () => {
  console.log('🔄 用户管理页面初始化')

  // 确保用户已登录
  const token = localStorage.getItem('token')

  if (!token) {
    console.warn('⚠️ 未找到token,跳转登录页')
    router.push('/login')
    return
  }

  // 确保有用户信息
  if (!userStore.userInfo) {
    console.log('🔄 获取用户信息')
    try {
      await userStore.getUserInfo()
    } catch (error) {
      console.error('❌ 获取用户信息失败:', error)
      ElMessage.error('获取用户信息失败，请重新登录')
      router.push('/login')
      return
    }
  }

  // 加载用户列表
  console.log('🔄 加载用户列表')
  await userStore.getList()

  // 加载本地存储的列配置
  const savedColumns = localStorage.getItem('userTableColumns')
  if (savedColumns) {
    try {
      const parsedColumns = JSON.parse(savedColumns)
      Object.keys(parsedColumns).forEach((key) => {
        if (columns[key]) {
          columns[key].visible = parsedColumns[key].visible
        }
      })
      checkedColumns.value = Object.keys(columns).filter((key) => columns[key].visible)
    } catch (e) {
      console.error('加载列配置失败', e)
    }
  }
})

// 处理多选框选择事件
const handleSelectionChange = (selection: UserInfo[]) => {
  selectedUsers.value = selection
}

// 搜索
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

// 重置
const handleReset = () => {
  dateRange.value = []
  userStore.resetQuery()
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  queryParams.value.pageSize = val
  getList()
}

// 页码变化
const handleCurrentChange = (val: number) => {
  queryParams.value.pageNum = val
  getList()
}

// 新增用户
const handleAdd = () => {
  dialogProps.visible = true
  dialogProps.title = '新增用户'
  dialogProps.type = 'add'
  dialogProps.data = undefined
}

// 编辑用户
const handleEdit = (row: UserInfo) => {
  dialogProps.visible = true
  dialogProps.title = '编辑用户'
  dialogProps.type = 'edit'
  dialogProps.data = { ...row }
}
// 删除用户
const handleDelete = (row: UserInfo) => {
  ElMessageBox.confirm(`确认删除用户【${row.username}】吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await userStore.deleteUser(row.userId)

      await userStore.getList()
    })
    .catch(() => {})
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请至少选择一个用户')
    return
  }

  const usernames = selectedUsers.value.map((item) => item.username).join('、')
  const userIds = selectedUsers.value.map((item) => item.userId)

  try {
    await ElMessageBox.confirm(`确认删除以下用户吗？<br/>${usernames}`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
    })

    await userStore.deleteBatchUsers(userIds)
    // 批量删除成功后刷新列表
    await userStore.getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败', error)
    }
  }
}
// 处理对话框提交
const handleDialogSuccess = async (data: Partial<UserInfo>, type: 'add' | 'edit') => {
  try {
    if (type === 'add') {
      await userStore.addUser(data)
    } else {
      await userStore.updateUser(data)
    }
    // 确保操作成功后刷新列表
    console.log(getList())
    await userStore.getList()
    console.log(getList())
  } catch (error) {
    console.error('操作失败:', error)
  }
}
// 重置密码
const handleResetPassword = (row: UserInfo) => {
  resetPwdDialog.userId = row.userId
  resetPwdDialog.visible = true
}
// 确认重置密码
const confirmResetPassword = async () => {
  if (!resetPwdDialog.userId) return

  try {
    // 调用重置密码API
    await userStore.resetUserPassword(resetPwdDialog.userId)
    resetPwdDialog.visible = false
  } catch (error) {
    console.error('密码重置失败', error)
  }
}
// 更多操作
const handleCommand = (command: string) => {
  switch (command) {
    case 'importUsers':
      importDialog.visible = true
      break
    case 'printTable':
      window.print()
      break
  }
}

// 修改状态
const handleStatusChange = async (row: UserInfo) => {
  // 防止重复操作
  if (isUpdatingStatus(row.userId)) {
    console.log(`⚠️ 用户 ${row.username} 正在更新状态中，跳过重复操作`)
    // 恢复原状态，因为switch已经改变了
    row.status = row.status === '1' ? '0' : '1'
    return
  }

  // 保存原始状态，用于错误时回滚
  const originalStatus = row.status === '1' ? '0' : '1' // switch改变后的相反状态就是原始状态
  const newStatus = row.status

  // 对于管理员用户，需要确认
  if (row.role === UserRoles.ADMIN && newStatus === '0') {
    try {
      await ElMessageBox.confirm(
        `确定要禁用管理员用户 "${row.username}" 吗？禁用后该用户将无法登录系统。`,
        '确认禁用管理员',
        {
          confirmButtonText: '确定禁用',
          cancelButtonText: '取消',
          type: 'warning',
          confirmButtonClass: 'el-button--danger'
        }
      )
    } catch {
      // 用户取消，恢复原状态
      row.status = originalStatus
      return
    }
  }

  // 添加到更新中的用户集合
  updatingStatusUserIds.value.add(row.userId)

  try {
    // 乐观更新：UI状态已经由switch改变
    console.log(`🔄 正在更新用户 ${row.username} 的状态: ${originalStatus} -> ${newStatus}`)

    // 调用API更新后端状态
    const success = await userStore.updateUserStatus(row.userId, newStatus)

    if (success) {
      console.log(`✅ 用户 ${row.username} 状态更新成功: ${newStatus}`)
      ElMessage.success(`用户状态已${newStatus === '1' ? '启用' : '禁用'}`)
      addStatusChangeLog(row.userId, row.username, originalStatus, newStatus, true)
    } else {
      // API调用失败，回滚状态
      row.status = originalStatus
      console.log(`❌ 用户 ${row.username} 状态更新失败，已回滚到: ${originalStatus}`)
      ElMessage.error('状态更新失败，请重试')
      addStatusChangeLog(row.userId, row.username, originalStatus, newStatus, false, 'API调用失败')
    }
  } catch (error) {
    // 发生异常，回滚状态
    row.status = originalStatus
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    console.error('修改状态失败:', error)
    ElMessage.error('状态更新失败，请重试')
    console.log(`🔄 用户 ${row.username} 状态已回滚到: ${originalStatus}`)
    addStatusChangeLog(row.userId, row.username, originalStatus, newStatus, false, errorMessage)
  } finally {
    // 从更新中的用户集合中移除
    updatingStatusUserIds.value.delete(row.userId)
  }
}
// 处理导出
const handleExport = () => {
  // 构建导出数据
  const header = ['用户名', '手机号码', '邮箱', '角色', '状态', '创建时间']
  const data = userList.value.map((item: UserInfo) => [
    item.username,
    item.phone,
    item.email,
    item.role === UserRoles.ADMIN ? '管理员' : '普通用户',
    item.status === '1' ? '启用' : '禁用',
    item.createTime,
  ])

  exportExcel({
    header,
    data,
    fileName: '用户数据',
    autoWidth: true,
    bookType: 'xlsx',
  })
}

// 导入成功
interface ImportResponse {
  code: number
  message?: string
  data?: unknown
}
const handleImportSuccess = (response: ImportResponse) => {
  if (response.code === 200) {
    ElMessage.success('导入成功')
    importDialog.visible = false
    getList()
  } else {
    ElMessage.error(response.message || '导入失败')
  }
}

// 导入失败
const handleImportError = () => {
  ElMessage.error('导入失败')
}

// 下载导入模板
const handleDownloadTemplate = () => {
  // 实际项目中替换为后端API
  window.location.href = import.meta.env.VITE_API_BASE_URL + '/user/download/template'
}

// 重置列设置
const resetColumns = () => {
  Object.keys(columns).forEach((key) => {
    columns[key].visible = true
  })
  checkedColumns.value = Object.keys(columns)
}

// 保存列设置
const saveColumns = () => {
  // 更新列可见性
  Object.keys(columns).forEach((key) => {
    columns[key].visible = checkedColumns.value.includes(key)
  })

  // 保存到本地存储
  localStorage.setItem('userTableColumns', JSON.stringify(columns))

  showColumnSetting.value = false
  ElMessage.success('列设置保存成功')
}


</script>
<style scoped>
/* 🎨 Enhanced User Management Styles */
/* 搜索表单样式 */
.enhanced-glass-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}
.enhanced-search-form {
  background: transparent;
}

.search-card {
  border-radius: 12px;
}

.search-card :deep(.el-card__header) {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.search-card :deep(.el-card__body) {
  padding: 16px 20px;
}

.enhanced-form-item :deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0 !important;
  line-height: 32px; /* 与输入框高度对齐 */
  display: flex;
  align-items: center;
  font-size: 14px;
  height: 32px; /* 固定标签高度 */
  padding-right: 4px; /* 减少右侧间距 */
}

.enhanced-form-item {
  margin-bottom: 16px !important;
  align-items: center;
  display: flex;
}

.enhanced-form-item :deep(.el-form-item__content) {
  line-height: 32px;
  min-height: 32px; /* 确保内容区域最小高度 */
  display: flex;
  align-items: center;
  flex: 1;
}

/* 确保输入框和选择框的高度一致 */
.enhanced-input :deep(.el-input__inner),
.enhanced-select :deep(.el-input__inner) {
  height: 32px !important;
  line-height: 32px !important;
}

/* 日期选择器对齐 */
.enhanced-date-picker :deep(.el-input__inner) {
  height: 32px !important;
  line-height: 32px !important;
}

/* 选择框下拉箭头对齐 */
.enhanced-select :deep(.el-input__suffix) {
  height: 32px !important;
  display: flex;
  align-items: center;
}

/* 选择框选项样式优化 */
.enhanced-select :deep(.el-select-dropdown__item) {
  padding: 8px 12px;
  min-height: 36px;
  display: flex;
  align-items: center;
}

/* 确保选择框有足够的宽度显示选项文字 */
.enhanced-select {
  min-width: 120px;
}

.enhanced-input :deep(.el-input__wrapper) {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.enhanced-input :deep(.el-input__wrapper):hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.enhanced-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.enhanced-select :deep(.el-select__wrapper) {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.enhanced-select :deep(.el-select__wrapper):hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.enhanced-date-picker :deep(.el-input__wrapper) {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 增强按钮样式 */
.enhanced-btn {
  border-radius: 0.75rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.enhanced-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.enhanced-btn:hover::before {
  left: 100%;
}

.enhanced-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.enhanced-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.enhanced-btn-small {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 4px 8px;
  font-size: 12px;
}

.enhanced-btn-secondary {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white !important;
}

.enhanced-btn-reset {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white !important;
  border: none;
}

.enhanced-tool-btn {
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  padding: 0.5rem;
}

.enhanced-tool-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: scale(1.1);
}

/* 下拉菜单样式 */
.enhanced-dropdown-menu {
  border-radius: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.enhanced-dropdown-item {
  border-radius: 0.5rem;
  margin: 0.25rem;
  transition: all 0.3s ease;
}

.enhanced-dropdown-item:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateX(4px);
}

/* 增强的状态开关样式 */
.enhanced-switch {
  --el-switch-on-color: #10b981 !important;
  --el-switch-off-color: #ef4444 !important;
  transition: all 0.3s ease;
}

.enhanced-switch:hover {
  transform: scale(1.05);
}

/* 确保启用状态显示绿色 */
.enhanced-switch.is-checked {
  --el-switch-on-color: #10b981 !important;
}

/* 确保禁用状态显示红色 */
.enhanced-switch:not(.is-checked) {
  --el-switch-off-color: #ef4444 !important;
}

/* 开关核心样式覆盖 */
.enhanced-switch :deep(.el-switch__core) {
  background-color: var(--el-switch-off-color) !important;
}

.enhanced-switch.is-checked :deep(.el-switch__core) {
  background-color: var(--el-switch-on-color) !important;
}

/* 状态切换加载动画 */
.enhanced-switch.is-loading {
  opacity: 0.7;
  pointer-events: none;
}

.enhanced-switch.is-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  margin: -6px 0 0 -6px;
  border: 2px solid #ffffff;
  border-top-color: transparent;
  border-radius: 50%;
  animation: switch-loading 1s linear infinite;
}

@keyframes switch-loading {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 状态文本样式 */
.enhanced-switch .el-switch__label {
  font-size: 12px;
  font-weight: 500;
}

.enhanced-switch .el-switch__label.is-active {
  color: #10b981;
}

.enhanced-switch .el-switch__label:not(.is-active) {
  color: #ef4444;
}

.search-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.button-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.column-item {
  margin: 8px 0;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  gap: 8px;
}
/* 分页样式 */
.enhanced-pagination :deep(.el-pager li) {
  border-radius: 8px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.enhanced-pagination :deep(.el-pager li:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(148, 84, 162, 0.2);
}

.enhanced-pagination :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}


/* 打印样式 */
@media print {
  .search-card,
  .card-header,
  .pagination-container,
  .el-table__column--selection,
  .el-table__column--fix-right {
    display: none;
  }
}
</style>
