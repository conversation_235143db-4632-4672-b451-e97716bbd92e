<template>
  <div class="playlist-edit-container">
    <!-- 返回按钮 -->
    <div class="mb-4">
      <el-button @click="handleBack">
        <el-icon><ArrowLeft /></el-icon> 返回歌单列表
      </el-button>
    </div>

    <el-row :gutter="20">
      <!-- 左侧歌单信息编辑 -->
      <el-col :span="8">
        <el-card shadow="hover" class="h-full">
          <template #header>
            <div class="font-bold text-lg">歌单信息</div>
          </template>

          <el-form
            ref="formRef"
            :model="playlistForm"
            :rules="rules"
            label-width="80px"
            label-position="top"
          >
            <!-- 歌单封面 -->
            <div class="flex justify-center mb-6">
              <el-upload
                class="avatar-uploader"
                :show-file-list="false"
                :auto-upload="false"
                :on-change="handleCoverChange"
                action="#"
              >
                <div class="relative">
                  <el-image
                    v-if="coverPreview"
                    :src="coverPreview"
                    class="w-40 h-40 rounded-lg object-cover"
                  />
                  <div
                    v-else
                    class="w-40 h-40 bg-gray-100 rounded-lg flex items-center justify-center"
                  >
                    <el-icon class="text-gray-400 text-3xl"><Picture /></el-icon>
                  </div>
                  <div class="upload-mask">
                    <el-icon class="text-white text-xl"><Upload /></el-icon>
                    <span class="text-white text-sm mt-1">点击上传封面</span>
                  </div>
                </div>
              </el-upload>
            </div>

            <el-form-item label="歌单名称" prop="name">
              <el-input v-model="playlistForm.name" placeholder="请输入歌单名称" />
            </el-form-item>

            <el-form-item label="歌单风格" prop="style">
              <el-select
                v-model="playlistForm.style"
                placeholder="请选择歌单风格"
                style="width: 100%"
              >
                <el-option
                  v-for="style in PlaylistStyles"
                  :key="style"
                  :label="style"
                  :value="style"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="歌单简介" prop="description">
              <el-input
                v-model="playlistForm.description"
                type="textarea"
                :rows="4"
                placeholder="请输入歌单简介"
              />
            </el-form-item>

            <el-form-item>
              <div class="flex justify-center gap-4">
                <el-button @click="handleBack">取消</el-button>
                <el-button type="primary" @click="handleSubmit">保存</el-button>
              </div>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 右侧歌曲管理 -->
      <el-col :span="16">
        <el-card shadow="hover">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="font-bold text-lg">歌单歌曲</span>
              <div class="flex gap-2">
                <el-button type="primary"  @click="handleAddSong" round>
                  <el-icon ><span class="iconfont icon-add pr-[10px]"></span></el-icon> 添加歌曲
                </el-button>
                <el-button
                  type="danger"
                  :disabled="selectedSongs.length === 0"
                  @click="handleRemoveSongs"
                  round
                >
                  <el-icon class="pr-1"><Delete /></el-icon> 移除选中 ({{ selectedSongs.length }})
                </el-button>
                <el-button  @click="handleRefreshSongs" round>
                  <el-icon><Refresh /></el-icon> 刷新
                </el-button>
              </div>
            </div>
          </template>

          <!-- 歌曲列表 -->
          <el-table
            v-loading="songsLoading"
            :data="playlistSongs"
            @selection-change="handleSongSelectionChange"
            row-key="id"
            highlight-current-row
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column label="歌曲封面" align="center" width="85">
              <template #default="{ row }">
                <el-image
                  :src="row.coverUrl || defaultCover"
                  class="w-12 h-12 rounded cursor-pointer"
                  fit="cover"
                  @click="handlePreviewImage(row.coverUrl)"
                >
                  <template #error>
                    <div class="w-12 h-12 bg-gray-100 rounded flex items-center justify-center">
                      <el-icon class="text-gray-400"><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column
              label="歌曲名称"
              prop="name"
              min-width="150"
              align="center"
              show-overflow-tooltip
            />
            <el-table-column
              label="歌手"
              prop="artistName"
              min-width="100"
              align="center"
              show-overflow-tooltip
            />
            <el-table-column
              label="专辑"
              prop="album"
              min-width="150"
              align="center"
              show-overflow-tooltip
            />
            <el-table-column label="时长" prop="duration" width="80" align="center" />
            <el-table-column label="风格" prop="style" width="100" align="center">
              <template #default="{ row }">
                <el-tag v-if="row.style" type="info" size="small">{{ row.style }}</el-tag>
                <span v-else class="text-gray-400">-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="120" fixed="right">
              <template #default="{ row }">
                <el-button link type="danger"  @click="handleRemoveSong(row)">
                  <el-icon><Delete /></el-icon> 移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container mt-4">
            <el-pagination
              v-model:current-page="songQueryParams.pageNum"
              v-model:page-size="songQueryParams.pageSize"
              :page-sizes="[10, 20, 30, 50]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="songTotal"
              @size-change="handleSongSizeChange"
              @current-change="handleSongCurrentChange"
              background
            />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="showImageViewer"
      :url-list="[previewImageUrl]"
      @close="showImageViewer = false"
    />

    <!-- 添加歌曲对话框 -->
    <el-dialog
      v-model="showAddSongDialog"
      title="添加歌曲到歌单"
      width="70%"
    >
      <!-- 搜索区域 -->
      <el-form :inline="true" :model="addSongQueryParams" class="mb-4">
        <el-form-item label="歌曲名称:">
          <el-input
            v-model="addSongQueryParams.name"
            placeholder="请输入歌曲名称"
            clearable
            @keyup.enter="handleSearchAddSongs"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="歌手:">
          <el-autocomplete
            v-model="addSongQueryParams.artistName"
            :fetch-suggestions="queryArtistSuggestions"
            placeholder="请输入歌手名称"
            clearable
            @keyup.enter="handleSearchAddSongs"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="风格:">
          <el-select
            v-model="addSongQueryParams.style"
            placeholder="请选择风格"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="style in SongStyles"
              :key="style"
              :label="style"
              :value="style"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearchAddSongs">搜索</el-button>
          <el-button @click="handleResetAddSongs">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 歌曲列表 -->
      <el-table
        v-loading="addSongsLoading"
        :data="availableSongs"
        @selection-change="handleAddSongSelectionChange"
        row-key="id"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="歌曲封面" align="center" width="85">
          <template #default="{ row }">
            <el-image
              :src="row.coverUrl || defaultCover"
              class="w-10 h-10 rounded"
              fit="cover"
            >
              <template #error>
                <div class="w-10 h-10 bg-gray-100 rounded flex items-center justify-center">
                  <el-icon class="text-gray-400"><Picture /></el-icon>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column label="歌曲名称" prop="name" min-width="120" align="center" show-overflow-tooltip />
        <el-table-column label="歌手" prop="artistName" min-width="100" align="center" show-overflow-tooltip />
        <el-table-column label="专辑" prop="album" min-width="120" align="center" show-overflow-tooltip />
        <el-table-column label="时长" prop="duration" width="80" align="center" />
        <el-table-column label="风格" prop="style" width="100" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.style" type="info" size="small">{{ row.style }}</el-tag>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container mt-4">
        <el-pagination
          v-model:current-page="addSongQueryParams.pageNum"
          v-model:page-size="addSongQueryParams.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="addSongTotal"
          @size-change="handleAddSongSizeChange"
          @current-change="handleAddSongCurrentChange"
          background
        />
      </div>

      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="showAddSongDialog = false">取消</el-button>
          <el-button
            type="primary"
            :disabled="selectedAddSongs.length === 0"
            @click="handleConfirmAddSongs"
          >
            添加选中歌曲 ({{ selectedAddSongs.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import type { UploadFile } from 'element-plus'
import {
  ArrowLeft,
  Picture,
  Upload,
  Delete,
  Refresh
} from '@element-plus/icons-vue'
import { PlaylistStyles } from '@/types/playlist'
import { SongStyles } from '@/types/song'  // 确保导入了 SongStyles
import type { SongInfo } from '@/types/song'
import { usePlaylistStore } from '@/stores/playlist'
import { getSongList } from '@/api/song'
import type { SongQueryParams } from '@/types/song'
import { getSingerList } from '@/api/singer'

defineOptions({
  name: 'PlaylistEditView'
})

const route = useRoute()
const router = useRouter()
const formRef = ref<FormInstance>()
const playlistStore = usePlaylistStore()

// 默认封面
const defaultCover = ref('/favicon.ico')

// 表单数据
const playlistForm = reactive<{
  id: number
  name: string
  description: string
  style: string
  coverFile: File | null
}>({
  id: 0,
  name: '',
  description: '',
  style: '',
  coverFile: null
})

// 表单校验规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入歌单名称', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  style: [
    { required: true, message: '请选择歌单风格', trigger: 'change' }
  ]
})

// 封面预览
const coverPreview = ref('')

// 歌单歌曲数据
const playlistSongs = ref<SongInfo[]>([])
const songsLoading = ref(false)
const selectedSongs = ref<SongInfo[]>([])
const songTotal = ref(0)
const showAddSongDialog = ref(false)

// 图片预览
const showImageViewer = ref(false)
const previewImageUrl = ref('')

// 歌曲查询参数
const songQueryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  playlistId: 0
})

// 添加歌曲相关
const availableSongs = ref<SongInfo[]>([])
const addSongsLoading = ref(false)
const selectedAddSongs = ref<SongInfo[]>([])
const addSongTotal = ref(0)

// 添加歌曲查询参数
const addSongQueryParams = reactive<SongQueryParams>({
  pageNum: 1,
  pageSize: 10,
  name: '',
  artistName: '',
  style: ''
})

// 获取歌单详情
const getPlaylistDetail = async () => {
  const playlistId = Number(route.params.id)
  console.log('获取歌单ID:', playlistId)

  if (!playlistId || isNaN(playlistId)) {
    ElMessage.error('歌单ID无效')
    router.push('/playlist')
    return
  }

  try {
    // 调用真实API获取歌单详情
    const playlistData = await playlistStore.getPlaylistDetail(playlistId)

    if (!playlistData) {
      ElMessage.error('歌单不存在')
      router.push('/playlist')
      return
    }

    // 填充表单
    playlistForm.id = playlistData.id
    playlistForm.name = playlistData.name
    playlistForm.description = playlistData.description || ''
    playlistForm.style = playlistData.style || ''
    coverPreview.value = playlistData.coverUrl || ''

    songQueryParams.playlistId = playlistId
    getPlaylistSongs()

    console.log('歌单详情加载完成:', playlistData)
  } catch (error) {
    console.error('获取歌单详情失败', error)
    ElMessage.error('获取歌单详情失败')
    router.push('/playlist')
  }
}

// 获取歌单歌曲列表
const getPlaylistSongs = async () => {
  try {
    songsLoading.value = true

    // 调用真实API获取歌单歌曲
    const res = await playlistStore.getPlaylistSongList(songQueryParams.playlistId, {
      pageNum: songQueryParams.pageNum,
      pageSize: songQueryParams.pageSize
    })

    playlistSongs.value = res.list
    songTotal.value = res.total
  } catch (error) {
    console.error('获取歌单歌曲失败', error)
    ElMessage.error('获取歌单歌曲失败')
  } finally {
    songsLoading.value = false
  }
}

// 获取可添加的歌曲列表
const getAvailableSongs = async () => {
  try {
    addSongsLoading.value = true

    // 调用真实API获取歌曲列表
    const res = await getSongList(addSongQueryParams)

    // 过滤掉已经在歌单中的歌曲
    const playlistSongIds = playlistSongs.value.map(song => song.id)
    availableSongs.value = res.list.filter(song => !playlistSongIds.includes(song.id))
    addSongTotal.value = res.total
  } catch (error) {
    console.error('获取可添加歌曲失败', error)
    ElMessage.error('获取可添加歌曲失败')
  } finally {
    addSongsLoading.value = false
  }
}

// 封面上传处理
const handleCoverChange = (file: UploadFile) => {
  if (file.raw) {
    playlistForm.coverFile = file.raw
    // 创建预览URL
    const reader = new FileReader()
    reader.onload = (e) => {
      coverPreview.value = e.target?.result as string
    }
    reader.readAsDataURL(file.raw)
  }
}

// 返回列表
const handleBack = () => {
  router.push('/playlist')
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) return

    try {
      // 构建提交数据
      const updateData = {
        id: playlistForm.id,
        name: playlistForm.name,
        description: playlistForm.description,
        style: playlistForm.style
      }

      // 更新歌单信息
      await playlistStore.updatePlaylist(updateData)

      // 如果有新封面，上传封面
      if (playlistForm.coverFile) {
        await playlistStore.uploadPlaylistCover(playlistForm.id, playlistForm.coverFile)
      }

      ElMessage.success('保存成功')
      router.push('/playlist')
    } catch (error) {
      console.error('保存歌单失败', error)
      ElMessage.error('保存歌单失败')
    }
  })
}

// 添加歌曲
const handleAddSong = () => {
  if (playlistForm.style.length === 0) {
    ElMessage.warning('请先选择歌单风格，以便筛选匹配的歌曲')
    return
  }

  showAddSongDialog.value = true
  // 重置查询参数
  Object.assign(addSongQueryParams, {
    pageNum: 1,
    pageSize: 10,
    name: '',
    artistName: '',
    style: ''
  })
  selectedAddSongs.value = []
  getAvailableSongs()
}

// 移除单首歌曲
const handleRemoveSong = async (song: SongInfo) => {
  try {
    await ElMessageBox.confirm(`确定要从歌单中移除歌曲"${song.name}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用真实API移除歌曲
    await playlistStore.removeSongFromPlaylistAction(playlistForm.id, song.id)
    getPlaylistSongs()
  } catch (error) {
    console.log('取消移除:', error)
  }
}

// 批量移除歌曲
const handleRemoveSongs = async () => {
  if (selectedSongs.value.length === 0) {
    ElMessage.warning('请至少选择一首歌曲')
    return
  }

  const songNames = selectedSongs.value.map(song => song.name).join('、')

  try {
    await ElMessageBox.confirm(`确定要从歌单中移除以下歌曲吗？<br/>${songNames}`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true
    })

    // 调用真实API批量移除歌曲
    const songIds = selectedSongs.value.map(song => song.id)
    await playlistStore.batchRemoveSongsFromPlaylistAction(playlistForm.id, songIds)

    selectedSongs.value = []
    getPlaylistSongs()
  } catch (error) {
    console.log('取消移除:', error)
  }
}

// 选择变化
const handleSongSelectionChange = (selection: SongInfo[]) => {
  selectedSongs.value = selection
}

// 添加歌曲选择变化
const handleAddSongSelectionChange = (selection: SongInfo[]) => {
  selectedAddSongs.value = selection
}

// 确认添加歌曲
const handleConfirmAddSongs = async () => {
  if (selectedAddSongs.value.length === 0) {
    ElMessage.warning('请至少选择一首歌曲')
    return
  }

  try {
    // 调用真实API添加歌曲到歌单
    const songIds = selectedAddSongs.value.map(song => song.id)
    await playlistStore.addSongsToPlaylistAction(playlistForm.id, songIds)

    showAddSongDialog.value = false
    getPlaylistSongs()
  } catch (error) {
    console.error('添加歌曲失败', error)
    ElMessage.error('添加歌曲失败')
  }
}

// 搜索歌曲
const handleSearchAddSongs = () => {
  addSongQueryParams.pageNum = 1
  getAvailableSongs()
}

// 重置搜索
const handleResetAddSongs = () => {
  resetAddSongQueryParams()
  getAvailableSongs()
}

// 刷新歌曲列表
const handleRefreshSongs = () => {
  getPlaylistSongs()
}

// 预览图片
const handlePreviewImage = (url: string) => {
  if (url) {
    previewImageUrl.value = url
    showImageViewer.value = true
  }
}

// 分页处理
const handleSongSizeChange = (val: number) => {
  songQueryParams.pageSize = val
  getPlaylistSongs()
}

const handleSongCurrentChange = (val: number) => {
  songQueryParams.pageNum = val
  getPlaylistSongs()
}

// 添加歌曲分页处理
const handleAddSongSizeChange = (val: number) => {
  addSongQueryParams.pageSize = val
  getAvailableSongs()
}

const handleAddSongCurrentChange = (val: number) => {
  addSongQueryParams.pageNum = val
  getAvailableSongs()
}


// 定义歌手建议项的类型
interface ArtistSuggestion {
  value: string;
}

// 歌手名称自动补全
const queryArtistSuggestions = (queryString: string, callback: (suggestions: ArtistSuggestion[]) => void) => {
  // 如果没有输入，返回空数组
  if (!queryString) {
    callback([])
    return
  }
  // 调用API获取歌手建议
  getSingerList({
    singerName: queryString,
    pageNum: 1,
    pageSize: 10,
    type: '',
    location: ''
  }).then(res => {
    // 转换为自动补全需要的格式
    const suggestions: ArtistSuggestion[] = res.list.map(singer => ({
      value: singer.singerName
    }))
    callback(suggestions)
  }).catch(error => {
    console.error('获取歌手建议失败', error)
    // 如果API调用失败，返回空数组
    callback([])
  })
}

// 重置添加歌曲搜索参数
const resetAddSongQueryParams = () => {
  Object.assign(addSongQueryParams, {
    pageNum: 1,
    pageSize: 10,
    name: '',
    artistName: '',
    style: ''
  })
}

// 初始化
onMounted(() => {
  // 重置搜索参数，确保每次进入页面时都是干净的状态
  resetAddSongQueryParams()
  getPlaylistDetail()
})

// 页面卸载时清理状态
onBeforeUnmount(() => {
  // 确保离开页面时重置搜索参数
  resetAddSongQueryParams()
})
</script>

<style scoped>
.playlist-edit-container {
  padding: 16px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
}

.upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 0.5rem;
}

.avatar-uploader:hover .upload-mask {
  opacity: 1;
}

</style>





