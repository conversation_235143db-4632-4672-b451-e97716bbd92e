<template>
  <el-dialog
    v-model="visible"
    title="回复反馈"
    width="600px"
    @update:model-value="handleVisibleChange"
  >
    <div v-if="feedbackData" class="reply-dialog">
      <!-- 反馈信息概览 -->
      <div class="feedback-overview mb-4">
        <h4 class="text-sm font-medium text-gray-700 mb-2">反馈信息</h4>
        <div class="overview-box">
          <div class="flex items-center justify-between mb-2">
            <span class="text-xs text-gray-500">反馈编号：{{ feedbackData.id }}</span>
            <span class="text-xs text-gray-500">用户：{{ feedbackData.username || '未知用户' }}</span>
          </div>
          <div class="feedback-content">{{ feedbackData.content }}</div>
        </div>
      </div>

      <!-- 回复表单 -->
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="回复内容" prop="reply">
          <el-input
            v-model="form.reply"
            type="textarea"
            :rows="6"
            placeholder="请输入回复内容..."
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="处理状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择处理状态" class="w-full">
            <el-option
              v-for="status in availableStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          发送回复
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { FeedbackStatuses, FeedbackStatus } from '@/types/feedback'
import type { FeedbackInfo } from '@/types/feedback'
import { useFeedbackStore } from '@/stores/feedback'

interface Props {
  modelValue: boolean
  feedbackData?: FeedbackInfo | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  feedbackData: null
})

const emit = defineEmits<Emits>()

const feedbackStore = useFeedbackStore()
const formRef = ref<FormInstance>()
const submitting = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const form = reactive({
  reply: '',
  status: FeedbackStatus.PROCESSING
})

// 可选择的状态（排除待处理状态）
const availableStatuses = computed(() => {
  return FeedbackStatuses.filter(status => status.value !== FeedbackStatus.PENDING)
})

// 表单验证规则
const rules = reactive<FormRules>({
  reply: [
    { required: true, message: '请输入回复内容', trigger: 'blur' },
    { min: 5, max: 1000, message: '回复内容长度在 5 到 1000 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择处理状态', trigger: 'change' }
  ]
})

// 监听对话框显示状态，重置表单
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      form.reply = ''
      form.status = FeedbackStatus.PROCESSING
      formRef.value?.clearValidate()
    }
  }
)

const handleVisibleChange = (value: boolean) => {
  emit('update:modelValue', value)
}

const handleClose = () => {
  emit('update:modelValue', false)
}

const handleSubmit = async () => {
  if (!formRef.value || !props.feedbackData) return

  await formRef.value.validate(async (valid) => {
    if (valid && props.feedbackData) {
      try {
        submitting.value = true

        // 发送回复
        await feedbackStore.replyFeedbackAction(props.feedbackData.id, form.reply)

        // 如果状态有变化，更新状态
        if (form.status !== props.feedbackData.status) {
          await feedbackStore.updateStatus(props.feedbackData.id, form.status)
        }

        ElMessage.success('回复成功')
        emit('success')
        handleClose()
      } catch (error) {
        console.error('回复失败:', error)
      } finally {
        submitting.value = false
      }
    }
  })
}
</script>

<style scoped>
.reply-dialog {
  @apply text-sm;
}

.overview-box {
  @apply p-3 bg-gray-50 rounded-lg border;
}

.feedback-content {
  @apply text-gray-700 text-sm leading-relaxed;
  max-height: 80px;
  overflow-y: auto;
}
</style>
