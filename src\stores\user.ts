import { defineStore } from 'pinia'
import type { UserInfo, UserQueryParams } from '@/types/user'
// 在store函数内部
import {
  getUserList,
  addUser as apiAddUser,
  updateUser as apiUpdateUser,
  deleteUser as apiDeleteUser,
  batchDeleteUsers,
  updateUserStatus as apiUpdateUserStatus,
  resetUserPassword as apiResetUserPassword,
  forgotPassword as apiForgotPassword
} from '@/api/user'
import {
  getUserInfo as ApiGetUserInfo,
  login as ApiLogin,
  logout as ApiLogout

} from '@/api/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  const userList = ref<UserInfo[]>([])
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string>(localStorage.getItem('token') || '')
  const total = ref(0)
  const loading = ref(false)
  const queryParams = reactive<UserQueryParams>({
    username: '',
    phone: '',
    status: undefined,
    role: undefined,
    pageNum: 1,
    pageSize: 10
  })
  // 模拟数据
  const permissions = ref<string[]>([
    'user:add', 'user:edit', 'user:delete'
  ])

  // 登录方法
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      const res = await ApiLogin(username, password)
      token.value = res.token
      localStorage.setItem('token', res.token)
      await getUserInfo()
      return true
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '登录失败'
      ElMessage.error(errorMessage)
      return false
    }
  }

  // 获取用户信息
  const getUserInfo = async (): Promise<boolean> => {
    try {
      const res = await ApiGetUserInfo()
      userInfo.value = res
      // 可以在这里设置权限数据
      // permissions.value = res.permissions || []
      return true
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '获取用户信息失败'
      ElMessage.error(errorMessage)
      return false
    }
  }
  const logout = async () => {
    try {
      // 调用后端登出API
      await ApiLogout()
    } catch (error) {
      console.error('登出API调用失败', error)
      // 即使API调用失败，也继续清理前端状态
    } finally {
      // 清理前端状态
      token.value = ''
      userInfo.value = null
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
  }

  const getList = async () => {
    try {
      loading.value = true;
      const res = await getUserList(queryParams)
      userList.value = res.list
      total.value = res.total
    } catch (error) {
      console.error("获取用户列表失败", error)
    } finally {
      loading.value = false;
    }
  }

  const resetQuery = () => {
    queryParams.username = ''
    queryParams.phone = ''
    queryParams.status = undefined
    queryParams.role = undefined
    queryParams.pageNum = 1
  }
  const addUser = async (userData: Partial<UserInfo>) => {
    try {
      await apiAddUser(userData)
      ElMessage.success("添加用户成功")
      return true
    } catch (error) {
      console.error("更新用户失败", error)
      ElMessage.error("添加用户失败")
      return false
    }
  }
  const updateUser = async (userData: Partial<UserInfo>) => {
    try {
      await apiUpdateUser(userData)
      ElMessage.success("更新用户成功")
      return true

    } catch (error) {
      console.error("更新用户失败", error)
      ElMessage.error("更新用户失败")
      return false
    }
  }
  const deleteUser = async (userId: number) => {
    try {
      await apiDeleteUser(userId)
      ElMessage.success("删除用户成功")
      return true
    } catch (error) {
      console.error("删除用户失败", error)
      ElMessage.error("删除用户失败")
      return false
    }
  }
  const deleteBatchUsers = async (userIds: number[]) => {
    try {
      await batchDeleteUsers(userIds)
      ElMessage.success("批量删除用户成功")
      return true
    } catch (error) {
      console.error("批量删除用户失败", error)
      ElMessage.error("批量删除用户失败")
      return false
    }
  }
  const updateUserStatus = async (userId: number, status: string) => {
    // 找到要更新的用户在本地列表中的索引
    const userIndex = userList.value.findIndex(user => user.userId === userId)
    const originalStatus = userIndex !== -1 ? userList.value[userIndex].status : null

    try {
      console.log(`🌐 调用API更新用户状态: userId=${userId}, status=${status}`)

      // 调用后端API
      await apiUpdateUserStatus(userId, status)

      // API调用成功，确保本地状态已更新
      if (userIndex !== -1) {
        userList.value[userIndex].status = status
        console.log(`📝 本地状态已同步: userId=${userId}, status=${status}`)
      }

      console.log("✅ 用户状态更新成功")
      return true
    } catch (error) {
      console.error("❌ 更新用户状态失败:", error)

      // API调用失败，回滚本地状态
      if (userIndex !== -1 && originalStatus !== null) {
        userList.value[userIndex].status = originalStatus
        console.log(`🔄 本地状态已回滚: userId=${userId}, status=${originalStatus}`)
      }

      // 不在这里显示错误消息，让调用方处理
      return false
    }
  }

  const resetUserPassword = async (userId: number) => {
    try {

      await apiResetUserPassword(userId)
      ElMessage.success('密码重置成功')
      return true
    } catch (error) {
      console.error("密码重置失败", error)
      ElMessage.error('密码重置失败')
      return false
    }
  }

  const forgotPassword = async (username: string, answer: string, newPassword: string) => {
    try {
      await apiForgotPassword(username, answer, newPassword)
      return true
    } catch (error) {
      console.error("密码重置失败", error)
      return false
    }
  }

  return {
    // 状态
    userInfo,
    userList,
    total,
    loading,
    permissions,
    queryParams,
    // 方法
    login,
    getUserInfo,
    getList,
    resetQuery,
    addUser,
    updateUser,
    deleteUser,
    deleteBatchUsers,
    updateUserStatus,
    logout,
    resetUserPassword,
    forgotPassword
  }

})
