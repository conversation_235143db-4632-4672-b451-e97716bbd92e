export interface UserInfo {
  userId: number;
  username: string;
  password?: string;
  avatar?: string;
  phone: string;
  email: string;
  introduction?: string;
  status: string;
  createTime: string;
  updateTime: string;
  role: string;
}
export interface UserQueryParams {
  username?: string;
  phone?: string;
  status?: string;
  role?: string;
  pageNum: number;
  pageSize: number;
  beginTime?: string;
  endTime?: string;
}

export const UserRoles = {
  ADMIN: 'ADMIN',
  USER: 'USER',
} as const;

export type UserRole = typeof UserRoles[keyof typeof UserRoles];
