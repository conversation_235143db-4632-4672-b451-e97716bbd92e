import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import './style/index.scss'
import './style/elementplus.scss'
import { ElConfigProvider } from 'element-plus'
import './assets/fonts/iconfont.scss'
// import 'element-plus/dist/index.css'

const app = createApp(App)

app.component("ElConfigProvider", ElConfigProvider)
app.use(createPinia())
app.use(router)


app.mount('#app')
