/* 🎨 Element Plus 主题定制 - Happy Music 风格 */
:root {
  /* 主色调 */
  --el-color-primary: #667eea;
  --el-color-primary-light-3: #8b9df0;
  --el-color-primary-light-5: #a4b5f3;
  --el-color-primary-light-7: #bccdf6;
  --el-color-primary-light-8: #d1e0f9;
  --el-color-primary-light-9: #e5ecfc;
  --el-color-primary-dark-2: #5a6bd8;

  /* 成功色 */
  --el-color-success: #10b981;
  --el-color-success-light-3: #4dd4aa;
  --el-color-success-light-5: #7de3c0;
  --el-color-success-light-7: #adf2d6;
  --el-color-success-light-9: #dcf9ec;

  /* 警告色 */
  --el-color-warning: #f59e0b;
  --el-color-warning-light-3: #f7b955;
  --el-color-warning-light-5: #f9d071;
  --el-color-warning-light-7: #fbe7a0;
  --el-color-warning-light-9: #fdf4d0;

  /* 危险色 */
  --el-color-danger: #ef4444;
  --el-color-danger-light-3: #f37777;
  --el-color-danger-light-5: #f79999;
  --el-color-danger-light-7: #fbbbbb;
  --el-color-danger-light-9: #fddddd;

  /* 信息色 */
  --el-color-info: #6b7280;
  --el-color-info-light-3: #9ca3af;
  --el-color-info-light-5: #d1d5db;
  --el-color-info-light-7: #e5e7eb;
  --el-color-info-light-9: #f3f4f6;

  /* 菜单样式 */
  --el-menu-active-color: #667eea;
  --el-menu-text-color: #64748b;
  --el-menu-hover-text-color: #475569;
  --el-menu-bg-color: #ffffff;
  --el-menu-hover-bg-color: rgba(102, 126, 234, 0.08);
  --el-menu-border-color: transparent;

  /* 卡片样式 */
  --el-card-border-color: rgba(255, 255, 255, 0.1);
  --el-card-border-radius: 16px;

  /* 按钮样式 */
  --el-button-border-radius: 12px;

  /* 输入框样式 */
  --el-input-border-radius: 10px;
  --el-input-border-color: #e5e7eb;
  --el-input-hover-border-color: #667eea;
  --el-input-focus-border-color: #667eea;

  /* 表格样式 */
  --el-table-border-color: #f0f0f0;
  --el-table-header-bg-color: #fafafa;
  --el-table-row-hover-bg-color: #f8faff;

  /* 分页样式 */
  --el-pagination-button-color: #667eea;
  --el-pagination-hover-color: #8b9df0;

  /* 标签样式 */
  --el-tag-border-radius: 8px;

  /* 对话框样式 */
  --el-dialog-border-radius: 20px;

  /* 消息样式 */
  --el-message-border-radius: 12px;

  /* 通知样式 */
  --el-notification-border-radius: 16px;
}

/* 菜单项激活状态 */
.el-menu-item.is-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-radius: 12px;
  margin: 4px 8px;
  color: #ffffff !important;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.el-menu-item.is-active::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.el-menu-item.is-active:hover::before {
  left: 100%;
}

/* 菜单项悬停效果 */
.el-menu-item:hover {
  background-color: rgba(102, 126, 234, 0.1) !important;
  border-radius: 12px;
  margin: 4px 8px;
  color: #475569 !important;
  transition: all 0.3s ease;
}

/* 子菜单样式 */
.el-sub-menu .el-menu-item {
  background-color: rgba(248, 250, 252, 0.8) !important;
  margin: 2px 16px;
  border-radius: 8px;
  color: #64748b !important;
}

.el-sub-menu .el-menu-item:hover {
  background-color: rgba(102, 126, 234, 0.15) !important;
  color: #475569 !important;
}

/* 菜单整体样式 */
.el-menu {
  border-right: none !important;
  background-color: #ffffff !important;
}

/* 菜单项基础样式 */
.el-menu-item {
  color: #64748b !important;
  margin: 2px 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

/* 菜单项图标样式 */
.el-menu-item .el-icon {
  color: inherit;
  margin-right: 8px;
}

/* 卡片增强样式 */
.el-card {
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--el-card-border-radius);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
}

.el-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.el-card__header {
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  border-bottom: 1px solid #e5e7eb;
  padding: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 按钮增强样式 */
.el-button {
  border-radius: var(--el-button-border-radius);
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.el-button--success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
}

.el-button--warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border: none;
}

.el-button--danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
}

/* 输入框增强样式 */
.el-input__wrapper {
  border-radius: var(--el-input-border-radius);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.el-input__wrapper:hover {
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.el-input__wrapper.is-focus {
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

/* 表格增强样式 */
.el-table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.el-table__header-wrapper {
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
}

.el-table th {
  background: transparent !important;
  font-weight: 600;
  color: var(--el-text-color-primary);
  border-bottom: 2px solid #e5e7eb;
}

.el-table tr:hover > td {
  background-color: #f8faff !important;
}

/* 分页增强样式 */
.el-pagination .el-pager li {
  border-radius: 8px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.el-pagination .el-pager li:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.el-pagination .el-pager li.is-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

/* 标签增强样式 */
.el-tag {
  border-radius: var(--el-tag-border-radius);
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 对话框增强样式 */
.el-dialog {
  border-radius: var(--el-dialog-border-radius);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.el-dialog__header {
  background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
}

.el-dialog__title {
  font-weight: 600;
  font-size: 18px;
  color: var(--el-text-color-primary);
}

/* 消息和通知增强样式 */
.el-message {
  border-radius: var(--el-message-border-radius);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10px);
}

.el-notification {
  border-radius: var(--el-notification-border-radius);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}
