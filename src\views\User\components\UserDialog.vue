<template>
  <div>
    <el-dialog
      :title="dialogProps.title"
      :model-value="dialogProps.visible"
      @close="handleClose"
      width="500px"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" label-position="right">
        <el-form-item v-if="dialogProps.type === 'edit'" label="用户编号">
          <el-input v-model="form.userId" disabled />
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="dialogProps.type === 'add'">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="密码" prop="password" v-else>
          <el-button type="primary" @click="showPasswordInput = !showPasswordInput">
            {{ showPasswordInput ? '取消修改' : '修改密码' }}
          </el-button>
        </el-form-item>
        <el-form-item
          label="新密码"
          prop="password"
          v-if="dialogProps.type === 'edit' && showPasswordInput"
        >
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="手机号码" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="form.role" placeholder="请选择职位">
            <el-option label="管理员" :value="UserRoles.ADMIN" />
            <el-option label="普通用户" :value="UserRoles.USER" />
          </el-select>
        </el-form-item>
        <el-form-item label="简介" prop="introduction">
          <el-input
            v-model="form.introduction"
            type="textarea"
            :rows="3"
            placeholder="请输入用户简介"
            maxlength="255"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-switch v-model="form.status" active-value="1" inactive-value="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'UserDialog',
})
import { ref, reactive, watch, computed } from 'vue'
import { UserRoles } from '@/types/user'
import type { UserInfo } from '@/types/user'
import type { FormInstance, FormRules } from 'element-plus'
const props = defineProps<{
  dialogProps: {
    title: string
    visible: boolean
    type: 'add' | 'edit'
    data?: Partial<UserInfo>
  }
}>()

const emit = defineEmits(['update:visible', 'success'])
const formRef = ref<FormInstance>()
const showPasswordInput = ref(false)
const form = reactive<Partial<UserInfo>>({
  userId: undefined,
  username: '',
  password: '',
  phone: '',
  email: '',
  introduction: '',
  status: '1',
  role: UserRoles.USER,
})

// 使用计算属性来动态生成验证规则
const rules = computed<FormRules>(() => ({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 15, message: '用户名长度在2-15个字符之间', trigger: 'blur' },
  ],
  password: [
    {
      required: props.dialogProps.type === 'add' || (props.dialogProps.type === 'edit' && showPasswordInput.value),
      message: '请输入密码',
      trigger: 'blur'
    },
    { min: 6, max: 14, message: '密码长度在6-14个字符之间', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
  ],
  role: [{ required: true, message: '请选择用户角色', trigger: 'change' }],
  introduction: [{ max: 255, message: '简介最多255个字符', trigger: 'blur' }],
}))

watch(
  () => props.dialogProps.visible,
  (val) => {
    if (val) {
      showPasswordInput.value = false
      if (props.dialogProps.type === 'edit' && props.dialogProps.data) {
        Object.assign(form, props.dialogProps.data)
        form.password = ''
      } else {
        form.userId = undefined
        form.username = ''
        form.password = ''
        form.phone = ''
        form.email = ''
        form.introduction = ''
        form.status = '1'
        form.role = UserRoles.USER
      }
    }
  },
)
const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      const submitData = { ...form }

      // 如果是编辑模式且没有选择修改密码，则删除密码字段
      if (props.dialogProps.type === 'edit' && !showPasswordInput.value) {
        delete submitData.password
      }

      emit('success', submitData, props.dialogProps.type)
      handleClose()
    }
  })
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
