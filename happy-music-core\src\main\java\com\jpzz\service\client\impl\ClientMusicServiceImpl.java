package com.jpzz.service.client.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jpzz.mapper.client.ClientMusicMapper;
import com.jpzz.mapper.client.UserBehaviorMapper;
import com.jpzz.pojo.dto.client.PlayRecordDTO;
import com.jpzz.pojo.entity.PlayRecord;
import com.jpzz.pojo.entity.RecentPlay;
import com.jpzz.pojo.entity.UserLike;
import com.jpzz.pojo.entity.UserCollection;
import com.jpzz.pojo.vo.client.ClientSongVO;
import com.jpzz.pojo.vo.client.ClientPlaylistVO;
import com.jpzz.result.PageResult;
import com.jpzz.service.client.ClientMusicService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户端音乐服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ClientMusicServiceImpl implements ClientMusicService {

    @Resource
    private ClientMusicMapper clientMusicMapper;

    @Resource
    private UserBehaviorMapper userBehaviorMapper;

    @Override
    public PageResult<ClientSongVO> getRecommendSongs(Long userId, Integer page, Integer size) {
        log.info("获取推荐歌曲，用户ID：{}，页码：{}，每页大小：{}", userId, page, size);

        Page<ClientSongVO> pageObj = new Page<>(page, size);
        Page<ClientSongVO> result;

        if (userId != null) {
            // 基于用户偏好推荐
            result = clientMusicMapper.selectRecommendSongsByUserPreference(pageObj, userId);
        } else {
            // 游客模式，返回热门歌曲
            result = clientMusicMapper.selectHotSongs(pageObj);
        }

        return new PageResult<>(result.getTotal(), result.getRecords(), page, size);
    }

    @Override
    public PageResult<ClientSongVO> getHotSongs(Integer page, Integer size) {
        log.info("获取热门歌曲，页码：{}，每页大小：{}", page, size);

        Page<ClientSongVO> pageObj = new Page<>(page, size);
        Page<ClientSongVO> result = clientMusicMapper.selectHotSongs(pageObj);

        return new PageResult<>(result.getTotal(), result.getRecords(), page, size);

    }

    @Override
    public PageResult<ClientSongVO> getNewSongs(Integer page, Integer size) {
        log.info("获取新歌推荐，页码：{}，每页大小：{}", page, size);

        Page<ClientSongVO> pageObj = new Page<>(page, size);
        Page<ClientSongVO> result = clientMusicMapper.selectNewSongs(pageObj);

        return new PageResult<>(result.getTotal(), result.getRecords(), page, size);

    }

    @Override
    public PageResult<ClientPlaylistVO> getHotPlaylists(Integer page, Integer size) {
        log.info("获取热门歌单，页码：{}，每页大小：{}", page, size);

        Page<ClientPlaylistVO> pageObj = new Page<>(page, size);
        Page<ClientPlaylistVO> result = clientMusicMapper.selectHotPlaylists(pageObj);

        return new PageResult<>(result.getTotal(), result.getRecords(), page, size);

    }

    @Override
    public ClientSongVO getSongDetail(Long songId, Long userId) {
        log.info("获取歌曲详情，歌曲ID：{}，用户ID：{}", songId, userId);

        ClientSongVO songDetail = clientMusicMapper.selectSongDetailById(songId, userId);
        if (songDetail == null) {
            throw new RuntimeException("歌曲不存在或已被删除");
        }

        return songDetail;
    }

    @Override
    public ClientPlaylistVO getPlaylistDetail(Long playlistId, Long userId) {
        log.info("获取歌单详情，歌单ID：{}，用户ID：{}", playlistId, userId);

        ClientPlaylistVO playlistDetail = clientMusicMapper.selectPlaylistDetailById(playlistId, userId);
        if (playlistDetail == null) {
            throw new RuntimeException("歌单不存在或已被删除");
        }

        return playlistDetail;
    }

    @Override
    public PageResult<ClientSongVO> getPlaylistSongs(Long playlistId, Long userId, Integer page, Integer size) {
        log.info("获取歌单歌曲，歌单ID：{}，用户ID：{}，页码：{}，每页大小：{}", playlistId, userId, page, size);

        Page<ClientSongVO> pageObj = new Page<>(page, size);
        Page<ClientSongVO> result = clientMusicMapper.selectPlaylistSongs(pageObj, playlistId, userId);

        return new PageResult<>(result.getTotal(), result.getRecords(), page, size);

    }

    @Override
    public PageResult<ClientSongVO> searchSongs(String keyword, Long userId, Integer page, Integer size) {
        log.info("搜索歌曲，关键词：{}，用户ID：{}，页码：{}，每页大小：{}", keyword, userId, page, size);

        Page<ClientSongVO> pageObj = new Page<>(page, size);
        Page<ClientSongVO> result = clientMusicMapper.searchSongs(pageObj, keyword, userId);

        return new PageResult<>(result.getTotal(), result.getRecords(), page, size);

    }

    @Override
    public PageResult<ClientPlaylistVO> searchPlaylists(String keyword, Long userId, Integer page, Integer size) {
        log.info("搜索歌单，关键词：{}，用户ID：{}，页码：{}，每页大小：{}", keyword, userId, page, size);

        Page<ClientPlaylistVO> pageObj = new Page<>(page, size);
        Page<ClientPlaylistVO> result = clientMusicMapper.searchPlaylists(pageObj, keyword, userId);

        return new PageResult<>(result.getTotal(), result.getRecords(), page, size);

    }

    @Override
    @Transactional
    public void recordPlay(PlayRecordDTO playRecordDTO) {
        log.info("记录播放行为：{}", playRecordDTO);

        // 1. 插入播放记录
        PlayRecord playRecord = new PlayRecord();
        BeanUtils.copyProperties(playRecordDTO, playRecord);
        playRecord.setCreateTime(LocalDateTime.now());
        userBehaviorMapper.insertPlayRecord(playRecord);

        // 2. 更新或插入最近播放
        RecentPlay recentPlay = new RecentPlay();
        recentPlay.setUserId(playRecordDTO.getUserId());
        recentPlay.setSongId(playRecordDTO.getSongId());
        recentPlay.setPlayTime(LocalDateTime.now());
        recentPlay.setUpdateTime(LocalDateTime.now());
        userBehaviorMapper.insertOrUpdateRecentPlay(recentPlay);

        // 3. 更新歌曲播放次数
        userBehaviorMapper.updateSongPlayCount(playRecordDTO.getSongId(), 1);

        // 4. 如果是从歌单播放，更新歌单播放次数
        if (playRecordDTO.getPlaylistId() != null) {
            userBehaviorMapper.updatePlaylistPlayCount(playRecordDTO.getPlaylistId(), 1);
        }

        // 5. 定期清理用户最近播放记录(保留最新100条)
        userBehaviorMapper.cleanUserRecentPlays(playRecordDTO.getUserId(), 100);
    }

    @Override
    public PageResult<ClientSongVO> getRecentPlays(Long userId, Integer page, Integer size) {
        log.info("获取最近播放，用户ID：{}，页码：{}，每页大小：{}", userId, page, size);

        Page<ClientSongVO> pageObj = new Page<>(page, size);
        Page<ClientSongVO> result = clientMusicMapper.selectRecentPlays(pageObj, userId);

        return new PageResult<>(result.getTotal(), result.getRecords(),page,size);

    }

    @Override
    @Transactional
    public void likeSong(Long userId, Long songId, Boolean isLike) {
        log.info("{}歌曲，用户ID：{}，歌曲ID：{}", isLike ? "喜欢" : "取消喜欢", userId, songId);

        if (isLike) {
            // 添加喜欢记录
            UserLike userLike = new UserLike();
            userLike.setUserId(userId);
            userLike.setTargetId(songId);
            userLike.setTargetType("SONG");
            userLike.setCreateTime(LocalDateTime.now());
            userBehaviorMapper.insertUserLike(userLike);

            // 增加歌曲喜欢次数
            userBehaviorMapper.updateSongLikeCount(songId, 1);
        } else {
            // 删除喜欢记录
            userBehaviorMapper.deleteUserLike(userId, songId, "SONG");

            // 减少歌曲喜欢次数
            userBehaviorMapper.updateSongLikeCount(songId, -1);
        }
    }

    @Override
    @Transactional
    public void collectSong(Long userId, Long songId, Boolean isCollect) {
        log.info("{}歌曲，用户ID：{}，歌曲ID：{}", isCollect ? "收藏" : "取消收藏", userId, songId);

        if (isCollect) {
            // 添加收藏记录
            UserCollection userCollection = new UserCollection();
            userCollection.setUserId(userId);
            userCollection.setTargetId(songId);
            userCollection.setTargetType("SONG");
            userCollection.setFolderName("默认收藏夹");
            userCollection.setCreateTime(LocalDateTime.now());
            userBehaviorMapper.insertUserCollection(userCollection);
        } else {
            // 删除收藏记录
            userBehaviorMapper.deleteUserCollection(userId, songId, "SONG");
        }
    }

    @Override
    @Transactional
    public void likePlaylist(Long userId, Long playlistId, Boolean isLike) {
        log.info("{}歌单，用户ID：{}，歌单ID：{}", isLike ? "喜欢" : "取消喜欢", userId, playlistId);

        if (isLike) {
            // 添加喜欢记录
            UserLike userLike = new UserLike();
            userLike.setUserId(userId);
            userLike.setTargetId(playlistId);
            userLike.setTargetType("PLAYLIST");
            userLike.setCreateTime(LocalDateTime.now());
            userBehaviorMapper.insertUserLike(userLike);

            // 增加歌单喜欢次数
            userBehaviorMapper.updatePlaylistLikeCount(playlistId, 1);
        } else {
            // 删除喜欢记录
            userBehaviorMapper.deleteUserLike(userId, playlistId, "PLAYLIST");

            // 减少歌单喜欢次数
            userBehaviorMapper.updatePlaylistLikeCount(playlistId, -1);
        }
    }

    @Override
    @Transactional
    public void collectPlaylist(Long userId, Long playlistId, Boolean isCollect) {
        log.info("{}歌单，用户ID：{}，歌单ID：{}", isCollect ? "收藏" : "取消收藏", userId, playlistId);

        if (isCollect) {
            // 添加收藏记录
            UserCollection userCollection = new UserCollection();
            userCollection.setUserId(userId);
            userCollection.setTargetId(playlistId);
            userCollection.setTargetType("PLAYLIST");
            userCollection.setFolderName("默认收藏夹");
            userCollection.setCreateTime(LocalDateTime.now());
            userBehaviorMapper.insertUserCollection(userCollection);
        } else {
            // 删除收藏记录
            userBehaviorMapper.deleteUserCollection(userId, playlistId, "PLAYLIST");
        }
    }

    @Override
    public PageResult<ClientSongVO> getUserLikedSongs(Long userId, Integer page, Integer size) {
        log.info("获取用户喜欢的歌曲，用户ID：{}，页码：{}，每页大小：{}", userId, page, size);

        Page<ClientSongVO> pageObj = new Page<>(page, size);
        Page<ClientSongVO> result = clientMusicMapper.selectUserLikedSongs(pageObj, userId);

        return new PageResult<>(result.getTotal(), result.getRecords(),page,size);

    }

    @Override
    public PageResult<ClientSongVO> getUserCollectedSongs(Long userId, Integer page, Integer size) {
        log.info("获取用户收藏的歌曲，用户ID：{}，页码：{}，每页大小：{}", userId, page, size);

        Page<ClientSongVO> pageObj = new Page<>(page, size);
        Page<ClientSongVO> result = clientMusicMapper.selectUserCollectedSongs(pageObj, userId);

        return new PageResult<>(result.getTotal(), result.getRecords(),page,size);

    }

    @Override
    public PageResult<ClientPlaylistVO> getUserCollectedPlaylists(Long userId, Integer page, Integer size) {
        log.info("获取用户收藏的歌单，用户ID：{}，页码：{}，每页大小：{}", userId, page, size);

        Page<ClientPlaylistVO> pageObj = new Page<>(page, size);
        Page<ClientPlaylistVO> result = clientMusicMapper.selectUserCollectedPlaylists(pageObj, userId);

        return new PageResult<>(result.getTotal(), result.getRecords(),page,size);

    }

    @Override
    public PageResult<ClientSongVO> getSongsByStyle(String style, Long userId, Integer page, Integer size) {
        log.info("按风格获取歌曲，风格：{}，用户ID：{}，页码：{}，每页大小：{}", style, userId, page, size);

        Page<ClientSongVO> pageObj = new Page<>(page, size);
        Page<ClientSongVO> result = clientMusicMapper.selectSongsByStyle(pageObj, style, userId);

        return new PageResult<>(result.getTotal(), result.getRecords(),page,size);

    }

    @Override
    public List<ClientSongVO> getSingerHotSongs(Long singerId, Long userId, Integer limit) {
        log.info("获取歌手热门歌曲，歌手ID：{}，用户ID：{}，数量限制：{}", singerId, userId, limit);

        return clientMusicMapper.selectSingerHotSongs(singerId, userId, limit);
    }
}
