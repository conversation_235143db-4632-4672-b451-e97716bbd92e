<!-- 虚拟滚动表格组件 - 优化大数据量渲染性能 -->
<template>
  <div class="relative border border-gray-200 rounded-lg overflow-hidden" ref="containerRef">
    <div class="bg-gray-50 border-b border-gray-200">
      <div class="flex border-b border-gray-100 transition-colors duration-150 bg-gray-50 font-semibold">
        <div
          v-for="column in columns"
          :key="column.key"
          class="px-4 py-3 text-sm flex-shrink-0 overflow-hidden text-ellipsis whitespace-nowrap font-medium text-gray-700"
          :style="{ width: column.width || 'auto' }"
        >
          {{ column.title }}
        </div>
      </div>
    </div>

    <div
      class="relative overflow-auto"
      ref="scrollRef"
      @scroll="handleScroll"
      :style="{ height: containerHeight + 'px' }"
    >
      <div
        class="relative"
        :style="{ height: totalHeight + 'px' }"
      >
        <div
          class="absolute top-0 left-0 w-full"
          :style="{ transform: `translateY(${offsetY}px)` }"
        >
          <div
            v-for="(item, index) in visibleData"
            :key="getRowKey(item, startIndex + index)"
            class="flex border-b border-gray-100 transition-colors duration-150 hover:bg-blue-50"
            @mouseenter="hoveredIndex = startIndex + index"
            @mouseleave="hoveredIndex = -1"
          >
            <div
              v-for="column in columns"
              :key="column.key"
              class="px-4 py-3 text-sm text-gray-900 flex-shrink-0 overflow-hidden text-ellipsis whitespace-nowrap"
              :style="{ width: column.width || 'auto' }"
            >
              <slot
                :name="column.key"
                :row="item"
                :index="startIndex + index"
                :column="column"
              >
                {{ getColumnValue(item, column.key) }}
              </slot>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75">
      <div class="w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
      <span class="ml-2 text-gray-600">数据加载中...</span>
    </div>
  </div>
</template>

<script setup lang="ts">
// 滚动处理变量
let scrollTimer: ReturnType<typeof setTimeout> | null = null

interface Column {
  key: string
  title: string
  width?: string
}

interface Props {
  data: unknown[]
  columns: Column[]
  rowHeight?: number
  containerHeight?: number
  loading?: boolean
  rowKey?: string | ((row: unknown) => string | number)
}

const props = withDefaults(defineProps<Props>(), {
  rowHeight: 50,
  containerHeight: 400,
  loading: false,
  rowKey: 'id'
})

// 响应式数据
const containerRef = ref<HTMLElement>()
const scrollRef = ref<HTMLElement>()
const scrollTop = ref(0)
const hoveredIndex = ref(-1)

// 计算属性
const totalHeight = computed(() => props.data.length * props.rowHeight)

const visibleCount = computed(() =>
  Math.ceil(props.containerHeight / props.rowHeight) + 2
)

const startIndex = computed(() =>
  Math.max(0, Math.floor(scrollTop.value / props.rowHeight) - 1)
)

const endIndex = computed(() =>
  Math.min(props.data.length, startIndex.value + visibleCount.value)
)

const visibleData = computed(() =>
  props.data.slice(startIndex.value, endIndex.value)
)

const offsetY = computed(() => startIndex.value * props.rowHeight)

// 防抖滚动处理
const handleScroll = (e: Event) => {
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }
  scrollTimer = setTimeout(() => {
    const target = e.target as HTMLElement
    scrollTop.value = target.scrollTop
  }, 16) // 约60fps
}

// 工具函数
const getRowKey = (row: unknown, index: number): string | number => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row)
  }
  const value = (row as Record<string, unknown>)[props.rowKey as string]
  return (typeof value === 'string' || typeof value === 'number') ? value : index
}

const getColumnValue = (row: unknown, key: string): unknown => {
  return key.split('.').reduce((obj, k) => (obj as Record<string, unknown>)?.[k], row)
}

// 暴露方法给父组件
const scrollToTop = () => {
  if (scrollRef.value) {
    scrollRef.value.scrollTop = 0
  }
}

const scrollToIndex = (index: number) => {
  if (scrollRef.value) {
    scrollRef.value.scrollTop = index * props.rowHeight
  }
}

defineExpose({
  scrollToTop,
  scrollToIndex
})
</script>

<style scoped>
/* 虚拟表格组件样式 - 使用Tailwind CSS类 */
</style>
