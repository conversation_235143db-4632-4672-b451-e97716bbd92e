// 仪表板数据类型定义
export interface DashboardOverview {
  userStats: {
    totalUsers: number
    todayNewUsers: number
    activeUsers: number
  }
  contentStats: {
    totalSingers: number
    totalSongs: number
    totalPlaylists: number
  }
  interactionStats: {
    totalPlays: number
    totalLikes: number
    totalCollections: number
  }
  systemStats: {
    totalFeedbacks: number
    pendingFeedbacks: number
    systemStatus: 'normal' | 'warning' | 'error'
  }
}

export interface UserTrendData {
  date: string
  newUsers: number
  activeUsers: number
}

export interface FeedbackStatsData {
  type: string
  count: number
  percentage: number
}

export interface ContentStatsData {
  month: string
  songUploads: number
  singerRegistrations: number
}