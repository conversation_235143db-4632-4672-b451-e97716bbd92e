package com.jpzz.pojo.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 客户端歌手VO
 * <AUTHOR>
 */
@Data
@Schema(description = "客户端歌手信息")
public class ClientSingerVO {

    @Schema(description = "歌手ID")
    private Long singerId;

    @Schema(description = "歌手名称")
    private String singerName;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "生日")
    private LocalDate birth;

    @Schema(description = "地区")
    private String location;

    @Schema(description = "简介")
    private String introduction;

    @Schema(description = "歌曲数量")
    private Integer songsCount;

    @Schema(description = "粉丝数量")
    private Long fansCount;

    @Schema(description = "用户是否关注")
    private Boolean isFollowed;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}
