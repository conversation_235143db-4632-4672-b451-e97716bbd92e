package com.jpzz.service.admin;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jpzz.pojo.dto.admin.UserDTO;
import com.jpzz.pojo.dto.admin.UserQueryDTO;
import com.jpzz.pojo.entity.User;
import com.jpzz.pojo.vo.admin.UserVO;
import com.jpzz.result.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserService extends IService<User> {

    /**
     * 添加用户
     *
     * @param userDTO 用户信息
     * @return 用户Id
     */
    Long addUser(UserDTO userDTO);

    /**
     * 修改用户
     *
     * @param userDTO 用户信息
     */
    void updateUser(UserDTO userDTO);

    PageResult<UserVO> pageUserList(UserQueryDTO userQueryDTO);

    void deleteById(Long userId);

    void deleteByIds(List<Long> userIds);

    void resetPassword(Long userId);

    void changeStatus(Long userId, String status);

    /**
     * 根据用户名重置密码（用于忘记密码功能）
     *
     * @param username    用户名
     * @param newPassword 新密码
     * 是否重置成功
     */
    void resetPasswordByUsername(String username, String newPassword);
}
