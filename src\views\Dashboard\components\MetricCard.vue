<template>
  <div
    v-motion
    :initial="{ opacity: 0, y: 50, scale: 0.9 }"
    :enter="{
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 600,
        delay: delay,
        type: 'spring',
        stiffness: 100
      }
    }"
    class="metric-card enhanced-metric-card relative overflow-hidden"
    :class="cardClass"
    @mouseenter="handleHover"
    @mouseleave="handleLeave"
  >
    <!-- 背景装饰 -->
    <div class="absolute inset-0 opacity-5">
      <div class="absolute top-0 right-0 w-32 h-32 rounded-full" :class="decorationClass"></div>
      <div class="absolute bottom-0 left-0 w-24 h-24 rounded-full" :class="decorationClass"></div>
    </div>

    <!-- 渐变边框效果 -->
    <div class="absolute inset-0 rounded-xl bg-gradient-to-r p-[1px]" :class="borderGradientClass">
      <div class="w-full h-full bg-white rounded-xl"></div>
    </div>

    <!-- 内容区域 -->
    <div class="relative z-10 p-6">
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <!-- 标题区域 -->
          <div class="flex items-center mb-3">
            <div class="w-1 h-6 rounded-full mr-3" :class="accentClass"></div>
            <h3 class="text-sm font-semibold text-gray-600 uppercase tracking-wide">{{ title }}</h3>
          </div>

          <!-- 数值显示 -->
          <div class="mb-4">
            <div class="flex items-baseline">
              <span class="text-4xl font-bold transition-all duration-300 bg-gradient-to-r bg-clip-text text-transparent" :class="textGradientClass" ref="numberRef">
                {{ displayValue }}
              </span>
              <span v-if="unit" class="text-lg text-gray-500 ml-2 font-medium">{{ unit }}</span>
            </div>
          </div>

          <!-- 趋势指标 -->
          <div v-if="trend" class="flex items-center">
            <div class="flex items-center px-3 py-1 rounded-full" :class="trendBgClass">
              <Icon
                :icon="trend.type === 'up' ? 'mdi:trending-up' : 'mdi:trending-down'"
                class="text-sm mr-1"
                :class="trend.type === 'up' ? 'text-green-600' : 'text-red-600'"
              />
              <span class="text-sm font-semibold" :class="trend.type === 'up' ? 'text-green-700' : 'text-red-700'">
                {{ trend.value }}%
              </span>
            </div>
            <span class="text-xs text-gray-500 ml-2 font-medium">{{ trend.period }}</span>
          </div>
        </div>

        <!-- 图标区域 -->
        <div class="flex-shrink-0 relative">
          <!-- 主图标 -->
          <div class="icon-wrapper relative" :class="iconClass">
            <Icon :icon="icon" class="text-3xl text-white relative z-10" />
            <!-- 脉冲效果 -->
            <div class="absolute inset-0 rounded-xl animate-pulse opacity-30" :class="pulseClass"></div>
          </div>

          <!-- 装饰点 -->
          <div class="absolute -top-2 -right-2 w-5 h-5 rounded-full bg-white shadow-lg flex items-center justify-center">
            <div class="w-2.5 h-2.5 rounded-full animate-pulse" :class="accentClass"></div>
          </div>
        </div>
      </div>

      <!-- 底部装饰线 -->
      <div class="absolute bottom-0 left-0 right-0 h-1 rounded-b-xl transition-all duration-300" :class="bottomLineClass"></div>
    </div>

    <!-- 悬停光效 -->
    <div class="absolute inset-0 bg-gradient-to-r opacity-0 transition-opacity duration-300 rounded-xl pointer-events-none" :class="hoverGradientClass"></div>
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'

interface Props {
  title: string
  value: number
  unit?: string
  icon: string
  color: 'blue' | 'green' | 'orange' | 'purple' | 'red'
  trend?: {
    type: 'up' | 'down'
    value: number
    period: string
  }
  delay?: number
}

const props = withDefaults(defineProps<Props>(), {
  delay: 0
})

const displayValue = ref(0)
const isHovered = ref(false)
const numberRef = ref<HTMLElement>()

const cardClass = computed(() => {
  const baseClass = 'transform transition-all duration-500 cursor-pointer'
  const hoverClass = isHovered.value ? 'scale-105 -translate-y-2' : ''
  return `${baseClass} ${hoverClass}`
})

const iconClass = computed(() => {
  const colorMap = {
    blue: 'p-4 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg shadow-blue-200',
    green: 'p-4 rounded-xl bg-gradient-to-br from-green-500 to-green-600 shadow-lg shadow-green-200',
    orange: 'p-4 rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 shadow-lg shadow-orange-200',
    purple: 'p-4 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 shadow-lg shadow-purple-200',
    red: 'p-4 rounded-xl bg-gradient-to-br from-red-500 to-red-600 shadow-lg shadow-red-200'
  }
  return colorMap[props.color]
})

const decorationClass = computed(() => {
  const colorMap = {
    blue: 'bg-gradient-to-br from-blue-400 to-blue-500',
    green: 'bg-gradient-to-br from-green-400 to-green-500',
    orange: 'bg-gradient-to-br from-orange-400 to-orange-500',
    purple: 'bg-gradient-to-br from-purple-400 to-purple-500',
    red: 'bg-gradient-to-br from-red-400 to-red-500'
  }
  return colorMap[props.color]
})

const borderGradientClass = computed(() => {
  const colorMap = {
    blue: 'from-blue-400 to-blue-600',
    green: 'from-green-400 to-green-600',
    orange: 'from-orange-400 to-orange-600',
    purple: 'from-purple-400 to-purple-600',
    red: 'from-red-400 to-red-600'
  }
  return colorMap[props.color]
})

const accentClass = computed(() => {
  const colorMap = {
    blue: 'bg-gradient-to-b from-blue-500 to-blue-600',
    green: 'bg-gradient-to-b from-green-500 to-green-600',
    orange: 'bg-gradient-to-b from-orange-500 to-orange-600',
    purple: 'bg-gradient-to-b from-purple-500 to-purple-600',
    red: 'bg-gradient-to-b from-red-500 to-red-600'
  }
  return colorMap[props.color]
})

const textGradientClass = computed(() => {
  const colorMap = {
    blue: 'from-blue-600 to-blue-800',
    green: 'from-green-600 to-green-800',
    orange: 'from-orange-600 to-orange-800',
    purple: 'from-purple-600 to-purple-800',
    red: 'from-red-600 to-red-800'
  }
  return colorMap[props.color]
})

const trendBgClass = computed(() => {
  if (!props.trend) return ''
  return props.trend.type === 'up'
    ? 'bg-green-50 border border-green-200'
    : 'bg-red-50 border border-red-200'
})

const pulseClass = computed(() => {
  const colorMap = {
    blue: 'bg-blue-400',
    green: 'bg-green-400',
    orange: 'bg-orange-400',
    purple: 'bg-purple-400',
    red: 'bg-red-400'
  }
  return colorMap[props.color]
})

const bottomLineClass = computed(() => {
  const baseClass = accentClass.value
  const hoverClass = isHovered.value ? 'h-2' : 'h-1'
  return `${baseClass} ${hoverClass}`
})

const hoverGradientClass = computed(() => {
  const colorMap = {
    blue: 'from-blue-400/10 to-blue-600/10',
    green: 'from-green-400/10 to-green-600/10',
    orange: 'from-orange-400/10 to-orange-600/10',
    purple: 'from-purple-400/10 to-purple-600/10',
    red: 'from-red-400/10 to-red-600/10'
  }
  const hoverOpacity = isHovered.value ? 'opacity-100' : 'opacity-0'
  return `${colorMap[props.color]} ${hoverOpacity}`
})

// 数字滚动动画
const animateNumber = () => {
  const duration = 1500
  const start = 0
  const end = props.value
  const startTime = Date.now()

  const updateNumber = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)

    // 使用缓动函数
    const easeOutQuart = 1 - Math.pow(1 - progress, 4)
    displayValue.value = Math.floor(start + (end - start) * easeOutQuart)

    if (progress < 1) {
      requestAnimationFrame(updateNumber)
    } else {
      displayValue.value = end
    }
  }

  requestAnimationFrame(updateNumber)
}

const handleHover = () => {
  isHovered.value = true
}

const handleLeave = () => {
  isHovered.value = false
}

onMounted(() => {
  // 延迟启动动画，创造更好的视觉效果
  setTimeout(() => {
    animateNumber()
  }, props.delay + 400)
})
</script>

<style scoped>
/* 🎨 Enhanced Metric Card Styles */
.enhanced-metric-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-metric-card:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border-color: rgba(255, 255, 255, 0.3);
}

.icon-wrapper {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.enhanced-metric-card:hover .icon-wrapper {
  transform: scale(1.1) rotate(5deg);
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.2),
    0 6px 16px rgba(0, 0, 0, 0.1);
}

/* 数字动画效果 */
.enhanced-metric-card [ref="numberRef"] {
  position: relative;
}

.enhanced-metric-card [ref="numberRef"]::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, currentColor, transparent);
  transition: width 0.3s ease;
}

.enhanced-metric-card:hover [ref="numberRef"]::after {
  width: 100%;
}

/* 脉冲动画 */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

.icon-wrapper .animate-pulse {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* 悬停时的光效 */
.enhanced-metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
  border-radius: 1.5rem;
}

.enhanced-metric-card:hover::before {
  left: 100%;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .enhanced-metric-card {
    padding: 1rem;
  }

  .enhanced-metric-card:hover {
    transform: translateY(-1px) scale(1.02);
  }

  .enhanced-metric-card:hover .icon-wrapper {
    transform: scale(1.05) rotate(2deg);
  }
}

/* 加载状态 */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}
</style>


