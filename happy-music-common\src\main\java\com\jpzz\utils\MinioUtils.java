package com.jpzz.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import com.jpzz.properties.MinioProperties;
import io.minio.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MinioUtils {

    @Resource(name = "minioClient")
    private MinioClient minioClient;
    @Resource
    private MinioProperties minioProperties;

    /**
     * 检查存储桶是否存在，不存在则创建
     */
    public void createBucketIfNotExists(String bucketName) {
        try {
            boolean exists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!exists) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                log.info("创建存储桶成功: {}", bucketName);
            }
        } catch (Exception e) {
            log.error("检查/创建存储桶失败: {}", e.getMessage());
            throw new RuntimeException("存储桶操作失败", e);
        }
    }

    /**
     * 上传文件
     *
     * @param file   文件
     * @param folder 文件夹路径（如：avatar/singer/）
     * @return 文件访问URL
     */
    public String uploadFile(MultipartFile file, String folder) {
        try {
            // 检查存储桶
            createBucketIfNotExists(minioProperties.getBucketName());

            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = StrUtil.subAfter(originalFilename, ".", true);
            String fileName = UUID.randomUUID().toString().replace("-", "") + "." + extension;

            // 构建对象名称：folder/yyyy/MM/dd/fileName
            String dateFolder = DateUtil.format(DateUtil.date(), "yyyy/MM/dd");
            String objectName = folder + dateFolder + "/" + fileName;

            // 上传文件
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(minioProperties.getBucketName())
                            .object(objectName)
                            .stream(file.getInputStream(), file.getSize(), -1)
                            .contentType(file.getContentType())
                            .build()
            );

            // 返回文件访问URL
            String fileUrl = minioProperties.getFileHost() + "/" + minioProperties.getBucketName() + "/" + objectName;
            log.info("文件上传成功: {}", fileUrl);
            return fileUrl;

        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage());
            throw new RuntimeException("文件上传失败", e);
        }
    }
    /**
     * 删除文件
     * @param objectName 对象名称
     */
    public void deleteFile(String objectName) {
        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(minioProperties.getBucketName())
                            .object(objectName)
                            .build()
            );
            log.info("文件删除成功: {}", objectName);
        } catch (Exception e) {
            log.error("文件删除失败: {}", e.getMessage());
            throw new RuntimeException("文件删除失败", e);
        }
    }


}
