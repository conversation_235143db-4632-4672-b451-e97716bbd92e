package com.jpzz.service.admin.Impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jpzz.constant.MessageConstant;
import com.jpzz.exception.FeedbackNotFoundException;
import com.jpzz.mapper.admin.FeedbackMapper;
import com.jpzz.pojo.dto.admin.FeedbackQueryDTO;
import com.jpzz.pojo.dto.admin.FeedbackUpdateDTO;
import com.jpzz.pojo.entity.Feedback;
import com.jpzz.pojo.vo.admin.FeedbackVO;
import com.jpzz.result.PageResult;
import com.jpzz.service.admin.FeedbackService;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 反馈服务实现类
 * <AUTHOR>
 */
@Service
@Slf4j
public class FeedbackServiceImpl extends ServiceImpl<FeedbackMapper, Feedback> implements FeedbackService {

    @Resource
    private FeedbackMapper feedbackMapper;

    @Override
    public PageResult<FeedbackVO> pageFeedbackList(FeedbackQueryDTO queryDTO) {
        log.info("分页查询反馈列表，查询参数：{}", queryDTO);

        Page<FeedbackVO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<FeedbackVO> feedbackPage = feedbackMapper.selectFeedbackPage(page, queryDTO);

        return PageResult.<FeedbackVO>builder()
                .total(feedbackPage.getTotal())
                .list(feedbackPage.getRecords())
                .pageNum(queryDTO.getPageNum())
                .pageSize(queryDTO.getPageSize())
                .build();
    }

    @Override
    public FeedbackVO getFeedbackById(Long feedbackId) {
        log.info("查询反馈详情，反馈ID：{}", feedbackId);

        FeedbackVO feedbackVO = feedbackMapper.selectFeedbackById(feedbackId);
        if (ObjectUtil.isNull(feedbackVO)) {
            throw new FeedbackNotFoundException(MessageConstant.FEEDBACK_NOT_FOUND);
        }

        return feedbackVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFeedback(Long feedbackId, FeedbackUpdateDTO updateDTO) {
        log.info("更新反馈，反馈ID：{}，更新信息：{}", feedbackId, updateDTO);

        // 检查反馈是否存在
        Feedback feedback = this.getById(feedbackId);
        if (ObjectUtil.isNull(feedback)) {
            throw new FeedbackNotFoundException(MessageConstant.FEEDBACK_NOT_FOUND);
        }

        // 更新状态
        if (StrUtil.isNotBlank(updateDTO.getStatus())) {
            feedback.setStatus(updateDTO.getStatus());
            log.info("更新反馈状态：{} -> {}", feedbackId, updateDTO.getStatus());
        }

        // 更新管理员回复
        if (StrUtil.isNotBlank(updateDTO.getReply())) {
            feedback.setAdminReply(updateDTO.getReply());
            feedback.setReplyTime(LocalDateTime.now());
            log.info("添加管理员回复：{}", feedbackId);
        }

        this.updateById(feedback);
        log.info("反馈更新成功，反馈ID：{}", feedbackId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long feedbackId) {
        log.info("删除反馈，反馈ID：{}", feedbackId);

        // 检查反馈是否存在
        Feedback feedback = this.getById(feedbackId);
        if (ObjectUtil.isNull(feedback)) {
            throw new FeedbackNotFoundException(MessageConstant.FEEDBACK_NOT_FOUND);
        }

        // MyBatis-Plus软删除
        this.removeById(feedbackId);
        log.info("反馈删除成功，反馈ID：{}", feedbackId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> feedbackIds) {
        log.info("批量删除反馈，反馈ID列表：{}", feedbackIds);

        if (ObjectUtil.isEmpty(feedbackIds)) {
            log.warn("批量删除反馈失败：反馈ID列表为空");
            return;
        }

        // MyBatis-Plus批量软删除
        this.removeByIds(feedbackIds);
        log.info("批量删除反馈成功，删除数量：{}", feedbackIds.size());
    }
}