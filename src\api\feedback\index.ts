import type { FeedbackInfo, FeedbackQueryParams } from '@/types/feedback'
import request from '@/utils/request'

export interface PageResult<T> {
  total: number
  list: T[]
}

/**
 * 获取反馈列表（分页）
 * @param params 查询参数
 * @returns 反馈列表分页数据
 */
export function getFeedbackList(params: FeedbackQueryParams): Promise<PageResult<FeedbackInfo>> {
  return request.get('/feedback/list', params)
}

/**
 * 获取反馈详情
 * @param feedbackId 反馈ID
 * @returns 反馈详情
 */
export function getFeedbackInfo(feedbackId: number): Promise<FeedbackInfo> {
  return request.get(`/feedback/${feedbackId}`)
}

/**
 * 回复反馈
 * @param feedbackId 反馈ID
 * @param reply 回复内容
 * @returns 操作结果
 */
export function replyFeedback(feedbackId: number, reply: string): Promise<string> {
  return request.post(`/feedback/${feedbackId}/reply`, { reply })
}

/**
 * 更新反馈状态
 * @param feedbackId 反馈ID
 * @param status 新状态
 * @returns 操作结果
 */
export function updateFeedbackStatus(feedbackId: number, status: string): Promise<string> {
  return request.put(`/feedback/${feedbackId}/status`, { status })
}

/**
 * 删除反馈
 * @param feedbackId 反馈ID
 * @returns 操作结果
 */
export function deleteFeedback(feedbackId: number): Promise<string> {
  return request.delete(`/feedback/${feedbackId}`)
}

/**
 * 批量删除反馈
 * @param feedbackIds 反馈ID数组
 * @returns 操作结果
 */
export function batchDeleteFeedback(feedbackIds: number[]): Promise<string> {
  return request.post('/feedback/batch-delete', {feedbackIds})
}
