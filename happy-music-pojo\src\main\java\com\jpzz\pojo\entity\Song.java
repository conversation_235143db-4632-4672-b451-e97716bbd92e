package com.jpzz.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 歌曲实体类
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_song")
@Schema(description = "歌曲实体")
public class Song implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "歌曲ID")
    private Long id;

    @Schema(description = "歌手ID")
    private Long artistId;

    @Schema(description = "歌曲名称")
    private String name;

    @Schema(description = "专辑名称")
    private String album;

    @Schema(description = "歌词")
    private String lyric;

    @Schema(description = "时长")
    private String duration;

    @Schema(description = "风格")
    private String style;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "音频URL")
    private String audioUrl;

    @Schema(description = "发行时间")
    private String releaseTime;

    @Schema(description = "点赞数")
    private Integer likeCount;
    @TableLogic
    @Schema(description = "逻辑删除标记(0-未删除,1-已删除)", hidden = true)
    private Integer deleted;

    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
