package com.jpzz.pojo.dto.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 歌曲数据传输对象
 * <AUTHOR>
 */
@Data
@Schema(description = "歌曲数据传输对象")
public class SongDTO {

    @Schema(description = "歌曲ID（修改时必填）", example = "1")
    private Long id;

    @NotNull(message = "歌手ID不能为空")
    @Schema(description = "歌手ID", example = "1")
    private Long artistId;

    @Schema(description = "歌手名称", example = "周杰伦")
    private String artistName;

    @NotBlank(message = "歌曲名称不能为空")
    @Size(max = 100, message = "歌曲名称长度不能超过100个字符")
    @Schema(description = "歌曲名称", example = "青花瓷")
    private String name;

    @Schema(description = "专辑名称", example = "我很忙")
    private String album;

    @Schema(description = "歌词")
    private String lyric;

    @Schema(description = "时长", example = "4:20")
    private String duration;

    @Schema(description = "风格", example = "流行")
    private String style;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "音频URL")
    private String audioUrl;

    @NotBlank(message = "发行时间不能为空")
    @Schema(description = "发行时间", example = "2007-11-01")
    private String releaseTime;

    @Schema(description = "点赞数", example = "1000")
    private Integer likeCount;
}