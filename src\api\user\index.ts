import type { UserInfo, UserQueryParams } from "@/types/user";
import request from "@/utils/request";


export interface PageResult<T> {
  total: number;
  list: T[];
}

/**
 * 获取用户列表（分页）
 * @param params 查询参数
 * @returns 用户列表分页数据
 */
export function getUserList(params: UserQueryParams): Promise<PageResult<UserInfo>> {
  return request.get('/user/list', params)
}

/**
 * 获取用户详情
 * @param userId 用户ID
 * @returns 用户详情
 */
export function getUserInfo(userId: number): Promise<UserInfo> {
  return request.get(`/user/${userId}`)
}
/**
 * 添加用户
 * @param data 用户数据
 * @returns 操作结果
 */
export function addUser(data: Partial<UserInfo>): Promise<unknown> {
  return request.post('/user', data)
}
/**
 * 更新用户信息
 * @param data 用户数据
 * @returns 操作结果
 */
export function updateUser(data: Partial<UserInfo>): Promise<unknown> {
  return request.put('/user', data)
}

/**
 * 删除用户
 * @param userId 用户ID
 * @returns 操作结果
 */
export function deleteUser(userId: number): Promise<unknown> {
  return request.delete(`/user/${userId}`)
}

/**
 * 更新用户状态
 * @param userId 用户ID
 * @param status 状态值
 * @returns 操作结果
 */
export function updateUserStatus(userId: number, status: string): Promise<unknown> {
  return request.put('/user/status', { userId, status })
}

/**
 * 重置用户密码
 * @param userId 用户ID
 * @returns 操作结果
 */
export function resetUserPassword(userId: number): Promise<unknown> {
  return request.put(`/user/reset-password/${userId}`)
}

/**
 * 忘记密码 - 通过安全问题重置密码
 * @param username 用户名
 * @param answer 安全问题答案
 * @param newPassword 新密码
 * @returns 重置结果
 */
export function forgotPassword(username: string, answer: string, newPassword: string): Promise<void> {
  return request.post('/user/forgot-password', {
    username,
    answer,
    newPassword
  })
}


/**
 * 批量删除用户
 * @param userIds 用户ID数组
 * @returns 操作结果
 */
export function batchDeleteUsers(userIds: number[]): Promise<unknown> {
  return request.post("/user/batch-delete", userIds)
}




