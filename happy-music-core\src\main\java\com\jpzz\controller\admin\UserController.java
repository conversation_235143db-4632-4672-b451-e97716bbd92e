package com.jpzz.controller.admin;


import cn.hutool.core.convert.Convert;
import com.jpzz.constant.AnswerConstant;
import com.jpzz.pojo.dto.admin.UserDTO;
import com.jpzz.pojo.dto.admin.UserQueryDTO;
import com.jpzz.pojo.vo.admin.UserVO;
import com.jpzz.result.PageResult;
import com.jpzz.result.Result;
import com.jpzz.service.admin.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Tag(name = "用户管理", description = "用户相关接口")
@RestController
@Slf4j
@RequestMapping("/api/user")
@SuppressWarnings("all")
public class UserController {

    @Resource
    private UserService userService;

    @Operation(summary = "添加用户", description = "添加用户")
    @PostMapping()
    public Result<Long> addUser(@Validated @RequestBody UserDTO userDTO) {
        Long userId = userService.addUser(userDTO);
        log.info("添加用户成功，用户ID：{}", userId);
        return Result.success("添加成功", userId);
    }

    @Operation(summary = "修改用户", description = "修改用户信息")
    @PutMapping()
    public Result<Void> updateUser(@Validated @RequestBody UserDTO userDTO) {
        log.info("修改用户，用户:{}", userDTO);
        userService.updateUser(userDTO);
        log.info("修改用户成功，用户:{}", userDTO);
        return Result.success("修改成功!");
    }


    @Operation(summary = "查询用户", description = "查询用户信息")
    @GetMapping("/list")
    public Result<PageResult<UserVO>> pageUserList(@Validated UserQueryDTO userQueryDTO) {
        PageResult<UserVO> pageResult = userService.pageUserList(userQueryDTO);
        log.info("查询用户成功，用户列表:{}", pageResult.getList());
        return Result.success(pageResult);
    }

    @Operation(summary = "删除用户", description = "删除用户信息")
    @DeleteMapping("/{userId}")
    public Result<Void> deleteUser(@Validated @PathVariable Long userId) {
        userService.deleteById(userId);
        log.info("删除用户成功，用户ID:{}", userId);
        return Result.success("删除成功!");
    }

    /**
     * 批量删除用户
     *
     * @param ids 用户ID列表
     * @return 删除结果
     */
    @Operation(summary = "批量删除用户", description = "批量删除多个用户")
    @PostMapping("/batch-delete")
    public Result<Void> batchDeleteUsers(@Validated @RequestBody Integer[] userIds) {
        log.info("批量删除用户，用户ID列表：{}", userIds);
        List<Long> UserIdsList = Convert.toList(Long.class, userIds);
        userService.deleteByIds(UserIdsList);
        return Result.success("删除成功");
    }

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @return 重置结果
     */
    @Operation(summary = "重置用户密码", description = "将用户密码重置为默认密码")
    @PutMapping("/reset-password/{userId}")
    public Result<Void> resetPassword(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        log.info("重置用户密码：{}", userId);
        userService.resetPassword(userId);
        return Result.success("密码重置成功");
    }

    /**
     * 修改用户状态
     *
     * @param params 参数，包含userId和status
     * @return 修改结果
     */
    @Operation(summary = "修改用户状态", description = "启用或禁用用户")
    @PutMapping("/status")
    public Result<Void> changeStatus(@RequestBody Map<String, Object> params) {
        Long userId = Long.valueOf(params.get("userId").toString());
        String status = params.get("status").toString();
        log.info("修改用户状态：{}，状态：{}", userId, status);
        userService.changeStatus(userId, status);
        return Result.success("状态修改成功");
    }

    /**
     * 忘记密码 - 通过安全问题重置密码
     *
     * @param params 参数，包含username、answer和newPassword
     * @return 重置结果
     */
    @Operation(summary = "忘记密码", description = "通过安全问题重置密码")
    @PostMapping("/forgot-password")
    public Result<Void> forgotPassword(@RequestBody Map<String, String> params) {
        String username = params.get("username");
        String answer = params.get("answer");
        String newPassword = params.get("newPassword");

        log.info("用户 {} 尝试重置密码，答案：{},新密码:{}", username, answer, newPassword);

        // 验证安全问题答案
        if (!AnswerConstant.YJP.equalsIgnoreCase(answer.trim())
                && !AnswerConstant.JPZZ.equalsIgnoreCase(answer.trim())
                && !AnswerConstant.JIAPENG.equalsIgnoreCase(answer.trim())) {
            return Result.error("安全问题答案错误");
        }
        // 验证新密码
        if (newPassword == null || newPassword.trim().length() < 6) {
            return Result.error("新密码长度不能少于6位");
        }
        // 重置密码
        userService.resetPasswordByUsername(username, newPassword);

        log.info("用户 {} 密码重置成功", username);
        return Result.success("密码重置成功");
    }


    //数据大量时,使用批量删除不太好
  /*  @Operation(summary = "批量删除用户", description = "批量删除用户信息")
    @DeleteMapping("/batch/{ids}")
    public Result<Void> BatchDeleteUser(@Validated @PathVariable List<Long> ids) {
        userService.deleteByIds(ids);
        log.info("批量删除用户成功，用户ID:{}", ids);
        return Result.success("批量删除成功!");
    }*/


}
