package com.jpzz.service;

import com.jpzz.pojo.dto.admin.LoginDTO;
import com.jpzz.pojo.vo.admin.UserLoginVO;
import com.jpzz.service.common.AuthService;
import com.jpzz.service.common.CacheService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class LogoutTest {

    @Resource
    private AuthService authService;

    @Resource
    private CacheService cacheService;

    @Test
    public void testLogout() {
        try {
            // 第一步：登录测试用户获取token
            LoginDTO loginDTO = new LoginDTO();
            loginDTO.setUsername("admin");
            loginDTO.setPassword("123456");

            UserLoginVO result = authService.login(loginDTO);
            System.out.println("登录成功: " + result);

            // 第二步：验证token已缓存
            String cachedToken = cacheService.getUserToken(result.getUserId());
            System.out.println("缓存中的token: " + cachedToken);

            // 第三步：注销
            authService.logout(result.getUserId());

            // 第四步：验证token已从缓存中移除
            cachedToken = cacheService.getUserToken(result.getUserId());
            System.out.println("注销后缓存中的token: " + (cachedToken == null ? "已清除" : cachedToken));

        } catch (Exception e) {
            System.out.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}