package com.jpzz.pojo.dto.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 歌单查询DTO
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "歌单查询DTO")
public class PlaylistQueryDTO extends PageQueryDTO {

    @Schema(description = "歌单名称(模糊查询)", example = "我的最爱")
    private String name;

    @Schema(description = "歌单风格", example = "流行")
    private String style;

    @Schema(description = "开始时间", example = "2023-01-01 00:00:00")
    private String beginTime;

    @Schema(description = "结束时间", example = "2023-12-31 23:59:59")
    private String endTime;
}

