<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.jpzz</groupId>
    <artifactId>happy-music-server</artifactId>
    <version>0.0.1-SNAPSHOT</version>

    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <name>happy-music-server</name>
    <description>happy-music-server</description>

    <modules>
        <module>happy-music-common</module>
        <module>happy-music-core</module>
        <module>happy-music-pojo</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <lombok.version>1.18.36</lombok.version>
        <mysql.version>8.0.33</mysql.version>
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <mybatis-plus-jsqlparser.version>3.5.12</mybatis-plus-jsqlparser.version>
        <druid.version>1.2.16</druid.version>
        <knife4j.version>4.5.0</knife4j.version>
        <minio.version>8.5.2</minio.version>
        <jwt.version>4.4.0</jwt.version>
        <hutool-core.version>5.8.20</hutool-core.version>

    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <scope>runtime</scope>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!--分页插件 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus-jsqlparser.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>


            <!--工具类 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-core</artifactId>
                <version>${hutool-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <optional>true</optional>
                <version>${lombok.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>
</project>
