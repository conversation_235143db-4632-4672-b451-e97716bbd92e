// 歌曲信息接口
export interface SongInfo {
  id: number
  artistId: number
  artistName?: string
  name: string
  album: string
  lyric?: string
  duration?: string
  style?: string
  coverUrl?: string
  audioUrl?: string
  releaseTime: string
  likeCount?: number
}

// 歌曲查询参数
export interface SongQueryParams {
  pageNum: number
  pageSize: number
  name?: string
  artistId?: number
  artistName?: string
  style?: string
  album?: string
}

// 歌曲风格枚举（根据你的业务需求调整）
export const SongStyles = [
  '华语流行',
  '粤语流行',
  '欧美流行',
  '日韩流行',
  '嘻哈说唱',
  '纯音乐',
  '民谣',
  '古典',
  '国风'
] as const

export type SongStyle = typeof SongStyles[number]


