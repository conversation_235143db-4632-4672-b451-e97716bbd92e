export interface SingerInfo {
    singerId: number;
    singerName: string;
    avatar?: string;
    type: string;
    birth?: string;
    location?: string;
    introduction?: string;
    createTime: string;
    updateTime: string;
    songsCount?: number;
    fansCount?: number;
}

export interface SingerQueryParams {
    singerName?: string;
    type?: string;
    location?: string;
    pageNum: number;
    pageSize: number;
    beginTime?: string;
    endTime?: string;
}

export const SingerType = {
    MALE: '男歌手',
    FEMALE: '女歌手',
    GROUP: '组合',
} as const;

export type SingerTypeValue = typeof SingerType[keyof typeof SingerType];

export const SingerLocations = [
    '中国大陆',
    '中国香港',
    '中国台湾',
    '日本',
    '韩国',
    '美国',
    '英国',
    '其他'
] as const;
