package com.jpzz.pojo.dto.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 歌单歌曲查询DTO
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "歌单歌曲查询DTO")
public class PlaylistSongQueryDTO extends PageQueryDTO {

    @Schema(description = "歌单ID")
    private Long playlistId;

    @Schema(description = "歌曲名称(模糊查询)", example = "青花瓷")
    private String name;

    @Schema(description = "歌手名称(模糊查询)", example = "周杰伦")
    private String artistName;

    @Schema(description = "音乐风格", example = "流行")
    private String style;
}

