<template>
  <div class="chart-container h-full">
    <v-chart
      v-if="!loading && chartData"
      :option="chartOption"
      class="h-full"
      autoresize
    />
    <div v-else-if="loading" class="flex items-center justify-center h-full">
      <el-icon class="animate-spin text-2xl text-gray-400">
        <Loading />
      </el-icon>
    </div>
    <div v-else class="flex items-center justify-center h-full text-gray-400">
      暂无数据
    </div>
  </div>
</template>

<script setup lang="ts">
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { Loading } from '@element-plus/icons-vue'
import type { FeedbackStatsData } from '@/types/dashboard'

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TitleComponent,
  TooltipComponent,
  LegendComponent
])

interface Props {
  data: FeedbackStatsData[]
  loading?: boolean
}

const props = defineProps<Props>()

const chartData = computed(() => {
  if (!props.data || props.data.length === 0) return null

  return props.data.map(item => ({
    name: item.type,
    value: item.count
  }))
})

const chartOption = computed(() => {
  if (!chartData.value) return {}

  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e4e7ed',
      borderWidth: 1,
      textStyle: {
        color: '#606266'
      }
    },
    legend: {
      orient: 'horizontal',
      bottom: 0,
      textStyle: {
        color: '#606266'
      }
    },
    series: [
      {
        name: '反馈类型',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '45%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 16,
            fontWeight: 'bold'
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: false
        },
        data: chartData.value,
        color: ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399']
      }
    ]
  }
})
</script>
