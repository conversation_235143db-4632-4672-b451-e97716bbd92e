package com.jpzz.controller.admin;

import cn.hutool.core.convert.Convert;
import com.jpzz.pojo.dto.admin.FeedbackQueryDTO;
import com.jpzz.pojo.dto.admin.FeedbackUpdateDTO;
import com.jpzz.pojo.vo.admin.FeedbackVO;
import com.jpzz.result.PageResult;
import com.jpzz.result.Result;
import com.jpzz.service.admin.FeedbackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 反馈管理控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/feedback")
@Tag(name = "反馈管理", description = "反馈相关接口")
@Slf4j
public class FeedbackController {

    @Resource
    private FeedbackService feedbackService;

    @GetMapping("/list")
    @Operation(summary = "分页查询反馈列表")
    public Result<PageResult<FeedbackVO>> list(FeedbackQueryDTO feedbackQueryDTO) {
        log.info("分页查询反馈列表，查询参数：{}", feedbackQueryDTO);
        PageResult<FeedbackVO> pageResult = feedbackService.pageFeedbackList(feedbackQueryDTO);
        return Result.success("查询成功", pageResult);
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询反馈详情")
    public Result<FeedbackVO> getById(@Parameter(description = "反馈ID") @PathVariable Long id) {
        log.info("根据ID查询反馈详情，反馈ID：{}", id);
        FeedbackVO feedbackVO = feedbackService.getFeedbackById(id);
        return Result.success("查询成功", feedbackVO);
    }

    @PostMapping("/{id}/reply")
    @Operation(summary = "回复反馈")
    public Result<Void> reply(
            @Parameter(description = "反馈ID") @PathVariable Long id,
            @RequestBody FeedbackUpdateDTO feedbackUpdateDTO) {
        log.info("回复反馈，反馈ID：{}，回复内容：{}", id, feedbackUpdateDTO.getReply());

        feedbackService.updateFeedback(id, feedbackUpdateDTO);
        return Result.success("回复反馈成功");
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "更新反馈状态")
    public Result<Void> updateStatus(
            @Parameter(description = "反馈ID") @PathVariable Long id,
            @RequestBody FeedbackUpdateDTO feedbackUpdateDTO) {
        log.info("更新反馈状态，反馈ID：{}，新状态：{}", id, feedbackUpdateDTO.getStatus());
        feedbackService.updateFeedback(id, feedbackUpdateDTO);
        return Result.success("状态更新成功");
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除反馈")
    public Result<Void> deleteById(@Parameter(description = "反馈ID") @PathVariable Long id) {
        log.info("删除反馈，反馈ID：{}", id);
        feedbackService.deleteById(id);
        return Result.success("删除成功");
    }

    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除反馈")
    public Result<Void> batchDelete(@RequestBody Integer[] feedbackIds) {
        log.info("批量删除反馈，反馈ID列表：{}", feedbackIds);
        List<Long> feedbackIdsList = Convert.toList(Long.class, feedbackIds);
        feedbackService.deleteByIds(feedbackIdsList);
        return Result.success("批量删除成功");
    }
}
