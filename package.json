{"name": "happy-music-admin", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iconify/json": "^2.2.363", "@iconify/vue": "^5.0.0", "@vueuse/motion": "^3.0.3", "axios": "^1.10.0", "echarts": "^5.6.0", "element-plus": "^2.10.3", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vue": "^3.5.17", "vue-echarts": "^7.0.3", "vue-router": "^4.5.1", "xlsx": "^0.18.5"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "prettier": "3.5.3", "sass": "^1.89.2", "stylelint": "^16.21.1", "stylelint-config-recommended": "^16.0.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-tailwindcss": "^1.0.0", "tailwindcss": "^3.4.17", "typescript": "~5.8.0", "unplugin-auto-import": "^19.3.0", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}