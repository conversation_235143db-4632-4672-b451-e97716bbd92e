package com.jpzz.mapper.client;

import com.jpzz.pojo.entity.PlayRecord;
import com.jpzz.pojo.entity.UserLike;
import com.jpzz.pojo.entity.UserCollection;
import com.jpzz.pojo.entity.RecentPlay;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户行为Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface UserBehaviorMapper {

    /**
     * 插入播放记录
     * @param playRecord 播放记录
     * @return 影响行数
     */
    int insertPlayRecord(PlayRecord playRecord);

    /**
     * 插入或更新最近播放
     * @param recentPlay 最近播放记录
     * @return 影响行数
     */
    int insertOrUpdateRecentPlay(RecentPlay recentPlay);

    /**
     * 插入用户喜欢记录
     * @param userLike 用户喜欢记录
     * @return 影响行数
     */
    int insertUserLike(UserLike userLike);

    /**
     * 删除用户喜欢记录
     * @param userId 用户ID
     * @param targetId 目标ID
     * @param targetType 目标类型
     * @return 影响行数
     */
    int deleteUserLike(@Param("userId") Long userId,
                       @Param("targetId") Long targetId,
                       @Param("targetType") String targetType);

    /**
     * 插入用户收藏记录
     * @param userCollection 用户收藏记录
     * @return 影响行数
     */
    int insertUserCollection(UserCollection userCollection);

    /**
     * 删除用户收藏记录
     * @param userId 用户ID
     * @param targetId 目标ID
     * @param targetType 目标类型
     * @return 影响行数
     */
    int deleteUserCollection(@Param("userId") Long userId,
                             @Param("targetId") Long targetId,
                             @Param("targetType") String targetType);

    /**
     * 更新歌曲喜欢次数
     * @param songId 歌曲ID
     * @param increment 增量(1或-1)
     * @return 影响行数
     */
    int updateSongLikeCount(@Param("songId") Long songId, @Param("increment") Integer increment);

    /**
     * 更新歌曲播放次数
     * @param songId 歌曲ID
     * @param increment 增量
     * @return 影响行数
     */
    int updateSongPlayCount(@Param("songId") Long songId, @Param("increment") Integer increment);

    /**
     * 更新歌单喜欢次数
     * @param playlistId 歌单ID
     * @param increment 增量(1或-1)
     * @return 影响行数
     */
    int updatePlaylistLikeCount(@Param("playlistId") Long playlistId, @Param("increment") Integer increment);

    /**
     * 更新歌单播放次数
     * @param playlistId 歌单ID
     * @param increment 增量
     * @return 影响行数
     */
    int updatePlaylistPlayCount(@Param("playlistId") Long playlistId, @Param("increment") Integer increment);

    /**
     * 获取用户播放总时长
     * @param userId 用户ID
     * @return 总播放时长(秒)
     */
    Long getUserTotalPlayDuration(@Param("userId") Long userId);

    /**
     * 获取用户播放歌曲总数
     * @param userId 用户ID
     * @return 播放歌曲总数
     */
    Long getUserPlaySongCount(@Param("userId") Long userId);

    /**
     * 获取用户最常听的风格
     * @param userId 用户ID
     * @param limit 数量限制
     * @return 风格列表
     */
    java.util.List<String> getUserPreferredStyles(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 获取用户最常听的歌手
     * @param userId 用户ID
     * @param limit 数量限制
     * @return 歌手ID列表
     */
    java.util.List<Long> getUserPreferredSingers(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 清理用户最近播放记录(保留最新的N条)
     * @param userId 用户ID
     * @param keepCount 保留数量
     * @return 影响行数
     */
    int cleanUserRecentPlays(@Param("userId") Long userId, @Param("keepCount") Integer keepCount);
}
