package com.jpzz.mapper.client;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jpzz.pojo.vo.client.ClientSongVO;
import com.jpzz.pojo.vo.client.ClientPlaylistVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户端音乐Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface ClientMusicMapper {

    /**
     * 基于用户偏好推荐歌曲
     * @param page 分页对象
     * @param userId 用户ID
     * @return 推荐歌曲列表
     */
    Page<ClientSongVO> selectRecommendSongsByUserPreference(Page<ClientSongVO> page, @Param("userId") Long userId);

    /**
     * 获取热门歌曲
     * @param page 分页对象
     * @return 热门歌曲列表
     */
    Page<ClientSongVO> selectHotSongs(Page<ClientSongVO> page);

    /**
     * 获取新歌推荐
     * @param page 分页对象
     * @return 新歌列表
     */
    Page<ClientSongVO> selectNewSongs(Page<ClientSongVO> page);

    /**
     * 获取热门歌单
     * @param page 分页对象
     * @return 热门歌单列表
     */
    Page<ClientPlaylistVO> selectHotPlaylists(Page<ClientPlaylistVO> page);

    /**
     * 根据ID获取歌曲详情
     * @param songId 歌曲ID
     * @param userId 用户ID
     * @return 歌曲详情
     */
    ClientSongVO selectSongDetailById(@Param("songId") Long songId, @Param("userId") Long userId);

    /**
     * 根据ID获取歌单详情
     * @param playlistId 歌单ID
     * @param userId 用户ID
     * @return 歌单详情
     */
    ClientPlaylistVO selectPlaylistDetailById(@Param("playlistId") Long playlistId, @Param("userId") Long userId);

    /**
     * 获取歌单中的歌曲
     * @param page 分页对象
     * @param playlistId 歌单ID
     * @param userId 用户ID
     * @return 歌曲列表
     */
    Page<ClientSongVO> selectPlaylistSongs(Page<ClientSongVO> page,
                                           @Param("playlistId") Long playlistId,
                                           @Param("userId") Long userId);

    /**
     * 搜索歌曲
     * @param page 分页对象
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @return 搜索结果
     */
    Page<ClientSongVO> searchSongs(Page<ClientSongVO> page,
                                   @Param("keyword") String keyword,
                                   @Param("userId") Long userId);

    /**
     * 搜索歌单
     * @param page 分页对象
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @return 搜索结果
     */
    Page<ClientPlaylistVO> searchPlaylists(Page<ClientPlaylistVO> page,
                                           @Param("keyword") String keyword,
                                           @Param("userId") Long userId);

    /**
     * 获取用户最近播放
     * @param page 分页对象
     * @param userId 用户ID
     * @return 最近播放列表
     */
    Page<ClientSongVO> selectRecentPlays(Page<ClientSongVO> page, @Param("userId") Long userId);

    /**
     * 获取用户喜欢的歌曲
     * @param page 分页对象
     * @param userId 用户ID
     * @return 喜欢的歌曲列表
     */
    Page<ClientSongVO> selectUserLikedSongs(Page<ClientSongVO> page, @Param("userId") Long userId);

    /**
     * 获取用户收藏的歌曲
     * @param page 分页对象
     * @param userId 用户ID
     * @return 收藏的歌曲列表
     */
    Page<ClientSongVO> selectUserCollectedSongs(Page<ClientSongVO> page, @Param("userId") Long userId);

    /**
     * 获取用户收藏的歌单
     * @param page 分页对象
     * @param userId 用户ID
     * @return 收藏的歌单列表
     */
    Page<ClientPlaylistVO> selectUserCollectedPlaylists(Page<ClientPlaylistVO> page, @Param("userId") Long userId);

    /**
     * 根据风格获取歌曲
     * @param page 分页对象
     * @param style 音乐风格
     * @param userId 用户ID
     * @return 歌曲列表
     */
    Page<ClientSongVO> selectSongsByStyle(Page<ClientSongVO> page,
                                          @Param("style") String style,
                                          @Param("userId") Long userId);

    /**
     * 获取歌手的热门歌曲
     * @param singerId 歌手ID
     * @param userId 用户ID
     * @param limit 数量限制
     * @return 热门歌曲列表
     */
    List<ClientSongVO> selectSingerHotSongs(@Param("singerId") Long singerId,
                                            @Param("userId") Long userId,
                                            @Param("limit") Integer limit);

    /**
     * 检查用户是否喜欢歌曲
     * @param userId 用户ID
     * @param songId 歌曲ID
     * @return 是否喜欢
     */
    Boolean checkUserLikeSong(@Param("userId") Long userId, @Param("songId") Long songId);

    /**
     * 检查用户是否收藏歌曲
     * @param userId 用户ID
     * @param songId 歌曲ID
     * @return 是否收藏
     */
    Boolean checkUserCollectSong(@Param("userId") Long userId, @Param("songId") Long songId);

    /**
     * 检查用户是否喜欢歌单
     * @param userId 用户ID
     * @param playlistId 歌单ID
     * @return 是否喜欢
     */
    Boolean checkUserLikePlaylist(@Param("userId") Long userId, @Param("playlistId") Long playlistId);

    /**
     * 检查用户是否收藏歌单
     * @param userId 用户ID
     * @param playlistId 歌单ID
     * @return 是否收藏
     */
    Boolean checkUserCollectPlaylist(@Param("userId") Long userId, @Param("playlistId") Long playlistId);
}
