package com.jpzz.pojo.vo.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户视图对象
 * <AUTHOR>
 */
@Data
@Schema(description = "用户信息视图对象")
public class UserVO {

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "用户名", example = "admin")
    private String username;

    @Schema(description = "手机号", example = "13800000000")
    private String phone;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "个人介绍", example = "系统管理员")
    private String introduction;

    @Schema(description = "角色(ADMIN-管理员,USER-普通用户)", example = "ADMIN")
    private String role;

    @Schema(description = "状态(0-禁用,1-启用)", example = "1")
    private String status;

    @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateTime;
}