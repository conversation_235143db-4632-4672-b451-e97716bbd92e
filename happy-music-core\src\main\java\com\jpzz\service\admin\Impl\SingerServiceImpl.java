package com.jpzz.service.admin.Impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jpzz.constant.MessageConstant;
import com.jpzz.exception.SingerExistedException;
import com.jpzz.exception.SingerNotFoundException;
import com.jpzz.mapper.admin.SingerMapper;
import com.jpzz.pojo.dto.admin.SingerDTO;
import com.jpzz.pojo.dto.admin.SingerQueryDTO;
import com.jpzz.pojo.entity.Singer;
import com.jpzz.pojo.vo.admin.SingerVO;
import com.jpzz.result.PageResult;
import com.jpzz.service.admin.SingerService;
import com.jpzz.service.admin.SongService;
import com.jpzz.utils.DateTimeUtils;
import com.jpzz.utils.MinioUtils;
import com.jpzz.utils.PageUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SingerServiceImpl extends ServiceImpl<SingerMapper, Singer> implements SingerService {

    @Resource
    private MinioUtils minioUtils;

    @Resource
    private SongService songService;

    @Override
    public PageResult<SingerVO> pageSingerList(SingerQueryDTO singerQueryDTO) {

        LocalDateTime[] times = DateTimeUtils.parseTimeRange(singerQueryDTO.getBeginTime(), singerQueryDTO.getEndTime());
        LocalDateTime beginTime = times[0];
        LocalDateTime endTime = times[1];
        Page<Singer> p = Page.of(singerQueryDTO.getPageNum(), singerQueryDTO.getPageSize());
        Page<Singer> page = this.lambdaQuery()
                .like(StrUtil.isNotBlank(singerQueryDTO.getSingerName()), Singer::getSingerName, singerQueryDTO.getSingerName())
                .eq(StrUtil.isNotBlank(singerQueryDTO.getType()), Singer::getType, singerQueryDTO.getType())
                .eq(StrUtil.isNotBlank(singerQueryDTO.getLocation()), Singer::getLocation, singerQueryDTO.getLocation())
                .ge(beginTime != null, Singer::getCreateTime, beginTime)
                .le(endTime != null, Singer::getCreateTime, endTime)
                .orderByDesc(Singer::getFansCount)
                .orderByDesc(Singer::getCreateTime)
                .page(p);

        return PageUtils.convertToPageResult(
                page,
                this::convertToSingerVO,
                singerQueryDTO.getPageNum(),
                singerQueryDTO.getPageSize());
    }

    @Override
    public SingerVO getSingerById(Long singerId) {
        Singer singer = this.getById(singerId);
        if (ObjectUtil.isNull(singer)) {
            throw new SingerNotFoundException(MessageConstant.SINGER_NOT_FOUND);
        }
        return convertToSingerVO(singer);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addSinger(SingerDTO singerDTO) {
        Singer existSinger = this.lambdaQuery()
                .eq(StrUtil.isNotBlank(singerDTO.getSingerName()), Singer::getSingerName, singerDTO.getSingerName()).one();
        if (ObjectUtil.isNotNull(existSinger)) {
            throw new SingerExistedException(MessageConstant.SINGER_EXISTS);
        }

        Singer singer = BeanUtil.copyProperties(singerDTO, Singer.class);
        // 设置默认值
        if (ObjUtil.isNull(singer.getSongsCount())) {
            singer.setSongsCount(0);
        }
        if (ObjUtil.isNull(singer.getFansCount())) {
            singer.setFansCount(0L);
        }
        this.save(singer);
        return singer.getSingerId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSinger(SingerDTO singerDTO) {
        Singer existSinger = this.getById(singerDTO.getSingerId());
        if (ObjectUtil.isNull(existSinger)) {
            throw new SingerNotFoundException(MessageConstant.SINGER_NOT_FOUND);
        }
        if (!StrUtil.equals(existSinger.getSingerName(), singerDTO.getSingerName())) {
            Singer singer = this.lambdaQuery().eq(Singer::getSingerName, singerDTO.getSingerName()).one();
            if (ObjectUtil.isNotNull(singer)) {
                throw new SingerExistedException(MessageConstant.SINGER_EXISTS);
            }
        }
        Singer singer = BeanUtil.copyProperties(singerDTO, Singer.class);
        this.updateById(singer);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long singerId) {
        Singer existSinger = this.getById(singerId);
        if (ObjectUtil.isNull(existSinger)) {
            throw new SingerNotFoundException(MessageConstant.SINGER_NOT_FOUND);
        }

        long songCount = songService.countByArtistId(singerId);
        if (songCount > 0) {

            songService.softDeleteByArtistId(singerId);
            log.info("歌手{}有歌曲，已删除歌曲", singerId);
        }

        this.removeById(singerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> singerIds) {

        List<Singer> singerList = this.listByIds(singerIds);
        if (singerList.size() != singerIds.size()) {
            throw new SingerNotFoundException(MessageConstant.SINGER_NOT_FOUND);
        }

        this.removeByIds(singerIds);
    }

    @Override
    public String uploadAvatar(MultipartFile file, Long singerId) {
        Singer singer = this.getById(singerId);
        if (ObjectUtil.isNull(singer)) {
            throw new SingerNotFoundException(MessageConstant.SINGER_NOT_FOUND);
        }
        String avatarUrl = minioUtils.uploadFile(file, "avatar/singer/");
        singer.setAvatar(avatarUrl);
        this.updateById(singer);
        return avatarUrl;
    }

    /**
     * 将Singer实体转换为SingeVO
     *
     * @param singer 用户实体
     * @return 用户VO
     */
    public SingerVO convertToSingerVO(Singer singer) {
        SingerVO singerVO = new SingerVO();
        BeanUtil.copyProperties(singer, singerVO);
        return singerVO;
    }

}
