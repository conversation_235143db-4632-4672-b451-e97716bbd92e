package com.jpzz.pojo.dto.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.math.BigDecimal;

/**
 * 播放记录DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "播放记录信息")
public class PlayRecordDTO {

    @Schema(description = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "歌曲ID")
    @NotNull(message = "歌曲ID不能为空")
    private Long songId;

    @Schema(description = "播放时长(秒)")
    @Min(value = 0, message = "播放时长不能小于0")
    private Integer playDuration;

    @Schema(description = "播放进度(0-100)")
    @Min(value = 0, message = "播放进度不能小于0")
    @Max(value = 100, message = "播放进度不能大于100")
    private BigDecimal playProgress;

    @Schema(description = "设备类型")
    private String deviceType = "WEB";

    @Schema(description = "IP地址")
    private String ipAddress;

    @Schema(description = "歌单ID(如果是从歌单播放)")
    private Long playlistId;
}
