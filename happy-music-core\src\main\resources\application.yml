server:
  port: 8080
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************
    username: root
    password: root
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      web-stat-filter:
        enabled: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      file-size-threshold: 2KB
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 5
      timeout: 3000ms
  application:
    name: happy-music-server
  jackson:
    # 时间格式化
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    # 序列化配置
    serialization:
      write-dates-as-timestamps: false
    # 反序列化配置
    deserialization:
      fail-on-unknown-properties: false

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  type-aliases-package: com.jpzz.pojo.entity
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted  # 逻辑删除字段
      logic-delete-value: 1        # 逻辑已删除值
      logic-not-delete-value: 0    # 逻辑未删除值
logging:
  level:
    org.springframework.jdbc.support.JdbcTransactionManager: debug
    com:
      jpzz:
        mapper: debug
        service: info
        controller: info

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'admin'
      paths-to-match: '/**'
      #生成文档所需的扫包路径，一般为启动类目录
      packages-to-scan: com.jpzz.controller.admin
    - group: 'client'
      paths-to-match: '/**'
      packages-to-scan: com.jpzz.controller.client
#knife4j配置
knife4j:
  #是否启用增强设置
  enable: true
  #开启生产环境屏蔽
  production: false
  #是否启用登录认证
  basic:
    enable: true
    username: admin
    password: 123456
  setting:
    language: zh_cn
    enable-version: true
    enable-swagger-models: true
    swagger-model-name: 用户模块

jpzz:
  # jwt配置
  jwt:
    secret-key: happy-music-secret-key # 密钥
    expiration: 864000000  # token过期时间，单位毫秒，默认1天  10天
  # MinIO 配置
  minio:
    endpoint: http://localhost:9000
    access-key: admin
    secret-key: admin1234
    bucket-name: happy-music-files
    # 文件访问前缀
    file-host: http://localhost:9000


