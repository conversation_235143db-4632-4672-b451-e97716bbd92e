@tailwind base;
@tailwind components;
@tailwind utilities;

/* 🎨 Happy Music 主题变量 */
:root {
  /* 主色调 - 音乐主题的紫蓝渐变 */
  --primary-color: #667eea;
  --primary-light: #764ba2;
  --primary-dark: #5a67d8;

  /* 辅助色彩 */
  --secondary-color: #f093fb;
  --accent-color: #4facfe;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 背景渐变 */
  --bg-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --bg-gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --bg-gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --bg-gradient-warm: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --bg-gradient-cool: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* 玻璃态效果 */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* 动画时长 */
  --transition-fast: 0.15s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;

  /* 圆角 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
}

@layer base {
  /* 基础样式重置 */
  * {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .app-wrapper {
    @apply flex h-screen;
  }

  /* 滚动条美化 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-md);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-md);
    transition: background var(--transition-normal);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  }
}

@layer components {
  /* 🎵 音乐主题组件样式 */

  /* 玻璃态卡片 */
  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
  }

  .glass-card:hover {
    background: rgba(255, 255, 255, 0.35);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
  }

  /* 渐变按钮 */
  .gradient-button {
    background: var(--bg-gradient-primary);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
  }

  .gradient-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .gradient-button:hover::before {
    left: 100%;
  }

  .gradient-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
  }

  /* 音乐波纹效果 */
  .music-ripple {
    position: relative;
    overflow: hidden;
  }

  .music-ripple::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  .music-ripple:hover::after {
    width: 300px;
    height: 300px;
  }

  /* 浮动动画 */
  .float-animation {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* 脉冲动画 */
  .pulse-animation {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  /* 渐变文字 */
  .gradient-text {
    background: var(--bg-gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
  }

  /* 音符装饰 */
  .music-note-decoration::before {
    content: '♪';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.5rem;
    color: var(--primary-color);
    opacity: 0.3;
    animation: musicNote 4s ease-in-out infinite;
  }

  @keyframes musicNote {
    0%, 100% { transform: rotate(0deg) scale(1); }
    25% { transform: rotate(5deg) scale(1.1); }
    75% { transform: rotate(-5deg) scale(0.9); }
  }
}
