// 反馈信息接口
export interface FeedbackInfo {
  id: number
  userId: number
  username?: string
  userAvatar?: string
  content: string
  type: FeedbackType
  status: FeedbackStatus
  createTime: string
  updateTime?: string
  adminReply?: string
  replyTime?: string
}

// 反馈类型枚举
export enum FeedbackType {
  BUG = 'BUG',
  FEATURE = 'FEATURE',
  COMPLAINT = 'COMPLAINT',
  SUGGESTION = 'SUGGESTION',
  OTHER = 'OTHER'
}

// 反馈状态枚举
export enum FeedbackStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  RESOLVED = 'RESOLVED',
  CLOSED = 'CLOSED'
}

// 反馈类型选项
export const FeedbackTypes = [
  { label: '问题反馈', value: FeedbackType.BUG },
  { label: '功能建议', value: FeedbackType.FEATURE },
  { label: '投诉举报', value: FeedbackType.COMPLAINT },
  { label: '意见建议', value: FeedbackType.SUGGESTION },
  { label: '其他', value: FeedbackType.OTHER }
]

// 反馈状态选项
export const FeedbackStatuses = [
  { label: '待处理', value: FeedbackStatus.PENDING },
  { label: '处理中', value: FeedbackStatus.PROCESSING },
  { label: '已解决', value: FeedbackStatus.RESOLVED },
  { label: '已关闭', value: FeedbackStatus.CLOSED }
]

// 查询参数接口
export interface FeedbackQueryParams {
  content?: string
  type?: string
  status?: string
  userId?: number
  pageNum: number
  pageSize: number
}

// 反馈回复数据接口
export interface FeedbackReplyData {
  id: number
  adminReply: string
}

// 反馈状态更新数据接口
export interface FeedbackStatusData {
  id: number
  status: FeedbackStatus
  remark?: string
}
