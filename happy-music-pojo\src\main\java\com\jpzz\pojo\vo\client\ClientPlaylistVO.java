package com.jpzz.pojo.vo.client;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户端歌单VO
 * <AUTHOR>
 */
@Data
@Schema(description = "客户端歌单信息")
public class ClientPlaylistVO {

    @Schema(description = "歌单ID")
    private Long id;

    @Schema(description = "歌单名称")
    private String name;

    @Schema(description = "歌单描述")
    private String description;

    @Schema(description = "歌单风格")
    private String style;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "歌曲数量")
    private Integer songCount;

    @Schema(description = "播放次数")
    private Long playCount;

    @Schema(description = "喜欢次数")
    private Long likeCount;

    @Schema(description = "创建者ID")
    private Long userId;

    @Schema(description = "创建者用户名")
    private String username;

    @Schema(description = "是否公开")
    private Boolean isPublic;

    @Schema(description = "用户是否喜欢")
    private Boolean isLiked;

    @Schema(description = "用户是否收藏")
    private Boolean isCollected;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "状态")
    private String status;
}
