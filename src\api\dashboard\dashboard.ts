import request from '@/utils/request'
import type { DashboardOverview, UserTrendData, FeedbackStatsData, ContentStatsData } from '@/types/dashboard'

/**
 * 获取仪表板概览数据
 * @returns 概览数据
 */
export function getDashboardOverview(): Promise<DashboardOverview> {
  return request.get('/dashboard/overview')
}

/**
 * 获取用户增长趋势
 * @param days 天数，默认30天
 * @returns 用户趋势数据
 */
export function getUserTrend(days: number = 30): Promise<UserTrendData[]> {
  return request.get('/dashboard/user-trend', { days })
}

/**
 * 获取反馈统计
 * @returns 反馈统计数据
 */
export function getFeedbackStats(): Promise<FeedbackStatsData[]> {
  return request.get('/dashboard/feedback-stats')
}

/**
 * 获取内容统计
 * @param months 月数，默认12个月
 * @returns 内容统计数据
 */
export function getContentStats(months: number = 12): Promise<ContentStatsData[]> {
  return request.get('/dashboard/content-stats', { months })
}
