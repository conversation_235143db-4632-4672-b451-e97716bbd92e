package com.jpzz.pojo.dto.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 歌曲查询参数DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "歌曲查询参数")
public class SongQueryDTO extends PageQueryDTO {


    @Schema(description = "歌曲名称(模糊查询)", example = "青花瓷")
    private String name;

    @Schema(description = "歌手ID", example = "1")
    private Long artistId;

    @Schema(description = "歌手名称(模糊查询)", example = "周杰伦")
    private String artistName;

    @Schema(description = "风格", example = "流行")
    private String style;

    @Schema(description = "专辑名称(模糊查询)", example = "我很忙")
    private String album;
}