<template>
  <div class="min-h-screen relative overflow-hidden">
    <!-- 背景装饰 -->
    <div
      class="absolute inset-0 bg-gradient-to-br from-blue-100 via-white to-purple-50 pointer-events-none"

    ></div>
    <div
      class="absolute top-0 right-0 w-96 h-96 rounded-full blur-3xl pointer-events-none"
      style="background: linear-gradient(135deg, rgba(97, 144, 232, 0.15) 0%, rgba(167, 191, 232, 0.1) 100%)"
    ></div>

    <div class="relative z-10 p-6">
      <!-- 页面标题 -->
      <div class="mb-4">
        <div class="flex items-center space-x-3 mb-1">
          <div
            class="w-8 h-8 rounded-lg flex items-center justify-center"
            style="background: linear-gradient(135deg, rgb(97, 144, 232) 0%, rgb(167, 191, 232) 100%)"
          >
            <Icon icon="mdi:playlist-music" class="text-white text-lg" />
          </div>
          <h1
            class="text-xl font-bold bg-clip-text text-transparent"
            style="background: linear-gradient(135deg, rgb(97, 144, 232) 0%, rgb(167, 191, 232) 100%); -webkit-background-clip: text;"
          >
            歌单管理
          </h1>
        </div>
        <p class="text-sm text-gray-600">管理音乐歌单信息，包括歌单创建、编辑和删除</p>
      </div>

      <!-- 搜索区域 -->
      <el-card class="search-card enhanced-glass-card mb-4" shadow="never">
        <template #header>
          <div class="flex items-center space-x-3 py-1">
            <div
              class="w-6 h-6 rounded-lg flex items-center justify-center"
              style="background: linear-gradient(135deg, rgb(97, 144, 232) 0%, rgb(167, 191, 232) 100%)"
            >
              <Icon icon="mdi:magnify" class="text-white text-sm" />
            </div>
            <div>
              <h3 class="text-base font-semibold text-gray-800">搜索筛选</h3>
              <p class="text-xs text-gray-500">快速查找歌单信息</p>
            </div>
          </div>
        </template>

        <el-form
          :model="playlistStore.queryParams"
          @submit.prevent
          class="enhanced-search-form"
          label-width="60px"
          label-position="left"
        >
          <el-row :gutter="16" class="items-end">
            <el-col :span="6">
              <el-form-item label="歌单名称：" class="enhanced-form-item" label-width="75">
                <el-input
                  v-model="playlistStore.queryParams.name"
                  placeholder="请输入歌单名称"
                  clearable
                  @keyup.enter="handleSearch"
                  class="enhanced-input"
                  size="default"
                />
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="风格：" class="enhanced-form-item" label-width="50">
                <el-select
                  v-model="playlistStore.queryParams.style"
                  placeholder="请选择风格"
                  clearable
                  class="enhanced-select w-full"
                  size="default"
                >
                  <el-option
                    v-for="style in PlaylistStyles"
                    :key="style"
                    :label="style"
                    :value="style"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="10">
              <el-form-item class="enhanced-form-item">
                <div class="flex items-center space-x-3">
                  <el-button
                    type="primary"
                    @click="handleSearch"
                    class="enhanced-btn enhanced-btn-primary"
                  >
                    <Icon icon="mdi:magnify" class="mr-2" />
                    搜索
                  </el-button>
                  <el-button @click="handleReset" class="enhanced-btn enhanced-btn-reset">
                    <Icon icon="mdi:refresh" class="mr-2" />
                    重置
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 操作按钮区域 -->
      <el-card class="table-card enhanced-glass-card" shadow="never">
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div
                class="w-8 h-8 rounded-lg flex items-center justify-center"
                style="background: linear-gradient(135deg, rgb(97, 144, 232) 0%, rgb(167, 191, 232) 100%)"
              >
                <Icon icon="mdi:table-edit" class="text-white text-lg" />
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-800">歌单列表</h3>
                <p class="text-sm text-gray-500">共 {{ playlistStore.total }} 条记录</p>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <el-button type="primary" @click="handleAdd" class="enhanced-btn-primary">
                <Icon icon="mdi:playlist-plus" class="mr-2" />
                新增歌单
              </el-button>

              <el-button
                type="danger"
                :disabled="selectedPlaylists.length === 0"
                @click="handleBatchDelete"
                class="enhanced-btn"
              >
                <el-icon><Delete /></el-icon>
                批量删除 ({{ selectedPlaylists.length }})
              </el-button>

              <el-button type="success" @click="handleRefresh" class="enhanced-btn">
                <Icon icon="mdi:refresh" class="mr-2" />
                刷新
              </el-button>
            </div>
          </div>
        </template>

        <!-- 歌单表格 -->
        <el-table
          :data="playlistStore.playlistList"
          v-loading="playlistStore.loading"
          @selection-change="handleSelectionChange"
          class="enhanced-table"
          stripe
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="id" label="ID" width="80" align="center" />

          <el-table-column label="封面" align="center" width="100">
            <template #default="{ row }">
              <div class="flex justify-center">
                <el-image
                  :src="row.coverUrl || defaultCover"
                  class="w-12 h-12 rounded-lg cursor-pointer shadow-md hover:shadow-lg transition-shadow"
                  fit="cover"
                  @click="handlePreviewImage(row.coverUrl)"
                />
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="name"
            label="歌单名称"
            min-width="150"
            align="center"
            show-overflow-tooltip
          />

          <el-table-column label="风格" min-width="120" align="center">
            <template #default="{ row }">
              <el-tag
                v-if="row.style"
                size="small"
                type="primary"
                effect="light"
                class="enhanced-tag"
              >
                {{ row.style }}
              </el-tag>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>

          <el-table-column prop="songCount" label="歌曲数量" width="120" align="center">
            <template #default="{ row }">
              <div class="flex items-center justify-center space-x-1">
                <Icon icon="mdi:music-note" style="color: rgb(97, 144, 232)" />
                <span class="font-medium">{{ row.songCount || 0 }}</span>
                <span class="text-gray-500 text-sm">首</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="description"
            label="简介"
            min-width="200"
            show-overflow-tooltip
            align="center"
          />

          <el-table-column prop="createTime" label="创建时间" width="180" align="center" />

          <el-table-column label="操作" width="200" fixed="right" align="center">
            <template #default="{ row }">
              <div class="flex items-center justify-center space-x-2">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleEdit(row)"
                  class="enhanced-btn-small enhanced-btn-primary"
                >
                  <Icon icon="mdi:pencil" class="mr-1" />
                  编辑
                </el-button>

                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
                  class="enhanced-btn-small"
                >
                  <Icon icon="mdi:delete" class="mr-1" />
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="flex justify-center mt-6">
          <el-pagination
            v-model:current-page="playlistStore.queryParams.pageNum"
            v-model:page-size="playlistStore.queryParams.pageSize"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="playlistStore.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
            class="enhanced-pagination"
          />
        </div>
      </el-card>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewImageVisible" title="封面预览" width="60%" center>
      <div class="text-center">
        <el-image :src="previewImageUrl" fit="contain" style="max-width: 100%; max-height: 70vh" />
      </div>
    </el-dialog>

    <!-- 添加/编辑歌单对话框 -->
    <PlaylistDialog
      :dialog-props="dialogProps"
      @update:visible="dialogProps.visible = $event"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { usePlaylistStore } from '@/stores/playlist'
import { PlaylistStyles } from '@/types/playlist'
import type { PlaylistInfo } from '@/types/playlist'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
defineOptions({
  name: 'PlaylistView',
})
// 默认封面
const defaultCover = ref('/favicon.ico')

// Store
const playlistStore = usePlaylistStore()
const router = useRouter()

// 选中的歌单
const selectedPlaylists = ref<PlaylistInfo[]>([])

// 弹窗属性
interface DialogProps {
  visible: boolean
  title: string
  type: 'add' | 'edit'
  data?: Partial<PlaylistInfo>
}

const dialogProps = reactive<DialogProps>({
  visible: false,
  title: '',
  type: 'add',
  data: undefined,
})

// 搜索
const handleSearch = () => {
  playlistStore.queryParams.pageNum = 1
  playlistStore.getList()
}

// 重置
const handleReset = () => {
  playlistStore.resetQuery()
}

// 刷新
const handleRefresh = () => {
  playlistStore.getList()
}

// 新增歌单
const handleAdd = () => {
  dialogProps.visible = true
  dialogProps.title = '新增歌单'
  dialogProps.type = 'add'
  dialogProps.data = undefined
}

// 编辑歌单
const handleEdit = (playlist: PlaylistInfo) => {
  router.push(`/playlist/edit/${playlist.id}`)
}

// 删除歌单
const handleDelete = async (playlist: PlaylistInfo) => {
  try {
    await ElMessageBox.confirm(`确认删除歌单【${playlist.name}】吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await playlistStore.deletePlaylist(playlist.id)
  } catch (error) {
    console.log(error)
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedPlaylists.value.length === 0) {
    ElMessage.warning('请选择要删除的歌单')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确认删除选中的 ${selectedPlaylists.value.length} 个歌单吗？`,
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    const playlistIds = selectedPlaylists.value.map((item) => item.id)
    await playlistStore.deleteBatchPlaylists(playlistIds)
    selectedPlaylists.value = []
  } catch (error) {
    console.log(error)
  }
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  playlistStore.queryParams.pageSize = size
  playlistStore.queryParams.pageNum = 1
  playlistStore.getList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  playlistStore.queryParams.pageNum = page
  playlistStore.getList()
}

// 选择变化
const handleSelectionChange = (selection: PlaylistInfo[]) => {
  selectedPlaylists.value = selection
}

// 对话框成功回调
const handleDialogSuccess = async (formData: Partial<PlaylistInfo>, type: 'add' | 'edit') => {
  try {
    if (type === 'add') {
      await playlistStore.addPlaylist(formData)
    } else {
      await playlistStore.updatePlaylist(formData)
    }
    dialogProps.visible = false
    playlistStore.getList()
  } catch (error) {
    console.error('操作失败:', error)
  }
}
// 图片预览
const previewImageVisible = ref(false)
const previewImageUrl = ref('')

const handlePreviewImage = (url: string) => {
  if (url) {
    previewImageUrl.value = url
    previewImageVisible.value = true
  }
}

// 初始化
onMounted(() => {
  playlistStore.getList()
})



</script>

<style scoped>
/* 🎵 歌单管理页面样式 */

/* 搜索表单样式 */
.enhanced-search-form {
  background: transparent;
}

.search-card {
  border-radius: 12px;
}

.search-card :deep(.el-card__header) {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.search-card :deep(.el-card__body) {
  padding: 16px 20px;
}

.enhanced-form-item :deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0 !important;
  line-height: 32px; /* 与输入框高度对齐 */
  display: flex;
  align-items: center;
  font-size: 14px;
  height: 32px; /* 固定标签高度 */
  padding-right: 4px; /* 减少右侧间距 */
}

.enhanced-form-item {
  margin-bottom: 16px !important;
  align-items: center;
  display: flex;
}

.enhanced-form-item :deep(.el-form-item__content) {
  line-height: 32px;
  min-height: 32px; /* 确保内容区域最小高度 */
  display: flex;
  align-items: center;
  flex: 1;
}

/* 确保输入框和选择框的高度一致 */
.enhanced-input :deep(.el-input__inner),
.enhanced-select :deep(.el-input__inner) {
  height: 32px !important;
  line-height: 32px !important;
}

/* 日期选择器对齐 */
.enhanced-date-picker :deep(.el-input__inner) {
  height: 32px !important;
  line-height: 32px !important;
}

/* 选择框下拉箭头对齐 */
.enhanced-select :deep(.el-input__suffix) {
  height: 32px !important;
  display: flex;
  align-items: center;
}

/* 选择框选项样式优化 */
.enhanced-select :deep(.el-select-dropdown__item) {
  padding: 8px 12px;
  min-height: 36px;
  display: flex;
  align-items: center;
}

/* 确保选择框有足够的宽度显示选项文字 */
.enhanced-select {
  min-width: 120px;
}

.enhanced-input :deep(.el-input__wrapper) {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.enhanced-input :deep(.el-input__wrapper):hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.enhanced-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.enhanced-select :deep(.el-select__wrapper) {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.enhanced-select :deep(.el-select__wrapper):hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

/* 玻璃态卡片样式 */
.enhanced-glass-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.enhanced-glass-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.95);
}

/* 按钮样式 */
.enhanced-btn {
  border-radius: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.enhanced-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.enhanced-btn-primary {
  background: linear-gradient(135deg, rgb(97, 144, 232) 0%, rgb(167, 191, 232) 100%);
  color: white !important;
}

.enhanced-btn-reset {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white !important;
}

.enhanced-btn-small {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 4px 8px;
  font-size: 12px;
}

/* 表格样式 */
.enhanced-table {
  border-radius: 12px;
  overflow: hidden;
}

.enhanced-table :deep(.el-table__header) {
  background: linear-gradient(135deg, rgba(97, 144, 232, 0.08) 0%, rgba(167, 191, 232, 0.12) 100%);
}

.enhanced-table :deep(.el-table__row:hover) {
  background-color: rgba(97, 144, 232, 0.08) !important;
}

/* 标签样式 */
.enhanced-tag {
  border-radius: 8px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 分页样式 */
.enhanced-pagination :deep(.el-pager li) {
  border-radius: 8px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.enhanced-pagination :deep(.el-pager li:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(97, 144, 232, 0.3);
}

.enhanced-pagination :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg, rgb(97, 144, 232) 0%, rgb(167, 191, 232) 100%);
  color: white;
  font-weight: 600;
}

/* 🔧 修复表格水平滚动时固定列层级问题 */
:deep(.el-table .el-table__fixed-left) {
  z-index: 10 !important;
}

:deep(.el-table .el-table__fixed-right) {
  z-index: 10 !important;
}

:deep(.el-table .el-table__fixed-left-patch) {
  z-index: 11 !important;
}

:deep(.el-table .el-table__fixed-right-patch) {
  z-index: 11 !important;
}

/* 确保固定列的表头也有正确的层级 */
:deep(.el-table .el-table__fixed-header-wrapper) {
  z-index: 12 !important;
}

/* 修复固定列边框显示问题 */
:deep(.el-table .el-table__fixed-left::before) {
  background-color: #e5e7eb;
  z-index: 10 !important;
}

:deep(.el-table .el-table__fixed-right::before) {
  background-color: #e5e7eb;
  z-index: 10 !important;
}
</style>
