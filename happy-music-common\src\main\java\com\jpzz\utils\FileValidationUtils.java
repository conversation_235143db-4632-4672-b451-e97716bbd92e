package com.jpzz.utils;

import cn.hutool.core.util.StrUtil;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件验证工具类
 * <AUTHOR>
 */
public class FileValidationUtils {

    /**
     * 验证图片文件
     */
    public static String validateImageFile(MultipartFile file, long maxSizeMB) {
        if (file.isEmpty()) {
            return "文件不能为空";
        }
        
        String contentType = file.getContentType();
        if (StrUtil.isBlank(contentType) || !contentType.startsWith("image/")) {
            return "文件格式错误，只能上传图片文件";
        }
        
        if (file.getSize() > maxSizeMB * 1024 * 1024) {
            return "文件大小不能超过" + maxSizeMB + "MB";
        }

        // 验证通过
        return null;
    }

    /**
     * 验证音频文件
     */
    public static String validateAudioFile(MultipartFile file, long maxSizeMB) {
        if (file.isEmpty()) {
            return "文件不能为空";
        }
        
        String contentType = file.getContentType();
        if (!isAudioFile(contentType)) {
            return "文件格式错误，只能上传音频文件（支持MP3、WAV、FLAC、AAC等格式）";
        }
        
        if (file.getSize() > maxSizeMB * 1024 * 1024) {
            return "文件大小不能超过" + maxSizeMB + "MB";
        }

        // 验证通过
        return null;
    }

    /**
     * 判断是否为音频文件
     */
    private static boolean isAudioFile(String contentType) {
        if (contentType == null) {
            return false;
        }
        return contentType.startsWith("audio/") || 
               contentType.equals("application/octet-stream");
    }
}