package com.jpzz.pojo.vo.admin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 歌单VO
 * <AUTHOR>
 */
@Data
@Schema(description = "歌单VO")
public class PlaylistVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "歌单ID")
    private Long id;

    @Schema(description = "歌单名称")
    private String name;

    @Schema(description = "歌单描述")
    private String description;

    @Schema(description = "歌单风格")
    private String style;

    @Schema(description = "封面URL")
    private String coverUrl;

    @Schema(description = "歌曲数量")
    private Integer songCount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}