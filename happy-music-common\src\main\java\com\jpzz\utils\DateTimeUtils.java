package com.jpzz.utils;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 日期时间处理工具类
 *
 * <AUTHOR>
 */
public class DateTimeUtils {

    private static final String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 解析开始时间和结束时间
     *
     * @param beginTimeStr 开始时间字符串
     * @param endTimeStr   结束时间字符串
     * @return 时间范围数组 [beginTime, endTime]
     */
    public static LocalDateTime[] parseTimeRange(String beginTimeStr, String endTimeStr) {
        LocalDateTime beginTime = null;
        LocalDateTime endTime = null;

        if (StrUtil.isNotBlank(beginTimeStr)) {
            beginTime = LocalDateTimeUtil.parse(beginTimeStr, DateTimeFormatter.ofPattern(DEFAULT_PATTERN));
        }
        if (StrUtil.isNotBlank(endTimeStr)) {
            endTime = LocalDateTimeUtil.parse(endTimeStr, DateTimeFormatter.ofPattern(DEFAULT_PATTERN));
        }

        return new LocalDateTime[]{beginTime, endTime};
    }
}