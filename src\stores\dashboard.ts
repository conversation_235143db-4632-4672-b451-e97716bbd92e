import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { DashboardOverview, UserTrendData, FeedbackStatsData, ContentStatsData } from '@/types/dashboard'
import {
    getDashboardOverview,
    getUserTrend,
    getFeedbackStats,
    getContentStats
} from '@/api/dashboard/dashboard'
import { ElMessage } from 'element-plus'

export const useDashboardStore = defineStore('dashboard', () => {
    // 状态定义
    const overview = ref<DashboardOverview | null>(null)
    const userTrendData = ref<UserTrendData[]>([])
    const feedbackStatsData = ref<FeedbackStatsData[]>([])
    const contentStatsData = ref<ContentStatsData[]>([])
    const loading = ref(false)

    // 获取概览数据
    const getOverview = async () => {
        try {
            loading.value = true
            const res = await getDashboardOverview()
            overview.value = res
        } catch (error) {
            console.error('获取概览数据失败', error)
            ElMessage.error('获取概览数据失败')
        } finally {
            loading.value = false
        }
    }

    // 获取用户趋势数据
    const getUserTrendData = async (days: number = 30) => {
        try {
            const res = await getUserTrend(days)
            userTrendData.value = res
        } catch (error) {
            console.error('获取用户趋势数据失败', error)
            ElMessage.error('获取用户趋势数据失败')
        }
    }

    // 获取反馈统计数据
    const getFeedbackStatsData = async () => {
        try {
            const res = await getFeedbackStats()
            feedbackStatsData.value = res
        } catch (error) {
            console.error('获取反馈统计数据失败', error)
            ElMessage.error('获取反馈统计数据失败')
        }
    }

    // 获取内容统计数据
    const getContentStatsData = async (months: number = 12) => {
        try {
            const res = await getContentStats(months)
            contentStatsData.value = res
        } catch (error) {
            console.error('获取内容统计数据失败', error)
            ElMessage.error('获取内容统计数据失败')
        }
    }

    // 初始化所有数据
    const initDashboard = async () => {
        await Promise.all([
            getOverview(),
            getUserTrendData(),
            getFeedbackStatsData(),
            getContentStatsData()
        ])
    }

    return {
        // 状态
        overview,
        userTrendData,
        feedbackStatsData,
        contentStatsData,
        loading,

        // 方法
        getOverview,
        getUserTrendData,
        getFeedbackStatsData,
        getContentStatsData,
        initDashboard
    }
})
