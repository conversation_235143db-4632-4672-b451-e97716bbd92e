<template>
  <div class="chart-container h-full">
    <v-chart
      v-if="!loading && chartData"
      :option="chartOption"
      class="h-full"
      autoresize
    />
    <div v-else-if="loading" class="flex items-center justify-center h-full">
      <Icon icon="mdi:loading" class="animate-spin text-3xl text-gray-400" />
    </div>
    <div v-else class="flex items-center justify-center h-full text-gray-400">
      <div class="text-center">
        <Icon icon="mdi:chart-bar" class="text-4xl mb-2" />
        <p>暂无数据</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { Icon } from '@iconify/vue'
import type { ContentStatsData } from '@/types/dashboard'

use([
  Canvas<PERSON><PERSON>er,
  Bar<PERSON>hart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

interface Props {
  data: ContentStatsData[]
  loading?: boolean
}

const props = defineProps<Props>()

const chartData = computed(() => {
  if (!props.data || props.data.length === 0) return null

  return {
    months: props.data.map(item => item.month),
    songUploads: props.data.map(item => item.songUploads),
    singerRegistrations: props.data.map(item => item.singerRegistrations)
  }
})

const chartOption = computed(() => {
  if (!chartData.value) return {}

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      },
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e4e7ed',
      borderWidth: 1,
      textStyle: {
        color: '#606266'
      }
    },
    legend: {
      data: ['歌曲上传', '歌手注册'],
      textStyle: {
        color: '#606266'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.value.months,
      axisPointer: {
        type: 'shadow'
      },
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#909399'
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#909399'
      },
      splitLine: {
        lineStyle: {
          color: '#f5f7fa'
        }
      }
    },
    series: [
      {
        name: '歌曲上传',
        type: 'bar',
        data: chartData.value.songUploads,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#ffd700' },
              { offset: 1, color: '#ff8c00' }
            ]
          },
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(255, 140, 0, 0.5)'
          }
        }
      },
      {
        name: '歌手注册',
        type: 'bar',
        data: chartData.value.singerRegistrations,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#67c23a' },
              { offset: 1, color: '#529b2e' }
            ]
          },
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(103, 194, 58, 0.5)'
          }
        }
      }
    ]
  }
})
</script>
