<template>
  <el-dialog
    :title="dialogProps.title"
    :model-value="dialogProps.visible"
    @close="handleClose"
    width="600px"
    class="playlist-dialog"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="right"
      class="px-4"
    >
      <el-form-item v-if="dialogProps.type === 'edit'" label="歌单编号">
        <el-input v-model="form.id" disabled class="w-full" />
      </el-form-item>

      <el-form-item label="歌单名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入歌单名称"
          class="w-full"
          clearable
        />
      </el-form-item>

      <el-form-item label="歌单风格" prop="style">
        <el-select
          v-model="form.style"
          placeholder="请选择歌单风格"
          class="w-full"
        >
          <el-option
            v-for="style in PlaylistStyles"
            :key="style"
            :label="style"
            :value="style"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="歌单简介" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入歌单简介"
          class="w-full"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { PlaylistStyles } from '@/types/playlist'
import type { PlaylistInfo } from '@/types/playlist'
import type { FormInstance, FormRules } from 'element-plus'

const props = defineProps<{
  dialogProps: {
    title: string
    visible: boolean
    type: 'add' | 'edit'
    data?: Partial<PlaylistInfo>
  }
}>()

const emit = defineEmits(['update:visible', 'success'])
const formRef = ref<FormInstance>()

const form = reactive<Partial<PlaylistInfo>>({
  id: undefined,
  name: '',
  style: '', // 改为单个字符串
  description: '',
})

const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入歌单名称', trigger: 'blur' },
    { min: 1, max: 50, message: '歌单名称长度在 1 到 50 个字符', trigger: 'blur' },
  ],
  style: [
    { required: true, message: '请选择歌单风格', trigger: 'change' }
  ]
})

watch(
  () => props.dialogProps.visible,
  (val) => {
    if (val) {
      if (props.dialogProps.type === 'edit' && props.dialogProps.data) {
        Object.assign(form, props.dialogProps.data)
      } else {
        form.id = undefined
        form.name = ''
        form.style = ''
        form.description = ''
      }
    }
  },
)

const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      emit('success', form, props.dialogProps.type)
      handleClose()
    }
  })
}
</script>

<style scoped>
.playlist-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}
</style>



