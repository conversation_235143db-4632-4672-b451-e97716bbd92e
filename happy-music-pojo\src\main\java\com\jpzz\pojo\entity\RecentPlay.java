package com.jpzz.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 最近播放实体
 * <AUTHOR>
 */
@Data
@TableName("tb_recent_play")
public class RecentPlay {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 歌曲ID
     */
    private Long songId;

    /**
     * 播放时间
     */
    private LocalDateTime playTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
