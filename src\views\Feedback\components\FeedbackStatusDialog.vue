<template>
  <el-dialog
    v-model="visible"
    title="更新反馈状态"
    width="500px"
    @update:model-value="handleVisibleChange"
  >
    <div v-if="feedbackData" class="status-dialog">
      <!-- 反馈信息概览 -->
      <div class="feedback-overview mb-4">
        <h4 class="text-sm font-medium text-gray-700 mb-2">反馈信息</h4>
        <div class="overview-box">
          <div class="flex items-center justify-between mb-2">
            <span class="text-xs text-gray-500">反馈编号：{{ feedbackData.id }}</span>
            <span class="text-xs text-gray-500">用户：{{ feedbackData.username || '未知用户' }}</span>
          </div>
          <div class="current-status mb-2">
            <span class="text-xs text-gray-500">当前状态：</span>
            <el-tag :type="getStatusTagType(feedbackData.status)" size="small">
              {{ getStatusLabel(feedbackData.status) }}
            </el-tag>
          </div>
          <div class="feedback-content">{{ feedbackData.content }}</div>
        </div>
      </div>

      <!-- 状态选择 -->
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="新状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择新状态" class="w-full">
            <el-option
              v-for="status in FeedbackStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
              :disabled="status.value === feedbackData.status"
            >
              <div class="flex items-center justify-between w-full">
                <span>{{ status.label }}</span>
                <el-tag
                  v-if="status.value === feedbackData.status"
                  type="info"
                  size="small"
                >
                  当前
                </el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入状态变更备注（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <!-- 状态说明 -->
      <div class="status-description">
        <h4 class="text-sm font-medium text-gray-700 mb-2">状态说明</h4>
        <div class="description-list">
          <div class="description-item">
            <el-tag type="warning" size="small">待处理</el-tag>
            <span class="ml-2 text-xs text-gray-600">新提交的反馈，等待处理</span>
          </div>
          <div class="description-item">
            <el-tag type="primary" size="small">处理中</el-tag>
            <span class="ml-2 text-xs text-gray-600">正在处理中，可能需要时间解决</span>
          </div>
          <div class="description-item">
            <el-tag type="success" size="small">已解决</el-tag>
            <span class="ml-2 text-xs text-gray-600">问题已解决，等待用户确认</span>
          </div>
          <div class="description-item">
            <el-tag type="info" size="small">已关闭</el-tag>
            <span class="ml-2 text-xs text-gray-600">反馈已关闭，不再处理</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          更新状态
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { FeedbackStatuses, FeedbackStatus } from '@/types/feedback'
import type { FeedbackInfo } from '@/types/feedback'
import { useFeedbackStore } from '@/stores/feedback'

interface Props {
  modelValue: boolean
  feedbackData?: FeedbackInfo | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  feedbackData: null
})

const emit = defineEmits<Emits>()

const feedbackStore = useFeedbackStore()
const formRef = ref<FormInstance>()
const submitting = ref(false)

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 表单数据
const form = reactive({
  status: '',
  remark: ''
})

// 表单验证规则
const rules = reactive<FormRules>({
  status: [
    { required: true, message: '请选择新状态', trigger: 'change' }
  ]
})

// 获取状态标签样式
const getStatusTagType = (status: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const statusMap: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    [FeedbackStatus.PENDING]: 'warning',
    [FeedbackStatus.PROCESSING]: 'primary',
    [FeedbackStatus.RESOLVED]: 'success',
    [FeedbackStatus.CLOSED]: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态标签文本
const getStatusLabel = (status: string) => {
  const statusItem = FeedbackStatuses.find(item => item.value === status)
  return statusItem?.label || status
}

// 监听对话框显示状态，重置表单
watch(
  () => props.modelValue,
  (val) => {
    if (val && props.feedbackData) {
      form.status = props.feedbackData.status
      form.remark = ''
      formRef.value?.clearValidate()
    }
  }
)

const handleVisibleChange = (value: boolean) => {
  emit('update:modelValue', value)
}

const handleClose = () => {
  emit('update:modelValue', false)
}

const handleSubmit = async () => {
  if (!formRef.value || !props.feedbackData) return

  await formRef.value.validate(async (valid) => {
    if (valid && props.feedbackData) {
      if (form.status === props.feedbackData.status) {
        ElMessage.warning('请选择不同的状态')
        return
      }

      try {
        submitting.value = true
        await feedbackStore.updateStatus(props.feedbackData.id, form.status)
        ElMessage.success('状态更新成功')
        emit('success')
        handleClose()
      } catch (error) {
        console.error('状态更新失败:', error)
      } finally {
        submitting.value = false
      }
    }
  })
}
</script>

<style scoped>
.status-dialog {
  @apply text-sm;
}

.overview-box {
  @apply p-3 bg-gray-50 rounded-lg border;
}

.feedback-content {
  @apply text-gray-700 text-sm leading-relaxed;
  max-height: 60px;
  overflow-y: auto;
}

.current-status {
  @apply flex items-center;
}

.status-description {
  @apply mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200;
}

.description-list {
  @apply space-y-2;
}

.description-item {
  @apply flex items-center;
}
</style>

