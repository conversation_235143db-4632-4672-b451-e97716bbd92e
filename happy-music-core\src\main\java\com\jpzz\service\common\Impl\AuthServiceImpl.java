package com.jpzz.service.common.Impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jpzz.constant.MessageConstant;
import com.jpzz.constant.StatusConstant;
import com.jpzz.exception.LoginFailException;
import com.jpzz.exception.UserNotFoundException;
import com.jpzz.mapper.admin.UserMapper;
import com.jpzz.pojo.dto.admin.LoginDTO;
import com.jpzz.pojo.entity.User;
import com.jpzz.pojo.vo.admin.UserLoginVO;
import com.jpzz.service.common.AuthService;
import com.jpzz.service.common.CacheService;
import com.jpzz.utils.JwtUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthServiceImpl implements AuthService {
    @Resource
    private UserMapper userMapper;
    @Resource
    private JwtUtils jwtUtils;
    @Resource
    private CacheService cacheService;


    @Override
    public UserLoginVO login(LoginDTO loginDTO) {

        User user = userMapper.selectByUserName(loginDTO.getUsername());
        // 用户不存在
        if (!ObjectUtil.isNotNull(user)) {
            throw new LoginFailException(MessageConstant.USER_NOT_FOUND);
        }
        // 校验密码(已加密)
        String encryptedPassword = DigestUtils.md5DigestAsHex(loginDTO.getPassword().getBytes());
        if (!encryptedPassword.equals(user.getPassword())) {
            log.info("用户密码错误：{}", encryptedPassword);
            throw new LoginFailException(MessageConstant.PASSWORD_ERROR);
        }
        if (StatusConstant.DISABLE.equals(user.getStatus())) {
            throw new LoginFailException(MessageConstant.USER_DISABLE);
        }
        String token = jwtUtils.generateToken(user.getUserId(), user.getUsername(), user.getRole());
        // 缓存用户token
        cacheService.cacheUserToken(user.getUserId(), token);
        log.info("用户登录成功：{}", user.getUsername());
        return UserLoginVO.builder()
                .userId(user.getUserId())
                .username(user.getUsername())
                .avatar(user.getAvatar())
                .role(user.getRole())
                .token(token)
                .build();
    }


    @Override
    public void logout(Long userId) {
        log.info("用户注销Id:{}", userId);
        cacheService.clearUserToken(userId);
    }

    @Override
    public UserLoginVO getUserInfo(Long userId) {
        User user = userMapper.selectById(userId);
        if (ObjectUtil.isNull(user)) {
            throw new UserNotFoundException(MessageConstant.USER_NOT_FOUND);
        }
        return BeanUtil.copyProperties(user, UserLoginVO.class);
    }
}
