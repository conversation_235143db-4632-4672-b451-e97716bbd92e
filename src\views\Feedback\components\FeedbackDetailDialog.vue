<template>
  <el-dialog
    v-model="visible"
    title="反馈详情"
    width="700px"
    @update:model-value="handleVisibleChange"
  >
    <div v-if="feedbackData" class="feedback-detail">
      <!-- 基本信息 -->
      <div class="info-section mb-6">
        <h3 class="section-title">基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="反馈编号">{{ feedbackData.id }}</el-descriptions-item>
          <el-descriptions-item label="用户编号">{{ feedbackData.userId }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ feedbackData.username || '未知用户' }}</el-descriptions-item>
          <el-descriptions-item label="反馈类型">
            <el-tag :type="getTypeTagType(feedbackData.type)" size="small">
              {{ getTypeLabel(feedbackData.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="处理状态">
            <el-tag :type="getStatusTagType(feedbackData.status)" size="small">
              {{ getStatusLabel(feedbackData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="反馈时间">{{ feedbackData.createTime }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 反馈内容 -->
      <div class="content-section mb-6">
        <h3 class="section-title">反馈内容</h3>
        <div class="content-box">
          {{ feedbackData.content }}
        </div>
      </div>

      <!-- 管理员回复 -->
      <div v-if="feedbackData.adminReply" class="reply-section">
        <h3 class="section-title">管理员回复</h3>
        <div class="reply-box">
          <div class="reply-content">{{ feedbackData.adminReply }}</div>
          <div class="reply-time">回复时间：{{ feedbackData.replyTime }}</div>
        </div>
      </div>
      <div v-else class="no-reply">
        <el-empty description="暂无回复" :image-size="80" />
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { FeedbackTypes, FeedbackStatuses, FeedbackType, FeedbackStatus } from '@/types/feedback'
import type { FeedbackInfo } from '@/types/feedback'

interface Props {
  modelValue: boolean
  feedbackData?: FeedbackInfo | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
  (e: 'reply', feedback: FeedbackInfo): void
}

const props = withDefaults(defineProps<Props>(), {
  feedbackData: null
})

const emit = defineEmits<Emits>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 获取类型标签样式
const getTypeTagType = (type: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const typeMap: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    [FeedbackType.BUG]: 'danger',
    [FeedbackType.FEATURE]: 'primary',
    [FeedbackType.COMPLAINT]: 'warning',
    [FeedbackType.SUGGESTION]: 'success',
    [FeedbackType.OTHER]: 'info'
  }
  return typeMap[type] || 'info'
}

// 获取类型标签文本
const getTypeLabel = (type: string) => {
  const typeItem = FeedbackTypes.find(item => item.value === type)
  return typeItem?.label || type
}

// 获取状态标签样式
const getStatusTagType = (status: string): "success" | "primary" | "warning" | "info" | "danger" => {
  const statusMap: Record<string, "success" | "primary" | "warning" | "info" | "danger"> = {
    [FeedbackStatus.PENDING]: 'warning',
    [FeedbackStatus.PROCESSING]: 'primary',
    [FeedbackStatus.RESOLVED]: 'success',
    [FeedbackStatus.CLOSED]: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态标签文本
const getStatusLabel = (status: string) => {
  const statusItem = FeedbackStatuses.find(item => item.value === status)
  return statusItem?.label || status
}

const handleVisibleChange = (value: boolean) => {
  emit('update:modelValue', value)
}

const handleClose = () => {
  emit('update:modelValue', false)
}


</script>

<style scoped>
.feedback-detail {
  @apply text-sm;
}

.section-title {
  @apply text-base font-semibold text-gray-800 mb-3 pb-2 border-b border-gray-200;
}

.content-box {
  @apply p-4 bg-gray-50 rounded-lg border min-h-[100px] whitespace-pre-wrap;
  line-height: 1.6;
}

.reply-box {
  @apply p-4 bg-blue-50 rounded-lg border border-blue-200;
}

.reply-content {
  @apply mb-2 whitespace-pre-wrap;
  line-height: 1.6;
}

.reply-time {
  @apply text-xs text-gray-500;
}

.no-reply {
  @apply py-8;
}
</style>

