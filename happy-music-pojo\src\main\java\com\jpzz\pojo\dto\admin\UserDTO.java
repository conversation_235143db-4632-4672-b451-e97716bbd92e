package com.jpzz.pojo.dto.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 用户数据传输对象
 * 用于接收前端传入的用户数据
 * <AUTHOR>
 */
@Data
@Schema(description = "用户数据传输对象")
public class UserDTO {

    @Schema(description = "用户ID（修改时必填）", example = "1")
    private Long userId;

    @NotBlank(message = "用户名不能为空")
    @Schema(description = "用户名", example = "admin")
    private String username;

    @Schema(description = "密码（新增时必填）", example = "123456")
    private String password;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Schema(description = "手机号", example = "13800000000")
    private String phone;

    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "个人介绍", example = "系统管理员")
    private String introduction;

    @Schema(description = "角色(ADMIN-管理员,USER-普通用户)", example = "ADMIN")
    private String role;

    @Schema(description = "状态(0-禁用,1-启用)", example = "1")
    private String status;
}