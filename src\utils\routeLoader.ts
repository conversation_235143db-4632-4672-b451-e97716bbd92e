// 路由懒加载优化工具
import { defineAsyncComponent, defineComponent } from 'vue'
import type { AsyncComponentLoader, Component } from 'vue'

// 加载状态组件
const LoadingComponent = defineComponent({
  template: `
    <div class="flex items-center justify-center min-h-[200px]">
      <div class="flex items-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span class="ml-2 text-gray-600">页面加载中...</span>
      </div>
    </div>
  `
})

// 错误状态组件
const ErrorComponent = defineComponent({
  template: `
    <div class="flex flex-col items-center justify-center min-h-[200px] text-center">
      <div class="text-red-500 mb-4">
        <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
      </div>
      <p class="text-gray-600 mb-4">页面加载失败</p>
      <button
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        @click="retry"
      >
        重新加载
      </button>
    </div>
  `,
  methods: {
    retry() {
      window.location.reload()
    }
  }
})

/**
 * 创建优化的异步组件
 * @param loader 组件加载器
 * @param options 配置选项
 */
export function createAsyncComponent(
  loader: AsyncComponentLoader,
  options: {
    delay?: number
    timeout?: number
    suspensible?: boolean
  } = {}
) {
  return defineAsyncComponent({
    loader,
    loadingComponent: LoadingComponent,
    errorComponent: ErrorComponent,
    delay: options.delay || 200, // 200ms后显示loading
    timeout: options.timeout || 10000, // 10秒超时
    suspensible: options.suspensible || false
  })
}

/**
 * 预加载路由组件
 * @param routeName 路由名称
 */
export function preloadRoute(routeName: string) {
  const routeLoaders: Record<string, () => Promise<Component>> = {
    user: () => import('@/views/User/index.vue'),
    singer: () => import('@/views/Singer/index.vue'),
    song: () => import('@/views/Song/index.vue'),
    playlist: () => import('@/views/Playlist/index.vue'),
    feedback: () => import('@/views/Feedback/index.vue')
  }

  const loader = routeLoaders[routeName]
  if (loader) {
    // 预加载但不执行
    loader().catch(() => {
      console.warn(`预加载路由 ${routeName} 失败`)
    })
  }
}

/**
 * 智能预加载 - 根据用户行为预测
 */
export function setupIntelligentPreload() {
  // 鼠标悬停在导航链接上时预加载
  document.addEventListener('mouseover', (e) => {
    const target = e.target as HTMLElement
    const link = target.closest('[data-route]')
    if (link) {
      const routeName = link.getAttribute('data-route')
      if (routeName) {
        preloadRoute(routeName)
      }
    }
  })

  // 页面空闲时预加载常用页面
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      preloadRoute('user')
      preloadRoute('singer')
    })
  }
}

/**
 * 路由组件缓存管理
 */
export class RouteCache {
  private static cache = new Map<string, Component>()
  private static maxSize = 10

  static set(key: string, component: Component) {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      if (firstKey) {
        this.cache.delete(firstKey)
      }
    }
    this.cache.set(key, component)
  }

  static get(key: string): Component | undefined {
    return this.cache.get(key)
  }

  static clear() {
    this.cache.clear()
  }
}
