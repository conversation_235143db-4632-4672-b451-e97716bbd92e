<template>
  <div class="user-container">
    <!-- 搜索区域 -->
    <el-card class="search-card mb-4" shadow="hover">
      <el-form :inline="true" :model="queryParams" @submit.prevent class="search-form">
        <el-form-item label="用户名:">
          <el-input
            v-model="queryParams.username"
            placeholder="请输入用户名"
            clearable
            @keyup.enter="handleQuery"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="手机号码:">
          <el-input
            v-model="queryParams.phone"
            placeholder="请输入手机号码"
            clearable
            @keyup.enter="handleQuery"
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="状态:">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择"
            clearable
            style="width: 100px"
          >
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="角色:">
          <el-select v-model="queryParams.role" placeholder="请选择" clearable style="width: 120px">
            <el-option label="管理员" :value="UserRoles.ADMIN" />
            <el-option label="普通用户" :value="UserRoles.USER" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间:">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :shortcuts="dateShortcuts"
            style="width: 320px"
          />
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" @click="handleQuery">
            <el-icon><span class="iconfont icon-search pr-[10px]"></span></el-icon> 搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon size="18" class="pr-[10px]"><Refresh /></el-icon> 重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span class="font-bold text-lg">用户管理</span>
          <div class="button-group">
            <el-button type="warning" @click="addMockData">
              <el-icon size="18" class="pr-1"><DataAnalysis /></el-icon> 添加模拟数据
            </el-button>
            <el-button type="primary" @click="handleAdd" v-if="hasPermission('user:add')">
              <el-icon><span class="iconfont icon-add pr-[10px]"></span></el-icon> 新增用户
            </el-button>
            <el-button
              type="danger"
              :disabled="selectedUsers.length === 0"
              @click="handleBatchDelete"
              v-if="hasPermission('user:delete')"
            >
              <el-icon class="pr-2"><Delete /></el-icon> 批量删除
            </el-button>
            <el-button type="success" @click="handleExport">
              <el-icon class="pr-1"><span class="iconfont icon-export"></span></el-icon> 导出
            </el-button>
            <el-dropdown @command="handleCommand">
              <el-button>
                <el-icon><More /></el-icon> 更多操作
                <el-icon class="ml-1"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="importUsers">导入用户</el-dropdown-item>
                  <el-dropdown-item command="printTable">打印表格</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-tooltip content="刷新" placement="top">
              <el-button text @click="getList">
                <el-icon size="18"><Refresh /></el-icon>
              </el-button>
            </el-tooltip>
            <el-tooltip content="列设置" placement="top">
              <el-button text @click="showColumnSetting = true">
                <el-icon><span class="iconfont icon-columnSetting"></span></el-icon>
              </el-button>
            </el-tooltip>
          </div>
        </div>
      </template>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="userList"
        border
        @selection-change="handleSelectionChange"
        highlight-current-row
        row-key="userId"
      >
        <el-table-column type="selection" width="50" align="center" fixed="left" />
        <el-table-column type="index" label="序号" width="60" align="center" fixed="left" />
        <el-table-column
          label="用户编号"
          prop="userId"
          align="center"
          width="85"
          v-if="columns.userId.visible"
        />
        <el-table-column label="用户头像" align="center" width="85" v-if="columns.avatar.visible">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatar || defaultAvatar">
              <el-icon><UserFilled /></el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column
          label="用户名"
          prop="username"
          align="center"
          min-width="90"
          show-overflow-tooltip
          v-if="columns.username.visible"
        />
        <el-table-column
          label="手机号码"
          prop="phone"
          align="center"
          min-width="120"
          show-overflow-tooltip
          v-if="columns.phone.visible"
        />
        <el-table-column
          label="邮箱"
          prop="email"
          align="center"
          min-width="180"
          show-overflow-tooltip
          v-if="columns.email.visible"
        />
        <el-table-column label="角色" align="center" width="90" v-if="columns.role.visible">
          <template #default="{ row }">
            <el-tag :type="row.role === UserRoles.ADMIN ? 'danger' : 'info'" effect="plain">
              {{ row.role === UserRoles.ADMIN ? '管理员' : '普通用户' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="简介"
          prop="introduction"
          align="center"
          min-width="120"
          show-overflow-tooltip
          v-if="columns.introduction.visible"
        />
        <el-table-column label="状态" align="center" width="80" v-if="columns.status.visible">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="'1'"
              :inactive-value="'0'"
              @change="handleStatusChange(row)"
              :disabled="!hasPermission('user:edit')"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          prop="createTime"
          align="center"
          min-width="160"
          sortable
          v-if="columns.createTime.visible"
        />
        <el-table-column
          label="修改时间"
          prop="updateTime"
          align="center"
          min-width="160"
          sortable
          v-if="columns.updateTime.visible"
        />
        <el-table-column label="操作" align="center" width="220" fixed="right">
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              @click="handleEdit(row)"
              v-if="hasPermission('user:edit')"
            >
              <el-icon><Edit /></el-icon> 修改
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(row)"
              v-if="hasPermission('user:delete')"
            >
              <el-icon><Delete /></el-icon> 删除
            </el-button>
            <el-button link type="warning" @click="handleResetPassword(row)">
              <el-icon><Key /></el-icon> 重置密码
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </el-card>

    <!-- 用户表单对话框 -->
    <user-dialog
      :dialog-props="dialogProps"
      @update:visible="dialogProps.visible = $event"
      @success="handleDialogSuccess"
    />

    <!-- 重置密码确认框 -->
    <el-dialog v-model="resetPwdDialog.visible" title="重置密码" width="400px" append-to-body>
      <p>确定重置该用户密码吗？重置后密码将变为默认密码。</p>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetPwdDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmResetPassword">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入用户对话框 -->
    <el-dialog v-model="importDialog.visible" title="导入用户" width="400px" append-to-body>
      <el-upload
        class="upload-demo"
        :action="importDialog.uploadUrl"
        :headers="importDialog.headers"
        :on-success="handleImportSuccess"
        :on-error="handleImportError"
        accept=".xlsx, .xls"
      >
        <el-button type="primary">点击上传</el-button>
        <template #tip>
          <div class="el-upload__tip">请上传Excel文件，文件大小不超过2MB</div>
        </template>
      </el-upload>
      <div class="mt-3">
        <el-button type="text" @click="handleDownloadTemplate">
          <el-icon><Download /></el-icon> 下载导入模板
        </el-button>
      </div>
    </el-dialog>

    <!-- 列设置抽屉 -->
    <el-drawer v-model="showColumnSetting" title="列设置" direction="rtl" size="300px">
      <el-divider>表格显示列</el-divider>
      <el-checkbox-group v-model="checkedColumns">
        <div v-for="(col, key) in columns" :key="key" class="column-item">
          <el-checkbox :label="key">{{ col.label }}</el-checkbox>
        </div>
      </el-checkbox-group>
      <template #footer>
        <div class="drawer-footer">
          <el-button @click="resetColumns">重置</el-button>
          <el-button type="primary" @click="saveColumns">确定</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'UserView',
})
import {
  Refresh,
  ArrowDown,
  Delete,
  Download,
  Edit,
  Key,
  UserFilled,
  More,
} from '@element-plus/icons-vue'
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import UserDialog from './components/UserDialog.vue'
import { useUserStore } from '@/stores/user'
import { UserRoles } from '@/types/user'
import type { UserInfo } from '@/types/user'
import usePermission from '@/hooks/usePermission'
import { exportExcel } from '@/utils/export'
import { useRouter } from 'vue-router'
import { DataAnalysis } from '@element-plus/icons-vue'

// 默认头像
const defaultAvatar = ref('/favicon.ico')

// 使用权限hook
const { hasPermission } = usePermission()

// 使用用户store
const userStore = useUserStore()
const { userList, total, loading, queryParams } = storeToRefs(userStore)
const getList = () => userStore.getList()

// 选中的用户
const selectedUsers = ref<UserInfo[]>([])

// 日期范围
const dateRange = ref<string[]>([])

// 日期快捷选项
const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
]

// 弹窗属性
interface DialogProps {
  visible: boolean
  title: string
  type: 'add' | 'edit'
  data?: Partial<UserInfo>
}

const dialogProps = reactive<DialogProps>({
  visible: false,
  title: '',
  type: 'add',
  data: undefined,
})

// 重置密码对话框
const resetPwdDialog = reactive({
  visible: false,
  userId: undefined as undefined | number,
})

// 导入 对话框
const importDialog = reactive({
  visible: false,
  uploadUrl: import.meta.env.VITE_API_BASE_URL + '/user/import',
  headers: {
    Authorization: 'Bearer ' + localStorage.getItem('token'),
  },
})

// 列设置
const showColumnSetting = ref(false)

interface ColumnItem {
  visible: boolean
  label: string
}

type ColumnsType = {
  [key: string]: ColumnItem
}

const columns = reactive<ColumnsType>({
  userId: { visible: true, label: '用户编号' },
  avatar: { visible: true, label: '用户头像' },
  username: { visible: true, label: '用户名' },
  phone: { visible: true, label: '手机号码' },
  email: { visible: true, label: '邮箱' },
  role: { visible: true, label: '角色' },
  introduction: { visible: true, label: '简介' },
  status: { visible: true, label: '状态' },
  createTime: { visible: true, label: '创建时间' },
  updateTime: { visible: true, label: '修改时间' },
})

// 选中的列
const checkedColumns = ref(Object.keys(columns).filter((key) => columns[key].visible))

// 监听日期范围变化
watch(dateRange, (val) => {
  queryParams.value.beginTime = val && val.length > 0 ? val[0] + ' 00:00:00' : undefined
  queryParams.value.endTime = val && val.length > 1 ? val[1] + ' 23:59:59' : undefined
})

const router = useRouter()
// 初始化加载数据
onMounted(async () => {
  console.log('🔄 用户管理页面初始化')

  // 确保用户已登录
  const token = localStorage.getItem('token')

  if (!token) {
    console.warn('⚠️ 未找到token,跳转登录页')
    router.push('/login')
    return
  }

  // 确保有用户信息
  if (!userStore.userInfo) {
    console.log('🔄 获取用户信息')
    try {
      await userStore.getUserInfo()
    } catch (error) {
      console.error('❌ 获取用户信息失败:', error)
      ElMessage.error('获取用户信息失败，请重新登录')
      router.push('/login')
      return
    }
  }

  // 加载用户列表
  console.log('🔄 加载用户列表')
  await userStore.getList()

  // 加载本地存储的列配置
  const savedColumns = localStorage.getItem('userTableColumns')
  if (savedColumns) {
    try {
      const parsedColumns = JSON.parse(savedColumns)
      Object.keys(parsedColumns).forEach((key) => {
        if (columns[key]) {
          columns[key].visible = parsedColumns[key].visible
        }
      })
      checkedColumns.value = Object.keys(columns).filter((key) => columns[key].visible)
    } catch (e) {
      console.error('加载列配置失败', e)
    }
  }
})

// 处理多选框选择事件
const handleSelectionChange = (selection: UserInfo[]) => {
  selectedUsers.value = selection
}

// 搜索
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

// 重置
const handleReset = () => {
  dateRange.value = []
  userStore.resetQuery()
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  queryParams.value.pageSize = val
  getList()
}

// 页码变化
const handleCurrentChange = (val: number) => {
  queryParams.value.pageNum = val
  getList()
}

// 新增用户
const handleAdd = () => {
  dialogProps.visible = true
  dialogProps.title = '新增用户'
  dialogProps.type = 'add'
  dialogProps.data = undefined
}

// 编辑用户
const handleEdit = (row: UserInfo) => {
  dialogProps.visible = true
  dialogProps.title = '编辑用户'
  dialogProps.type = 'edit'
  dialogProps.data = { ...row }
}
// 删除用户
const handleDelete = (row: UserInfo) => {
  ElMessageBox.confirm(`确认删除用户【${row.username}】吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      await userStore.deleteUser(row.userId)

      await userStore.getList()
    })
    .catch(() => {})
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请至少选择一个用户')
    return
  }

  const usernames = selectedUsers.value.map((item) => item.username).join('、')
  const userIds = selectedUsers.value.map((item) => item.userId)

  try {
    await ElMessageBox.confirm(`确认删除以下用户吗？<br/>${usernames}`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
    })

    await userStore.deleteBatchUsers(userIds)
    // 批量删除成功后刷新列表
    await userStore.getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败', error)
    }
  }
}
// 处理对话框提交
const handleDialogSuccess = async (data: Partial<UserInfo>, type: 'add' | 'edit') => {
  try {
    if (type === 'add') {
      await userStore.addUser(data)
    } else {
      await userStore.updateUser(data)
    }
    // 确保操作成功后刷新列表
    console.log(getList())
    await userStore.getList()
    console.log(getList())
  } catch (error) {
    console.error('操作失败:', error)
  }
}
// 重置密码
const handleResetPassword = (row: UserInfo) => {
  resetPwdDialog.userId = row.userId
  resetPwdDialog.visible = true
}
// 确认重置密码
const confirmResetPassword = async () => {
  if (!resetPwdDialog.userId) return

  try {
    // 调用重置密码API
    await userStore.resetUserPassword(resetPwdDialog.userId)
    resetPwdDialog.visible = false
  } catch (error) {
    console.error('密码重置失败', error)
  }
}
// 更多操作
const handleCommand = (command: string) => {
  switch (command) {
    case 'importUsers':
      importDialog.visible = true
      break
    case 'printTable':
      window.print()
      break
  }
}

// 修改状态
const handleStatusChange = async (row: UserInfo) => {
  try {
    await userStore.updateUserStatus(row.userId, row.status)
    // 状态修改成功后刷新列表
    await userStore.getList()
  } catch (error) {
    // 恢复原状态
    console.error('修改状态失败', error)
    row.status = row.status === '1' ? '0' : '1'
  }
}
// 处理导出
const handleExport = () => {
  // 构建导出数据
  const header = ['用户名', '手机号码', '邮箱', '角色', '状态', '创建时间']
  const data = userList.value.map((item: UserInfo) => [
    item.username,
    item.phone,
    item.email,
    item.role === UserRoles.ADMIN ? '管理员' : '普通用户',
    item.status === '1' ? '启用' : '禁用',
    item.createTime,
  ])

  exportExcel({
    header,
    data,
    fileName: '用户数据',
    autoWidth: true,
    bookType: 'xlsx',
  })
}

// 导入成功
interface ImportResponse {
  code: number
  message?: string
  data?: unknown
}
const handleImportSuccess = (response: ImportResponse) => {
  if (response.code === 200) {
    ElMessage.success('导入成功')
    importDialog.visible = false
    getList()
  } else {
    ElMessage.error(response.message || '导入失败')
  }
}

// 导入失败
const handleImportError = () => {
  ElMessage.error('导入失败')
}

// 下载导入模板
const handleDownloadTemplate = () => {
  // 实际项目中替换为后端API
  window.location.href = import.meta.env.VITE_API_BASE_URL + '/user/download/template'
}

// 重置列设置
const resetColumns = () => {
  Object.keys(columns).forEach((key) => {
    columns[key].visible = true
  })
  checkedColumns.value = Object.keys(columns)
}

// 保存列设置
const saveColumns = () => {
  // 更新列可见性
  Object.keys(columns).forEach((key) => {
    columns[key].visible = checkedColumns.value.includes(key)
  })

  // 保存到本地存储
  localStorage.setItem('userTableColumns', JSON.stringify(columns))

  showColumnSetting.value = false
  ElMessage.success('列设置保存成功')
}

const addMockData = () => {
  // 生成随机日期(过去30天内)
  const randomDate = () => {
    const date = new Date()
    const daysAgo = Math.floor(Math.random() * 30)
    date.setDate(date.getDate() - daysAgo)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  // 生成随机手机号
  const randomPhone = () => {
    return `138${Math.floor(Math.random() * 100000000)
      .toString()
      .padStart(8, '0')}`
  }

  // 模拟用户数据
  const mockUsers: UserInfo[] = [
    {
      userId: 1,
      username: 'admin',
      phone: '13800000000',
      email: '<EMAIL>',
      role: UserRoles.ADMIN,
      status: '1',
      createTime: randomDate(),
      updateTime: randomDate(),
      introduction: '系统管理员',
    },
    {
      userId: 2,
      username: 'test1',
      phone: '13800000001',
      email: '<EMAIL>',
      role: UserRoles.USER,
      status: '1',
      createTime: randomDate(),
      updateTime: randomDate(),
      introduction: '普通用户1',
    },
    {
      userId: 3,
      username: 'test2',
      phone: '13800000002',
      email: '<EMAIL>',
      role: UserRoles.USER,
      status: '0',
      createTime: randomDate(),
      updateTime: randomDate(),
      introduction: '已禁用用户',
    },
    {
      userId: 4,
      username: 'manager',
      phone: randomPhone(),
      email: '<EMAIL>',
      role: UserRoles.ADMIN,
      status: '1',
      createTime: randomDate(),
      updateTime: randomDate(),
      introduction: '部门经理',
    },
    {
      userId: 5,
      username: 'user1',
      phone: randomPhone(),
      email: '<EMAIL>',
      role: UserRoles.USER,
      status: '1',
      createTime: randomDate(),
      updateTime: randomDate(),
      introduction: '普通用户示例',
    },
    {
      userId: 6,
      username: 'inactive',
      phone: randomPhone(),
      email: '<EMAIL>',
      role: UserRoles.USER,
      status: '0',
      createTime: randomDate(),
      updateTime: randomDate(),
      introduction: '长期未活跃用户',
    },
    {
      userId: 7,
      username: 'tester',
      phone: randomPhone(),
      email: '<EMAIL>',
      role: UserRoles.USER,
      status: '1',
      createTime: randomDate(),
      updateTime: randomDate(),
      introduction: '测试账号',
    },
  ]

  // 直接设置到store中
  userStore.$patch({
    userList: mockUsers,
    total: mockUsers.length,
  })

  ElMessage.success('已添加7条模拟用户数据')
}
</script>
<style scoped>
.user-container {
  padding: 16px;
}
.search-form {
  @apply flex flex-wrap items-center justify-between;
}
.search-buttons {
  @apply ml-auto mb-2;
}

.search-card :deep(.el-form) {
  @apply flex flex-wrap items-center;
}

.search-card :deep(.el-form-item) {
  @apply mb-2 mr-4;
}

.search-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.button-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.column-item {
  margin: 8px 0;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  gap: 8px;
}

/* 打印样式 */
@media print {
  .search-card,
  .card-header,
  .pagination-container,
  .el-table__column--selection,
  .el-table__column--fix-right {
    display: none;
  }
}
</style>
