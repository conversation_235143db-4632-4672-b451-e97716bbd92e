<template>
  <div class="layout-container">
    <SideBar class="layout-sidebar" />
    <div class="layout-content">
      <NavBar />
      <main class="main-content">
        <router-view />
      </main>
    </div>
  </div>
</template>
l
<script setup lang="ts">
defineOptions({
  name: 'MainLayout',
})
</script>

<style scoped>
.layout-container {
  @apply flex h-screen w-full overflow-hidden;
}
.layout-sidebar {
  @apply w-[220px] h-full bg-white flex-shrink-0;
  border-right: 1px solid #e5e7eb;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
}
.layout-content {
  @apply flex flex-col flex-1 overflow-hidden;
}
.main-content {
  @apply flex-1 p-5 overflow-y-auto bg-[#f5f7fa];
}
</style>
