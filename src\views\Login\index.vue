<template>
  <div class="login-container">
    <div class="decorative-circle"></div>
    <div class="music-note note-1"></div>
    <div class="music-note note-2"></div>
    <div class="music-note note-3"></div>
    <div class="music-note note-4">&#9833;</div>
    <div class="music-note note-5">&#9834;</div>
    <div class="wave wave-1"></div>
    <div class="wave wave-2"></div>
    <div class="vinyl-record"></div>
    <div class="music-bar bar-1"></div>
    <div class="music-bar bar-2"></div>
    <div class="music-bar bar-3"></div>

    <div class="login-left">
      <img src="@/assets/images/music-illustration.jpg" alt="音乐派对" class="login-image" />
    </div>

    <div class="login-right">
      <div class="login-box">
        <!-- 音乐图标 -->
        <div class="logo-container">
          <div class="logo-circle">
            <i class="iconfont icon-login-music"></i>
          </div>
        </div>

        <div class="login-header">
          <h2 class="text-2xl font-bold text-center mb-6">HappyMusic 管理系统</h2>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          @keyup.enter="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="请输入用户名"
              :prefix-icon="User"
              clearable
              size="large"
              class="custom-input"
            />
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              :prefix-icon="Lock"
              show-password
              clearable
              size="large"
              class="custom-input"
            />
          </el-form-item>

          <el-form-item class="remember-forgot-container">
            <div class="flex justify-between items-center w-full">
              <el-checkbox v-model="rememberMe">记住密码</el-checkbox>
              <el-link type="primary" @click="showForgotPasswordDialog">忘记密码？</el-link>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              :loading="loading"
              class="login-button"
              size="large"
              color="#5350e9"
              @click="handleLogin"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>

        <div class="login-footer mt-4 text-center text-gray-500">
          <p>© {{ new Date().getFullYear() }} HappyMusic 版权所有</p>
        </div>
      </div>
    </div>

    <!-- 忘记密码对话框 -->
    <el-dialog
      v-model="forgotPasswordVisible"
      title="找回密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="forgot-password-content">
        <div class="question-section">
          <h4 class="text-lg font-semibold mb-4 text-center">🤔 安全问题</h4>
          <p class="text-center mb-6 text-gray-600">你认为谁最帅？</p>

          <el-input
            v-model="securityAnswer"
            placeholder="请输入答案"
            size="large"
            class="mb-4"
            @keyup.enter="handleForgotPassword"
          />

          <div v-if="showPasswordForm" class="password-form">
            <el-alert
              title="安全问题验证通过！请设置新密码"
              type="success"
              :closable="false"
              show-icon
              class="mb-4"
            />
            <el-input
              v-model="newPassword"
              type="password"
              placeholder="请输入新密码（至少6位）"
              size="large"
              class="mb-4"
              show-password
              @keyup.enter="handleResetPassword"
            />
            <el-input
              v-model="confirmPassword"
              type="password"
              placeholder="请确认新密码"
              size="large"
              class="mb-4"
              show-password
              @keyup.enter="handleResetPassword"
            />
          </div>

          <div v-if="resetSuccess" class="reset-result">
            <el-alert
              title="密码重置成功！请使用新密码登录"
              type="success"
              :closable="false"
              show-icon
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeForgotPasswordDialog">
            {{ resetSuccess ? '关闭' : '取消' }}
          </el-button>
          <el-button
            v-if="!showPasswordForm && !resetSuccess"
            type="primary"
            @click="handleForgotPassword"
            :disabled="!securityAnswer.trim()"
          >
            验证答案
          </el-button>
          <el-button
            v-if="showPasswordForm"
            type="primary"
            @click="handleResetPassword"
            :disabled="!newPassword.trim() || !confirmPassword.trim()"
          >
            重置密码
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { User, Lock } from '@element-plus/icons-vue'
import { ElMessage, type FormInstance } from 'element-plus'
import { useUserStore } from '@/stores/user'
defineOptions({
  name: 'LoginView',
})

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)
const rememberMe = ref(false)
const loginFormRef = ref<FormInstance>()

// 忘记密码相关
const forgotPasswordVisible = ref(false)
const securityAnswer = ref('')
const showPasswordForm = ref(false)
const newPassword = ref('')
const confirmPassword = ref('')
const resetSuccess = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: '',
})

// 表单验证规则
const loginRules = reactive({
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
})

// 加载本地存储的登录信息
onMounted(() => {
  const savedUsername = localStorage.getItem('rememberedUsername')
  const savedPassword = localStorage.getItem('rememberedPassword')
  const savedTime = localStorage.getItem('rememberedTime')

  if (savedUsername && savedPassword && savedTime) {
    const savedDate = new Date(savedTime)
    const currentDate = new Date()
    const daysDiff = Math.floor((currentDate.getTime() - savedDate.getTime()) / (1000 * 60 * 60 * 24))

    // 如果保存时间在3天内，自动填充用户名和密码
    if (daysDiff <= 3) {
      loginForm.username = savedUsername
      loginForm.password = savedPassword
      rememberMe.value = true
    } else {
      // 超过3天，清除保存的密码，只保留用户名
      localStorage.removeItem('rememberedPassword')
      localStorage.removeItem('rememberedTime')
      loginForm.username = savedUsername
      rememberMe.value = false
    }
  } else if (savedUsername) {
    loginForm.username = savedUsername
    rememberMe.value = false
  }
})

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.warning('请完成登录验证')
      return
    }

    loading.value = true
    try {
      const result = await userStore.login(loginForm.username, loginForm.password)

      if (result) {
        // 记住密码逻辑
        if (rememberMe.value) {
          localStorage.setItem('rememberedUsername', loginForm.username)
          localStorage.setItem('rememberedPassword', loginForm.password)
          localStorage.setItem('rememberedTime', new Date().toISOString())
        } else {
          localStorage.removeItem('rememberedUsername')
          localStorage.removeItem('rememberedPassword')
          localStorage.removeItem('rememberedTime')
        }
        ElMessage.success('登录成功')
        // 获取redirect参数，如果有则跳转到指定页面
        const redirect = router.currentRoute.value.query.redirect as string
        router.push(redirect || '/')
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : '登录失败，请检查用户名和密码'
      ElMessage.error(errorMessage)
      console.error('登录失败:', error)
    } finally {
      loading.value = false
    }
  })
}

// 显示忘记密码对话框
const showForgotPasswordDialog = () => {
  if (!loginForm.username.trim()) {
    ElMessage.warning('请先输入用户名')
    return
  }
  forgotPasswordVisible.value = true
  securityAnswer.value = ''
  showPasswordForm.value = false
  newPassword.value = ''
  confirmPassword.value = ''
  resetSuccess.value = false
}

// 关闭忘记密码对话框
const closeForgotPasswordDialog = () => {
  forgotPasswordVisible.value = false
  securityAnswer.value = ''
  showPasswordForm.value = false
  newPassword.value = ''
  confirmPassword.value = ''
  resetSuccess.value = false
}

// 处理忘记密码 - 验证安全问题
const handleForgotPassword = () => {
  const answer = securityAnswer.value.trim()

  // 验证安全问题答案
  const correctAnswers = ['yjp','嘉鹏','jpzz']

  if (correctAnswers.includes(answer.toLowerCase())) {
    showPasswordForm.value = true
    ElMessage.success('安全问题验证通过！请设置新密码')
  }
  else if(correctAnswers.includes(answer.toUpperCase())){
    showPasswordForm.value = true
    ElMessage.success('安全问题验证通过！请设置新密码')
  }
  else {
    ElMessage.error('答案错误，请重新输入')
    console.log(answer)
    securityAnswer.value = ''
  }
}

// 处理密码重置
const handleResetPassword = async () => {
  // 验证新密码
  if (newPassword.value.length < 6) {
    ElMessage.error('新密码长度不能少于6位')
    return
  }

  if (newPassword.value !== confirmPassword.value) {
    ElMessage.error('两次输入的密码不一致')
    return
  }

  try {
    // 调用后端API重置密码
    await userStore.forgotPassword(loginForm.username, securityAnswer.value, newPassword.value)
    resetSuccess.value = true
    showPasswordForm.value = false
    ElMessage.success('密码重置成功！请使用新密码登录')

    // 3秒后关闭对话框
    setTimeout(() => {
      closeForgotPasswordDialog()
    }, 3000)
  } catch (error: unknown) {
    // 处理API错误响应
    let errorMessage = '密码重置失败，请重试'
    if (error && typeof error === 'object') {
      const apiError = error as { response?: { data?: { message?: string } }; message?: string }
      if (apiError.response?.data?.message) {
        errorMessage = apiError.response.data.message
      } else if (apiError.message) {
        errorMessage = apiError.message
      }
    }
    ElMessage.error(errorMessage)
  }
}
</script>

<style scoped>
.login-container {
  @apply h-screen flex;
  background-color: white;
  position: relative;
  overflow: hidden;
}

.decorative-circle {
  position: absolute;
  right: -15%;
  top: -15%;
  width: 70%;
  height: 140%;
  background-color: #e6f0ff; /* 调整为更柔和的蓝色 */
  border-radius: 50%;
  z-index: 0;
  box-shadow: inset -15px 15px 25px rgba(180, 200, 255, 0.3); /* 添加内阴影效果 */
}

/* 音符装饰 - 调整位置 */
.music-note {
  position: absolute;
  z-index: 0;
  border-radius: 50%;
  border: 2px solid rgba(83, 80, 233, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.music-note::before {
  content: '♪';
  font-size: 18px;
  color: rgba(83, 80, 233, 0.6);
}

.note-1 {
  width: 40px;
  height: 40px;
  top: 20%;
  right: 30%;
  animation: float 8s infinite ease-in-out;
}

.note-2 {
  width: 35px;
  height: 35px;
  top: 35%;
  right: 18%;
  animation: float 10s infinite ease-in-out 1s;
}

.note-3 {
  width: 30px;
  height: 30px;
  top: 60%;
  right: 25%;
  animation: float 7s infinite ease-in-out 0.5s;
}

.note-4 {
  width: 40px;
  height: 40px;
  top: 72%;
  right: 40%;
  animation: float 12s infinite ease-in-out 1.5s;
}

.note-4::before,
.note-5::before {
  content: none; /* 不使用默认音符 */
}

.note-5 {
  width: 32px;
  height: 32px;
  top: 15%;
  right: 45%;
  animation: float 9s infinite ease-in-out 2s;
}

/* 波浪线装饰 */
.wave {
  position: absolute;
  z-index: 0;
  opacity: 0.4;
  background: radial-gradient(
    circle at center,
    rgba(83, 80, 233, 0) 0%,
    rgba(83, 80, 233, 0) 50%,
    rgba(83, 80, 233, 0.2) 51%,
    rgba(83, 80, 233, 0) 52%
  );
  border-radius: 50%;
}

.wave-1 {
  width: 100px;
  height: 100px;
  top: 30%;
  right: 35%;
  animation: pulse 10s infinite ease-in-out;
}

.wave-2 {
  width: 150px;
  height: 150px;
  top: 50%;
  right: 20%;
  animation: pulse 15s infinite ease-in-out 2s;
}

/* 唱片装饰 */
.vinyl-record {
  position: absolute;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(45deg, #333 0%, #666 100%);
  top: 45%;
  right: 38%;
  z-index: 0;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  animation: rotate 20s infinite linear;
}

.vinyl-record::before {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #e6f0ff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.vinyl-record::after {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #333;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 音乐条 */
.music-bar {
  position: absolute;
  z-index: 0;
  background-color: rgba(83, 80, 233, 0.3);
  border-radius: 2px;
  width: 4px;
}

.bar-1 {
  height: 15px;
  top: 25%;
  right: 15%;
  animation: barHeight 1.5s infinite ease-in-out;
}

.bar-2 {
  height: 25px;
  top: 25%;
  right: 12%;
  animation: barHeight 2s infinite ease-in-out 0.5s;
}

.bar-3 {
  height: 20px;
  top: 25%;
  right: 9%;
  animation: barHeight 1.2s infinite ease-in-out 0.3s;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes barHeight {
  0%,
  100% {
    height: 15px;
  }
  50% {
    height: 30px;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.4;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.2;
  }
}

.login-left {
  @apply flex-1 relative flex items-center justify-center;
  z-index: 1;
}

.login-image {
  @apply w-4/5 max-w-full object-contain;
}

.login-right {
  @apply flex-1 flex items-center justify-center;
  z-index: 1;
}

.login-box {
  @apply p-8 w-[420px];
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05); /* 添加轻微阴影效果 */
  border-radius: 12px;
}

.logo-container {
  @apply flex justify-center mb-6;
}

/* 自定义输入框尺寸 */
.custom-input {
  width: 100%;
}

:deep(.custom-input .el-input__wrapper) {
  @apply py-1;
  height: 42px;
}

/* 忘记密码和记住我布局 */
.remember-forgot-container {
  margin-bottom: 20px;
}

/* 自定义登录按钮尺寸 */
.login-button {
  width: 80%;
  margin: 0 auto;
  display: block;
  height: 42px;
  font-size: 15px;
}

:deep(.el-button--primary) {
  background-color: #5350e9;
  border: none;
  box-shadow: 0 4px 12px rgba(83, 80, 233, 0.3); /* 按钮阴影 */
  transition: all 0.3s ease;
}

:deep(.el-button--primary:hover) {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(83, 80, 233, 0.35);
}

/* 适配移动设备 */
@media (max-width: 768px) {
  .login-container {
    @apply flex-col;
  }

  .decorative-circle {
    width: 150%;
    height: 70%;
    top: auto;
    right: -25%;
    bottom: -15%;
  }

  .vinyl-record {
    top: 65%;
    right: 20%;
  }

  .note-1,
  .note-2,
  .note-3,
  .note-4,
  .note-5 {
    right: 20%;
  }

  .wave-1,
  .wave-2 {
    right: 30%;
  }

  .bar-1,
  .bar-2,
  .bar-3 {
    top: 60%;
  }

  .login-left {
    @apply h-1/3;
  }

  .login-right {
    @apply h-2/3;
  }

  .login-box {
    @apply w-[90%];
  }
}

/* 忘记密码对话框样式 */
.forgot-password-content {
  padding: 20px 0;
}

.question-section {
  text-align: center;
}

.password-form {
  margin-top: 20px;
}

.reset-result {
  margin-top: 20px;
}

:deep(.el-dialog__header) {
  text-align: center;
  padding-bottom: 10px;
}

:deep(.el-dialog__body) {
  padding-top: 10px;
}

.dialog-footer {
  text-align: center;
}
</style>
