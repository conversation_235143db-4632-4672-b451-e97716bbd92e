package com.jpzz.controller.client;

import com.jpzz.pojo.dto.client.PlayRecordDTO;
import com.jpzz.pojo.vo.client.ClientSongVO;
import com.jpzz.pojo.vo.client.ClientPlaylistVO;
import com.jpzz.result.PageResult;
import com.jpzz.result.Result;
import com.jpzz.service.client.ClientMusicService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户端音乐控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/client/music")
@Tag(name = "客户端音乐", description = "客户端音乐相关接口")
@Slf4j
public class ClientMusicController {

    @Resource
    private ClientMusicService clientMusicService;

    @GetMapping("/recommend/songs")
    @Operation(summary = "获取推荐歌曲", description = "基于用户喜好推荐歌曲")
    public Result<PageResult<ClientSongVO>> getRecommendSongs(
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("获取推荐歌曲，用户ID：{}，页码：{}，每页大小：{}", userId, page, size);
        PageResult<ClientSongVO> result = clientMusicService.getRecommendSongs(userId, page, size);
        return Result.success("获取成功", result);
    }

    @GetMapping("/hot/songs")
    @Operation(summary = "获取热门歌曲", description = "获取当前热门歌曲")
    public Result<PageResult<ClientSongVO>> getHotSongs(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("获取热门歌曲，页码：{}，每页大小：{}", page, size);
        PageResult<ClientSongVO> result = clientMusicService.getHotSongs(page, size);
        return Result.success("获取成功", result);
    }

    @GetMapping("/new/songs")
    @Operation(summary = "获取新歌推荐", description = "获取最新发布的歌曲")
    public Result<PageResult<ClientSongVO>> getNewSongs(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("获取新歌推荐，页码：{}，每页大小：{}", page, size);
        PageResult<ClientSongVO> result = clientMusicService.getNewSongs(page, size);
        return Result.success("获取成功", result);
    }

    @GetMapping("/hot/playlists")
    @Operation(summary = "获取热门歌单", description = "获取当前热门歌单")
    public Result<PageResult<ClientPlaylistVO>> getHotPlaylists(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("获取热门歌单，页码：{}，每页大小：{}", page, size);
        PageResult<ClientPlaylistVO> result = clientMusicService.getHotPlaylists(page, size);
        return Result.success("获取成功", result);
    }

    @GetMapping("/song/{songId}")
    @Operation(summary = "获取歌曲详情", description = "根据ID获取歌曲详细信息")
    public Result<ClientSongVO> getSongDetail(
            @Parameter(description = "歌曲ID") @PathVariable Long songId,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId) {
        log.info("获取歌曲详情，歌曲ID：{}，用户ID：{}", songId, userId);
        ClientSongVO result = clientMusicService.getSongDetail(songId, userId);
        return Result.success("获取成功", result);
    }



    @GetMapping("/playlist/{playlistId}")
    @Operation(summary = "获取歌单详情", description = "根据ID获取歌单详细信息")
    public Result<ClientPlaylistVO> getPlaylistDetail(
            @Parameter(description = "歌单ID") @PathVariable Long playlistId,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId) {
        log.info("获取歌单详情，歌单ID：{}，用户ID：{}", playlistId, userId);
        ClientPlaylistVO result = clientMusicService.getPlaylistDetail(playlistId, userId);
        return Result.success("获取成功", result);
    }



    @GetMapping("/playlist/{playlistId}/songs")
    @Operation(summary = "获取歌单歌曲", description = "获取歌单中的歌曲列表")
    public Result<PageResult<ClientSongVO>> getPlaylistSongs(
            @Parameter(description = "歌单ID") @PathVariable Long playlistId,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("获取歌单歌曲，歌单ID：{}，用户ID：{}，页码：{}，每页大小：{}", playlistId, userId, page, size);
        PageResult<ClientSongVO> result = clientMusicService.getPlaylistSongs(playlistId, userId, page, size);
        return Result.success("获取成功", result);
    }



    @GetMapping("/search/songs")
    @Operation(summary = "搜索歌曲", description = "根据关键词搜索歌曲")
    public Result<PageResult<ClientSongVO>> searchSongs(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("搜索歌曲，关键词：{}，用户ID：{}，页码：{}，每页大小：{}", keyword, userId, page, size);
        PageResult<ClientSongVO> result = clientMusicService.searchSongs(keyword, userId, page, size);
        return Result.success("搜索成功", result);
    }

    @GetMapping("/search/playlists")
    @Operation(summary = "搜索歌单", description = "根据关键词搜索歌单")
    public Result<PageResult<ClientPlaylistVO>> searchPlaylists(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("搜索歌单，关键词：{}，用户ID：{}，页码：{}，每页大小：{}", keyword, userId, page, size);
        PageResult<ClientPlaylistVO> result = clientMusicService.searchPlaylists(keyword, userId, page, size);
        return Result.success("搜索成功", result);
    }

    @PostMapping("/play/record")
    @Operation(summary = "记录播放", description = "记录用户播放行为")
    public Result<Void> recordPlay(@Validated @RequestBody PlayRecordDTO playRecordDTO) {
        log.info("记录播放行为：{}", playRecordDTO);
        clientMusicService.recordPlay(playRecordDTO);
        return Result.success("记录成功");
    }

    @GetMapping("/recent/plays")
    @Operation(summary = "获取最近播放", description = "获取用户最近播放的歌曲")
    public Result<PageResult<ClientSongVO>> getRecentPlays(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("获取最近播放，用户ID：{}，页码：{}，每页大小：{}", userId, page, size);
        PageResult<ClientSongVO> result = clientMusicService.getRecentPlays(userId, page, size);
        return Result.success("获取成功", result);
    }

    @PostMapping("/like/song")
    @Operation(summary = "喜欢歌曲", description = "喜欢或取消喜欢歌曲")
    public Result<Void> likeSong(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "歌曲ID") @RequestParam Long songId,
            @Parameter(description = "是否喜欢") @RequestParam Boolean isLike) {
        log.info("{}歌曲，用户ID：{}，歌曲ID：{}", isLike ? "喜欢" : "取消喜欢", userId, songId);
        clientMusicService.likeSong(userId, songId, isLike);
        return Result.success(isLike ? "喜欢成功" : "取消喜欢成功");
    }

    @PostMapping("/collect/song")
    @Operation(summary = "收藏歌曲", description = "收藏或取消收藏歌曲")
    public Result<Void> collectSong(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "歌曲ID") @RequestParam Long songId,
            @Parameter(description = "是否收藏") @RequestParam Boolean isCollect) {
        log.info("{}歌曲，用户ID：{}，歌曲ID：{}", isCollect ? "收藏" : "取消收藏", userId, songId);
        clientMusicService.collectSong(userId, songId, isCollect);
        return Result.success(isCollect ? "收藏成功" : "取消收藏成功");
    }

    @GetMapping("/user/liked/songs")
    @Operation(summary = "获取喜欢的歌曲", description = "获取用户喜欢的歌曲列表")
    public Result<PageResult<ClientSongVO>> getUserLikedSongs(
            @Parameter(description = "用户ID") @RequestParam Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("获取用户喜欢的歌曲，用户ID：{}，页码：{}，每页大小：{}", userId, page, size);
        PageResult<ClientSongVO> result = clientMusicService.getUserLikedSongs(userId, page, size);
        return Result.success("获取成功", result);
    }

    @GetMapping("/style/{style}/songs")
    @Operation(summary = "按风格获取歌曲", description = "根据音乐风格获取歌曲")
    public Result<PageResult<ClientSongVO>> getSongsByStyle(
            @Parameter(description = "音乐风格") @PathVariable String style,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") Integer size) {
        log.info("按风格获取歌曲，风格：{}，用户ID：{}，页码：{}，每页大小：{}", style, userId, page, size);
        PageResult<ClientSongVO> result = clientMusicService.getSongsByStyle(style, userId, page, size);
        return Result.success("获取成功", result);
    }

    @GetMapping("/singer/{singerId}/hot-songs")
    @Operation(summary = "获取歌手热门歌曲", description = "获取指定歌手的热门歌曲")
    public Result<List<ClientSongVO>> getSingerHotSongs(
            @Parameter(description = "歌手ID") @PathVariable Long singerId,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") Integer limit) {
        log.info("获取歌手热门歌曲，歌手ID：{}，用户ID：{}，数量限制：{}", singerId, userId, limit);
        List<ClientSongVO> result = clientMusicService.getSingerHotSongs(singerId, userId, limit);
        return Result.success("获取成功", result);
    }


}
