<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jpzz.mapper.client.ClientMusicMapper">

    <!-- ClientSongVO结果映射 -->
    <resultMap id="ClientSongVOMap" type="com.jpzz.pojo.vo.client.ClientSongVO">
        <id column="id" property="id"/>
        <result column="artist_id" property="artistId"/>
        <result column="artist_name" property="artistName"/>
        <result column="name" property="name"/>
        <result column="album" property="album"/>
        <result column="lyric" property="lyric"/>
        <result column="duration" property="duration"/>
        <result column="style" property="style"/>
        <result column="cover_url" property="coverUrl"/>
        <result column="audio_url" property="audioUrl"/>
        <result column="release_time" property="releaseTime"/>
        <result column="like_count" property="likeCount"/>
        <result column="play_count" property="playCount"/>
        <result column="is_liked" property="isLiked"/>
        <result column="is_collected" property="isCollected"/>
        <result column="recommend_score" property="recommendScore"/>
        <result column="create_time" property="createTime"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- ClientPlaylistVO结果映射 -->
    <resultMap id="ClientPlaylistVOMap" type="com.jpzz.pojo.vo.client.ClientPlaylistVO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="style" property="style"/>
        <result column="cover_url" property="coverUrl"/>
        <result column="song_count" property="songCount"/>
        <result column="play_count" property="playCount"/>
        <result column="like_count" property="likeCount"/>
        <result column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="is_public" property="isPublic"/>
        <result column="is_liked" property="isLiked"/>
        <result column="is_collected" property="isCollected"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 基于用户偏好推荐歌曲 -->
    <select id="selectRecommendSongsByUserPreference" resultMap="ClientSongVOMap">
        SELECT
            s.id,
            s.artist_id,
            si.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count,
            s.play_count,
            s.create_time,
            s.status,
            CASE WHEN ul.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_collected,
            (
                -- 推荐分数计算
                    (CASE WHEN s.style IN (
                        SELECT style FROM tb_play_record pr
                                              JOIN tb_song ps ON pr.song_id = ps.id
                        WHERE pr.user_id = #{userId}
                        GROUP BY style
                        ORDER BY COUNT(*) DESC
                        LIMIT 3
                    ) THEN 30 ELSE 0 END) +
                    (s.play_count * 0.0001) +
                    (s.like_count * 0.001) +
                    (CASE WHEN s.release_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 10 ELSE 0 END)
                ) as recommend_score
        FROM tb_song s
                 LEFT JOIN tb_singer si ON s.artist_id = si.singer_id
                 LEFT JOIN tb_user_like ul ON ul.user_id = #{userId} AND ul.target_id = s.id AND ul.target_type = 'SONG'
                 LEFT JOIN tb_user_collection uc ON uc.user_id = #{userId} AND uc.target_id = s.id AND uc.target_type = 'SONG'
        WHERE s.deleted = 0 AND s.status = 'ACTIVE'
        ORDER BY recommend_score DESC, s.play_count DESC
    </select>

    <!-- 获取热门歌曲 -->
    <select id="selectHotSongs" resultMap="ClientSongVOMap">
        SELECT
            s.id,
            s.artist_id,
            si.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count,
            s.play_count,
            s.create_time,
            s.status,
            0 as is_liked,
            0 as is_collected,
            (s.play_count * 0.7 + s.like_count * 0.3) as recommend_score
        FROM tb_song s
                 LEFT JOIN tb_singer si ON s.artist_id = si.singer_id
        WHERE s.deleted = 0 AND s.status = 'ACTIVE'
        ORDER BY s.play_count DESC, s.like_count DESC
    </select>

    <!-- 获取新歌推荐 -->
    <select id="selectNewSongs" resultMap="ClientSongVOMap">
        SELECT
            s.id,
            s.artist_id,
            si.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count,
            s.play_count,
            s.create_time,
            s.status,
            0 as is_liked,
            0 as is_collected,
            0 as recommend_score
        FROM tb_song s
                 LEFT JOIN tb_singer si ON s.artist_id = si.singer_id
        WHERE s.deleted = 0 AND s.status = 'ACTIVE'
          AND s.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ORDER BY s.create_time DESC, s.play_count DESC
    </select>

    <!-- 获取热门歌单 -->
    <select id="selectHotPlaylists" resultMap="ClientPlaylistVOMap">
        SELECT
            p.id,
            p.name,
            p.description,
            p.style,
            p.cover_url,
            p.song_count,
            p.play_count,
            p.like_count,
            p.user_id,
            u.username,
            p.is_public,
            p.create_time,
            p.update_time,
            p.status,
            0 as is_liked,
            0 as is_collected
        FROM tb_playlist p
                 LEFT JOIN tb_user u ON p.user_id = u.user_id
        WHERE p.deleted = 0 AND p.status = 'ACTIVE' AND p.is_public = 1
        ORDER BY p.play_count DESC, p.like_count DESC
    </select>

    <!-- 根据ID获取歌曲详情 -->
    <select id="selectSongDetailById" resultMap="ClientSongVOMap">
        SELECT
            s.id,
            s.artist_id,
            si.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count,
            s.play_count,
            s.create_time,
            s.status,
            CASE WHEN ul.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_collected,
            0 as recommend_score
        FROM tb_song s
                 LEFT JOIN tb_singer si ON s.artist_id = si.singer_id
                 LEFT JOIN tb_user_like ul ON ul.user_id = #{userId} AND ul.target_id = s.id AND ul.target_type = 'SONG'
                 LEFT JOIN tb_user_collection uc ON uc.user_id = #{userId} AND uc.target_id = s.id AND uc.target_type = 'SONG'
        WHERE s.id = #{songId} AND s.deleted = 0
    </select>

    <!-- 根据ID获取歌单详情 -->
    <select id="selectPlaylistDetailById" resultMap="ClientPlaylistVOMap">
        SELECT
            p.id,
            p.name,
            p.description,
            p.style,
            p.cover_url,
            p.song_count,
            p.play_count,
            p.like_count,
            p.user_id,
            u.username,
            p.is_public,
            p.create_time,
            p.update_time,
            p.status,
            CASE WHEN ul.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_collected
        FROM tb_playlist p
                 LEFT JOIN tb_user u ON p.user_id = u.user_id
                 LEFT JOIN tb_user_like ul ON ul.user_id = #{userId} AND ul.target_id = p.id AND ul.target_type = 'PLAYLIST'
                 LEFT JOIN tb_user_collection uc ON uc.user_id = #{userId} AND uc.target_id = p.id AND uc.target_type = 'PLAYLIST'
        WHERE p.id = #{playlistId} AND p.deleted = 0
    </select>

    <!-- 获取歌单中的歌曲 -->
    <select id="selectPlaylistSongs" resultMap="ClientSongVOMap">
        SELECT
            s.id,
            s.artist_id,
            si.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count,
            s.play_count,
            s.create_time,
            s.status,
            CASE WHEN ul.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_collected,
            0 as recommend_score
        FROM tb_playlist_song ps
                 JOIN tb_song s ON ps.song_id = s.id
                 LEFT JOIN tb_singer si ON s.artist_id = si.singer_id
                 LEFT JOIN tb_user_like ul ON ul.user_id = #{userId} AND ul.target_id = s.id AND ul.target_type = 'SONG'
                 LEFT JOIN tb_user_collection uc ON uc.user_id = #{userId} AND uc.target_id = s.id AND uc.target_type = 'SONG'
        WHERE ps.playlist_id = #{playlistId} AND s.deleted = 0 AND s.status = 'ACTIVE'
        ORDER BY ps.sort_order ASC, ps.create_time ASC
    </select>

    <!-- 搜索歌曲 -->
    <select id="searchSongs" resultMap="ClientSongVOMap">
        SELECT
            s.id,
            s.artist_id,
            si.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count,
            s.play_count,
            s.create_time,
            s.status,
            CASE WHEN ul.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_collected,
            0 as recommend_score
        FROM tb_song s
                 LEFT JOIN tb_singer si ON s.artist_id = si.singer_id
                 LEFT JOIN tb_user_like ul ON ul.user_id = #{userId} AND ul.target_id = s.id AND ul.target_type = 'SONG'
                 LEFT JOIN tb_user_collection uc ON uc.user_id = #{userId} AND uc.target_id = s.id AND uc.target_type = 'SONG'
        WHERE s.deleted = 0 AND s.status = 'ACTIVE'
          AND (
                    s.name LIKE CONCAT('%', #{keyword}, '%')
                OR si.singer_name LIKE CONCAT('%', #{keyword}, '%')
                OR s.album LIKE CONCAT('%', #{keyword}, '%')
                OR s.style LIKE CONCAT('%', #{keyword}, '%')
            )
        ORDER BY s.play_count DESC, s.like_count DESC
    </select>

    <!-- 搜索歌单 -->
    <select id="searchPlaylists" resultMap="ClientPlaylistVOMap">
        SELECT
            p.id,
            p.name,
            p.description,
            p.style,
            p.cover_url,
            p.song_count,
            p.play_count,
            p.like_count,
            p.user_id,
            u.username,
            p.is_public,
            p.create_time,
            p.update_time,
            p.status,
            CASE WHEN ul.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_collected
        FROM tb_playlist p
                 LEFT JOIN tb_user u ON p.user_id = u.user_id
                 LEFT JOIN tb_user_like ul ON ul.user_id = #{userId} AND ul.target_id = p.id AND ul.target_type = 'PLAYLIST'
                 LEFT JOIN tb_user_collection uc ON uc.user_id = #{userId} AND uc.target_id = p.id AND uc.target_type = 'PLAYLIST'
        WHERE p.deleted = 0 AND p.status = 'ACTIVE' AND p.is_public = 1
          AND (
                    p.name LIKE CONCAT('%', #{keyword}, '%')
                OR p.description LIKE CONCAT('%', #{keyword}, '%')
                OR p.style LIKE CONCAT('%', #{keyword}, '%')
            )
        ORDER BY p.play_count DESC, p.like_count DESC
    </select>

    <!-- 获取用户最近播放 -->
    <select id="selectRecentPlays" resultMap="ClientSongVOMap">
        SELECT
            s.id,
            s.artist_id,
            si.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count,
            s.play_count,
            s.create_time,
            s.status,
            CASE WHEN ul.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_collected,
            0 as recommend_score
        FROM tb_recent_play rp
                 JOIN tb_song s ON rp.song_id = s.id
                 LEFT JOIN tb_singer si ON s.artist_id = si.singer_id
                 LEFT JOIN tb_user_like ul ON ul.user_id = #{userId} AND ul.target_id = s.id AND ul.target_type = 'SONG'
                 LEFT JOIN tb_user_collection uc ON uc.user_id = #{userId} AND uc.target_id = s.id AND uc.target_type = 'SONG'
        WHERE rp.user_id = #{userId} AND s.deleted = 0 AND s.status = 'ACTIVE'
        ORDER BY rp.update_time DESC
    </select>

    <!-- 获取用户喜欢的歌曲 -->
    <select id="selectUserLikedSongs" resultMap="ClientSongVOMap">
        SELECT
            s.id,
            s.artist_id,
            si.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count,
            s.play_count,
            s.create_time,
            s.status,
            1 as is_liked,
            CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_collected,
            0 as recommend_score
        FROM tb_user_like ul
                 JOIN tb_song s ON ul.target_id = s.id
                 LEFT JOIN tb_singer si ON s.artist_id = si.singer_id
                 LEFT JOIN tb_user_collection uc ON uc.user_id = #{userId} AND uc.target_id = s.id AND uc.target_type = 'SONG'
        WHERE ul.user_id = #{userId} AND ul.target_type = 'SONG'
          AND s.deleted = 0 AND s.status = 'ACTIVE'
        ORDER BY ul.create_time DESC
    </select>

    <!-- 获取用户收藏的歌曲 -->
    <select id="selectUserCollectedSongs" resultMap="ClientSongVOMap">
        SELECT
            s.id,
            s.artist_id,
            si.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count,
            s.play_count,
            s.create_time,
            s.status,
            CASE WHEN ul.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            1 as is_collected,
            0 as recommend_score
        FROM tb_user_collection uc
                 JOIN tb_song s ON uc.target_id = s.id
                 LEFT JOIN tb_singer si ON s.artist_id = si.singer_id
                 LEFT JOIN tb_user_like ul ON ul.user_id = #{userId} AND ul.target_id = s.id AND ul.target_type = 'SONG'
        WHERE uc.user_id = #{userId} AND uc.target_type = 'SONG'
          AND s.deleted = 0 AND s.status = 'ACTIVE'
        ORDER BY uc.create_time DESC
    </select>

    <!-- 获取用户收藏的歌单 -->
    <select id="selectUserCollectedPlaylists" resultMap="ClientPlaylistVOMap">
        SELECT
            p.id,
            p.name,
            p.description,
            p.style,
            p.cover_url,
            p.song_count,
            p.play_count,
            p.like_count,
            p.user_id,
            u.username,
            p.is_public,
            p.create_time,
            p.update_time,
            p.status,
            CASE WHEN ul.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            1 as is_collected
        FROM tb_user_collection uc
                 JOIN tb_playlist p ON uc.target_id = p.id
                 LEFT JOIN tb_user u ON p.user_id = u.user_id
                 LEFT JOIN tb_user_like ul ON ul.user_id = #{userId} AND ul.target_id = p.id AND ul.target_type = 'PLAYLIST'
        WHERE uc.user_id = #{userId} AND uc.target_type = 'PLAYLIST'
          AND p.deleted = 0 AND p.status = 'ACTIVE'
        ORDER BY uc.create_time DESC
    </select>

    <!-- 根据风格获取歌曲 -->
    <select id="selectSongsByStyle" resultMap="ClientSongVOMap">
        SELECT
            s.id,
            s.artist_id,
            si.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count,
            s.play_count,
            s.create_time,
            s.status,
            CASE WHEN ul.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_collected,
            0 as recommend_score
        FROM tb_song s
                 LEFT JOIN tb_singer si ON s.artist_id = si.singer_id
                 LEFT JOIN tb_user_like ul ON ul.user_id = #{userId} AND ul.target_id = s.id AND ul.target_type = 'SONG'
                 LEFT JOIN tb_user_collection uc ON uc.user_id = #{userId} AND uc.target_id = s.id AND uc.target_type = 'SONG'
        WHERE s.deleted = 0 AND s.status = 'ACTIVE' AND s.style = #{style}
        ORDER BY s.play_count DESC, s.like_count DESC
    </select>

    <!-- 获取歌手的热门歌曲 -->
    <select id="selectSingerHotSongs" resultMap="ClientSongVOMap">
        SELECT
            s.id,
            s.artist_id,
            si.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count,
            s.play_count,
            s.create_time,
            s.status,
            CASE WHEN ul.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            CASE WHEN uc.id IS NOT NULL THEN 1 ELSE 0 END as is_collected,
            0 as recommend_score
        FROM tb_song s
                 LEFT JOIN tb_singer si ON s.artist_id = si.singer_id
                 LEFT JOIN tb_user_like ul ON ul.user_id = #{userId} AND ul.target_id = s.id AND ul.target_type = 'SONG'
                 LEFT JOIN tb_user_collection uc ON uc.user_id = #{userId} AND uc.target_id = s.id AND uc.target_type = 'SONG'
        WHERE s.artist_id = #{singerId} AND s.deleted = 0 AND s.status = 'ACTIVE'
        ORDER BY s.play_count DESC, s.like_count DESC
        LIMIT #{limit}
    </select>

    <!-- 检查用户是否喜欢歌曲 -->
    <select id="checkUserLikeSong" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM tb_user_like
        WHERE user_id = #{userId} AND target_id = #{songId} AND target_type = 'SONG'
    </select>

    <!-- 检查用户是否收藏歌曲 -->
    <select id="checkUserCollectSong" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM tb_user_collection
        WHERE user_id = #{userId} AND target_id = #{songId} AND target_type = 'SONG'
    </select>

    <!-- 检查用户是否喜欢歌单 -->
    <select id="checkUserLikePlaylist" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM tb_user_like
        WHERE user_id = #{userId} AND target_id = #{playlistId} AND target_type = 'PLAYLIST'
    </select>

    <!-- 检查用户是否收藏歌单 -->
    <select id="checkUserCollectPlaylist" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM tb_user_collection
        WHERE user_id = #{userId} AND target_id = #{playlistId} AND target_type = 'PLAYLIST'
    </select>

</mapper>
