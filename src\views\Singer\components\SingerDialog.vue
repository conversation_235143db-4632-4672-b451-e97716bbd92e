<template>
  <div>
    <el-dialog
      :title="dialogProps.title"
      :model-value="dialogProps.visible"
      @close="handleClose"
      width="650px"
      class="singer-dialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="right"
        class="px-4"
      >
        <el-form-item v-if="dialogProps.type === 'edit'" label="歌手编号">
          <el-input v-model="form.singerId" disabled class="w-full" />
        </el-form-item>

        <el-form-item label="歌手名称" prop="singerName">
          <el-input
            v-model="form.singerName"
            placeholder="请输入歌手名称"
            class="w-full"
            clearable
          />
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择类型" class="w-full" clearable>
            <el-option label="组合" value="组合" />
            <el-option label="男歌手" value="男歌手" />
            <el-option label="女歌手" value="女歌手" />
          </el-select>
        </el-form-item>

        <el-form-item label="出生日期" prop="birth">
          <el-date-picker
            v-model="form.birth"
            type="date"
            placeholder="请选择出生日期"
            value-format="YYYY-MM-DD"
            class="w-full"
          />
        </el-form-item>

        <el-form-item label="地区" prop="location">
          <el-select
            v-model="form.location"
            placeholder="请选择地区"
            class="w-full"
            clearable
            filterable
          >
            <el-option
              v-for="location in SingerLocations"
              :key="location"
              :label="location"
              :value="location"
            />
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="歌曲数">
              <el-input-number
                v-model="form.songsCount"
                :min="0"
                placeholder="歌曲数量"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="粉丝数">
              <el-input-number
                v-model="form.fansCount"
                :min="0"
                placeholder="粉丝数量"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="简介" prop="introduction">
          <el-input
            v-model="form.introduction"
            type="textarea"
            :rows="4"
            placeholder="请输入歌手简介"
            maxlength="500"
            show-word-limit
            class="w-full"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-end gap-3 pt-4 border-t border-gray-200">
          <el-button @click="handleClose" class="px-6">取消</el-button>
          <el-button type="primary" @click="handleSubmit" class="px-6">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SingerDialog',
})
import { SingerLocations } from '@/types/singer'
import type { SingerInfo } from '@/types/singer'
import type { FormInstance, FormRules } from 'element-plus'

const props = defineProps<{
  dialogProps: {
    title: string
    visible: boolean
    type: 'add' | 'edit'
    data?: Partial<SingerInfo>
  }
}>()
const emit = defineEmits(['update:visible', 'success'])
const formRef = ref<FormInstance>()

const form = reactive<Partial<SingerInfo>>({
  singerId: undefined,
  singerName: '',
  type: '',
  birth: '',
  location: '',
  introduction: '',
  songsCount: 0,
  fansCount: 0,
})

const rules = reactive<FormRules>({
  singerName: [
    { required: true, message: '请输入歌手名称', trigger: 'blur' },
    { min: 1, max: 50, message: '歌手名称长度在 1 到 50 个字符', trigger: 'blur' },
  ],
  type: [{ required: true, message: '请选择歌手类型', trigger: 'change' }],
  location: [{ required: true, message: '请选择地区', trigger: 'change' }],
  introduction: [{ max: 500, message: '简介最多500个字符', trigger: 'blur' }],
})

watch(
  () => props.dialogProps.visible,
  (val) => {
    if (val) {
      if (props.dialogProps.type === 'edit' && props.dialogProps.data) {
        Object.assign(form, props.dialogProps.data)
      } else {
        form.singerId = undefined
        form.singerName = ''
        form.type = ''
        form.birth = ''
        form.location = ''
        form.introduction = ''
        form.songsCount = 0
        form.fansCount = 0
      }
    }
  },
)

const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      emit('success', form, props.dialogProps.type)
      handleClose()
    }
  })
}
</script>

<style scoped>
.singer-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.singer-dialog :deep(.el-dialog__footer) {
  padding: 0 24px 20px;
}

.singer-dialog :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.singer-dialog :deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.2s ease;
}

.singer-dialog :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.singer-dialog :deep(.el-textarea__inner) {
  border-radius: 6px;
  transition: all 0.2s ease;
}

.singer-dialog :deep(.el-textarea__inner:hover) {
  border-color: #c0c4cc;
}

.singer-dialog :deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}
</style>
