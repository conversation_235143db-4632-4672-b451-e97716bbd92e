<template>
  <div class="sidebar-container">
    <div class="logo">
      <el-icon class="mr-3">
        <span class="iconfont icon-music"></span>
      </el-icon>
      <span v-show="!isCollapse">HappyMusic-Admin</span>
    </div>
    <el-menu class="sidebar-menu" :default-active="activeMenu" :collapse="isCollapse" router>
      <el-menu-item
        v-for="item in menuItems"
        :key="item.index"
        :index="item.path"
        :data-route="item.routeName"
      >
        <el-icon>
          <span :class="'iconfont ' + item.icon"></span>
        </el-icon>
        <span>{{ item.label }}</span>
        <!-- <template #title>{{ item.label }}</template> -->
      </el-menu-item>
    </el-menu>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SideBar',
})
const isCollapse = ref(false)
const activeMenu = ref('0')

const menuItems = ref([
  { index: '0', icon: 'icon-Home', label: '首页', path: '/', routeName: 'home' },
  { index: '1', icon: 'icon-user', label: '用户管理', path: '/user', routeName: 'user' },
  { index: '2', icon: 'icon-singer', label: '歌手管理', path: '/singer', routeName: 'singer' },
  { index: '3', icon: 'icon-song', label: '歌曲管理', path: '/song', routeName: 'song' },
  { index: '4', icon: 'icon-playlist', label: '歌单管理', path: '/playlist', routeName: 'playlist' },
  { index: '5', icon: 'icon-feedback', label: '反馈管理', path: '/feedback', routeName: 'feedback' },
])
</script>
<style scoped>
.sidebar-container {
  @apply h-full flex flex-col overflow-hidden;
}

.logo {
  @apply h-[60px] flex items-center justify-center  font-bold px-4 bg-[#ffffff];
}

.sidebar-menu {
  @apply flex-1;
}
:deep(.el-menu) {
  border-right: none;
}
</style>
