package com.jpzz.service.admin;


import com.baomidou.mybatisplus.extension.service.IService;
import com.jpzz.pojo.dto.admin.SingerDTO;
import com.jpzz.pojo.dto.admin.SingerQueryDTO;
import com.jpzz.pojo.entity.Singer;
import com.jpzz.pojo.vo.admin.SingerVO;
import com.jpzz.result.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SingerService extends IService<Singer> {

    /**
     * 分页查询
     * @param singerQueryDTO 分页参数
     * @return 分页数据
     */
    PageResult<SingerVO> pageSingerList(SingerQueryDTO singerQueryDTO);

    <PERSON><PERSON> getSingerById(Long singerId);

    Long addSinger(SingerDTO singerDTO);

    void updateSinger(SingerDTO singerDTO);

    void deleteById(Long singerId);

    void deleteByIds(List<Long> singerIds);

    String uploadAvatar(MultipartFile file,Long singerId);
}
