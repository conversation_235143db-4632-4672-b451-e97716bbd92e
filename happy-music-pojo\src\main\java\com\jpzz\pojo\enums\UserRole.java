package com.jpzz.pojo.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * <AUTHOR>
 *  用户角色枚举
 *  定义系统中的用户角色类型
 *
 */
//尚未用上
@Schema(description = "用户角色")
public enum UserRole {
    /**
     * 管理员角色
     * 拥有系统的全部权限
     */
    @Schema(description = "管理员")
    ADMIN("ADMIN","管理员"),

    /**
     * 普通用户角色
     * 拥有部分权限
     */
    @Schema(description = "普通用户")
    USER("USER","普通用户");

    @Getter
    private final String code;
    @Getter
    private final String desc;
    UserRole(String code, String desc){
        this.code = code;
        this.desc = desc;
    }

}
