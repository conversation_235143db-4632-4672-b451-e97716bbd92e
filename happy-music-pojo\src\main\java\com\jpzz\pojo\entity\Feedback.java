package com.jpzz.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 反馈实体类
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tb_feedback")
public class Feedback implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 反馈ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 反馈内容
     */
    @TableField("content")
    private String content;

    /**
     * 反馈类型
     */
    @TableField("type")
    private String type;

    /**
     * 反馈状态
     */
    @TableField("status")
    private String status;

    /**
     * 管理员回复
     */
    @TableField("admin_reply")
    private String adminReply;

    /**
     * 回复时间
     */
    @TableField("reply_time")
    private LocalDateTime replyTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableLogic
    private Integer deleted;


}