package com.jpzz.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 播放记录实体
 * <AUTHOR>
 */
@Data
@TableName("tb_play_record")
public class PlayRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 歌曲ID
     */
    private Long songId;

    /**
     * 播放时长(秒)
     */
    private Integer playDuration;

    /**
     * 播放进度(百分比)
     */
    private BigDecimal playProgress;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
