import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import type { FeedbackInfo, FeedbackQueryParams } from '@/types/feedback'
import {
  getFeedbackList,
  getFeedbackInfo,
  replyFeedback,
  updateFeedbackStatus,
  deleteFeedback,
  batchDeleteFeedback
} from '@/api/feedback/index'
import { ElMessage } from 'element-plus'

export const useFeedbackStore = defineStore('feedback', () => {
  // 状态定义
  const feedbackList = ref<FeedbackInfo[]>([])
  const currentFeedback = ref<FeedbackInfo | null>(null)
  const total = ref(0)
  const loading = ref(false)

  // 查询参数
  const queryParams = reactive<FeedbackQueryParams>({
    content: '',
    type: '',
    status: '',
    userId: undefined,
    pageNum: 1,
    pageSize: 10
  })

  // 获取反馈列表
  const getList = async () => {
    try {
      loading.value = true
      const res = await getFeedbackList(queryParams)
      feedbackList.value = res.list
      total.value = res.total
    } catch (error) {
      console.error('获取反馈列表失败', error)
      ElMessage.error('获取反馈列表失败')
    } finally {
      loading.value = false
    }
  }

  // 获取反馈详情
  const getDetail = async (feedbackId: number) => {
    try {
      const res = await getFeedbackInfo(feedbackId)
      currentFeedback.value = res
      return res
    } catch (error) {
      console.error('获取反馈详情失败', error)
      ElMessage.error('获取反馈详情失败')
      throw error
    }
  }

  // 回复反馈
  const replyFeedbackAction = async (feedbackId: number, reply: string) => {
    try {
      await replyFeedback(feedbackId, reply)
      ElMessage.success('回复成功')
      getList()
    } catch (error) {
      console.error('回复反馈失败', error)
      ElMessage.error('回复反馈失败')
      throw error
    }
  }

  // 更新反馈状态
  const updateStatus = async (feedbackId: number, status: string) => {
    try {
      await updateFeedbackStatus(feedbackId, status)
      ElMessage.success('状态更新成功')
      getList()
    } catch (error) {
      console.error('更新状态失败', error)
      ElMessage.error('更新状态失败')
      throw error
    }
  }

  // 删除反馈
  const deleteFeedbackAction = async (feedbackId: number) => {
    try {
      await deleteFeedback(feedbackId)
      ElMessage.success('删除成功')
      getList()
    } catch (error) {
      console.error('删除反馈失败', error)
      ElMessage.error('删除反馈失败')
      throw error
    }
  }

  // 批量删除反馈
  const batchDelete = async (feedbackIds: number[]) => {
    try {
      await batchDeleteFeedback(feedbackIds)
      ElMessage.success('批量删除成功')
      getList()
    } catch (error) {
      console.error('批量删除失败', error)
      ElMessage.error('批量删除失败')
      throw error
    }
  }

  // 重置查询条件
  const resetQuery = () => {
    queryParams.content = ''
    queryParams.type = ''
    queryParams.status = ''
    queryParams.userId = undefined
    queryParams.pageNum = 1
    getList()
  }

  return {
    // 状态
    feedbackList,
    currentFeedback,
    total,
    loading,
    queryParams,
    // 方法
    getList,
    getDetail,
    replyFeedbackAction,
    updateStatus,
    deleteFeedbackAction,
    batchDelete,
    resetQuery
  }
})
