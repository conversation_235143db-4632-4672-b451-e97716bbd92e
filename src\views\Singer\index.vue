<template>
  <div class="singer-container min-h-screen relative overflow-hidden">
    <!-- 背景装饰 -->
    <div
      class="absolute inset-0 bg-gradient-to-br from-pink-50 via-white to-purple-50 pointer-events-none"
    ></div>
    <div
      class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-r from-pink-400/10 to-purple-400/10 rounded-full blur-3xl pointer-events-none"
    ></div>

    <div class="relative z-10 p-6">
      <!-- 页面标题 -->
      <div class="mb-4">
        <div class="flex items-center space-x-3 mb-1">
          <div
            class="w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-500 rounded-lg flex items-center justify-center"
          >
            <Icon icon="mdi:account-music" class="text-white text-lg" />
          </div>
          <h1
            class="text-xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent"
          >
            歌手管理
          </h1>
        </div>
        <p class="text-sm text-gray-600">管理音乐歌手信息，包括歌手资料、头像和基本信息</p>
      </div>

      <!-- 搜索区域 -->
      <el-card class="search-card enhanced-glass-card mb-4" shadow="never">
        <template #header>
          <div class="flex items-center space-x-3 py-1">
            <div
              class="w-6 h-6 bg-gradient-to-r from-pink-500 to-purple-500 rounded-lg flex items-center justify-center"
            >
              <Icon icon="mdi:magnify" class="text-white text-sm" />
            </div>
            <div>
              <h3 class="text-base font-semibold text-gray-800">搜索筛选</h3>
              <p class="text-xs text-gray-500">快速查找歌手信息</p>
            </div>
          </div>
        </template>

        <el-form
          :model="singerStore.queryParams"
          @submit.prevent
          class="enhanced-search-form"
          label-width="70px"
          label-position="left"
        >
          <el-row :gutter="16" class="items-end">
            <el-col :span="6">
              <el-form-item label="歌手名称：" class="enhanced-form-item" label-width="75">
                <el-input
                  v-model="singerStore.queryParams.singerName"
                  placeholder="请输入歌手名称"
                  clearable
                  @keyup.enter="handleSearch"
                  class="enhanced-input"
                  size="default"
                />
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="类型：" class="enhanced-form-item" label-width="50">
                <el-select
                  v-model="singerStore.queryParams.type"
                  placeholder="请选择类型"
                  clearable
                  class="enhanced-select w-full"
                  size="default"
                >
                  <el-option v-for="type in SingerType" :key="type" :label="type" :value="type" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="4">
              <el-form-item label="地区：" class="enhanced-form-item" label-width="50">
                <el-select
                  v-model="singerStore.queryParams.location"
                  placeholder="请选择地区"
                  clearable
                  class="enhanced-select w-full"
                  size="default"
                >
                  <el-option
                    v-for="location in SingerLocations"
                    :key="location"
                    :label="location"
                    :value="location"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="10">
              <el-form-item class="enhanced-form-item">
                <div class="flex items-center space-x-3">
                  <el-button
                    type="primary"
                    @click="handleSearch"
                    class="enhanced-btn enhanced-btn-primary"
                  >
                    <Icon icon="mdi:magnify" class="mr-2" />
                    搜索
                  </el-button>
                  <el-button @click="handleReset" class="enhanced-btn enhanced-btn-reset">
                    <Icon icon="mdi:refresh" class="mr-2" />
                    重置
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 操作按钮区域 -->
      <el-card class="table-card enhanced-glass-card" shadow="never">
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div
                class="w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-500 rounded-lg flex items-center justify-center"
              >
                <Icon icon="mdi:table-edit" class="text-white text-lg" />
              </div>
              <div>
                <h3 class="text-lg font-semibold text-gray-800">歌手列表</h3>
                <p class="text-sm text-gray-500">共 {{ singerStore.total }} 条记录</p>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <el-button
                type="primary"
                @click="handleAdd"
                v-if="hasPermission('user:add')"
                class="enhanced-btn-primary"
              >
                <Icon icon="mdi:account-plus" class="mr-2" />
                新增歌手
              </el-button>

              <el-button
                type="danger"
                :disabled="selectedSingers.length === 0"
                @click="handleBatchDelete"
                v-if="hasPermission('user:delete')"
                class="enhanced-btn"
              >
                <el-icon><Delete /></el-icon>
                批量删除 ({{ selectedSingers.length }})
              </el-button>

              <el-button type="success" @click="handleRefresh" class="enhanced-btn">
                <Icon icon="mdi:refresh" class="mr-2" />
                刷新
              </el-button>

              <el-tooltip content="列设置" placement="top">
                <el-button type="info" @click="showColumnSetting = true" class="enhanced-btn">
                  <Icon icon="mdi:cog" class="mr-2" />
                  列设置
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </template>

        <!-- 表格区域 -->
        <el-table
          v-loading="singerStore.loading"
          :data="singerStore.singerList"
          @selection-change="handleSelectionChange"
          highlight-current-row
          row-key="singerId"
          class="enhanced-table"
          stripe
        >
          <el-table-column type="selection" width="50" align="center" fixed="left" />
          <el-table-column type="index" label="序号" width="60" align="center" fixed="left" />
          <el-table-column
            label="歌手编号"
            prop="singerId"
            align="center"
            width="85"
            v-if="columns.singerId.visible"
          />
          <el-table-column
            label="歌手头像"
            align="center"
            width="100"
            v-if="columns.avatar.visible"
          >
            <template #default="{ row }">
              <div class="flex justify-center">
                <el-avatar
                  :size="40"
                  :src="row.avatar || defaultAvatar"
                  class="shadow-md hover:shadow-lg transition-shadow cursor-pointer"
                  @click="handlePreviewAvatar(row.avatar)"
                >
                  <Icon icon="mdi:account-music" />
                </el-avatar>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="歌手名称"
            prop="singerName"
            align="center"
            min-width="120"
            show-overflow-tooltip
            v-if="columns.singerName.visible"
          />
          <el-table-column label="类型" align="center" width="100" v-if="columns.type.visible">
            <template #default="{ row }">
              <el-tag
                :type="
                  row.type === '男歌手' ? 'primary' : row.type === '女歌手' ? 'danger' : 'warning'
                "
                effect="light"
                class="enhanced-tag"
              >
                <Icon
                  :icon="
                    row.type === '男歌手'
                      ? 'mdi:account'
                      : row.type === '女歌手'
                        ? 'mdi:account-outline'
                        : 'mdi:account-group'
                  "
                  class="mr-1"
                />
                {{ row.type }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="出生日期"
            prop="birth"
            align="center"
            width="120"
            v-if="columns.birth.visible"
          />
          <el-table-column
            label="地区"
            prop="location"
            align="center"
            width="100"
            v-if="columns.location.visible"
          />
          <el-table-column
            label="歌曲数"
            align="center"
            width="80"
            v-if="columns.songsCount.visible"
          >
            <template #default="{ row }">
              <span>{{ row.songsCount || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="粉丝数"
            align="center"
            width="100"
            v-if="columns.fansCount.visible"
          >
            <template #default="{ row }">
              <span>{{ formatNumber(row.fansCount || 0) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="简介"
            prop="introduction"
            align="center"
            min-width="200"
            show-overflow-tooltip
            v-if="columns.introduction.visible"
          />
          <el-table-column
            label="创建时间"
            prop="createTime"
            align="center"
            min-width="160"
            sortable
            v-if="columns.createTime.visible"
          />
          <el-table-column
            label="修改时间"
            prop="updateTime"
            align="center"
            min-width="160"
            sortable
            v-if="columns.updateTime.visible"
          />
          <el-table-column label="操作" align="center" width="200" fixed="right">
            <template #default="{ row }">
              <div class="flex items-center justify-center space-x-2">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleEdit(row)"
                  class="enhanced-btn-small enhanced-btn-primary"
                >
                  <Icon icon="mdi:pencil" class="mr-1" />
                  编辑
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="handleUploadAvatar(row)"
                  class="enhanced-btn-small"
                >
                  <Icon icon="mdi:upload" class="mr-1" />
                  头像
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
                  class="enhanced-btn-small"
                >
                  <Icon icon="mdi:delete" class="mr-1" />
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页区域 -->
        <div class="flex justify-center mt-6">
          <el-pagination
            v-model:current-page="singerStore.queryParams.pageNum"
            v-model:page-size="singerStore.queryParams.pageSize"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="singerStore.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
            class="enhanced-pagination"
          />
        </div>
      </el-card>
    </div>

    <!-- 头像预览对话框 -->
    <el-dialog v-model="previewAvatarVisible" title="头像预览" width="60%" center>
      <div class="text-center">
        <el-image :src="previewAvatarUrl" fit="contain" style="max-width: 100%; max-height: 70vh" />
      </div>
    </el-dialog>

    <!-- 添加/编辑歌手对话框 -->
    <SingerDialog
      :dialog-props="dialogProps"
      @update:visible="dialogProps.visible = $event"
      @success="handleDialogSuccess"
    />

    <!-- 头像上传对话框 -->
    <AvatarUploadDialog
      v-model="avatarDialogVisible"
      :singer-id="currentSingerId"
      @success="handleAvatarSuccess"
    />
  </div>

  <el-drawer v-model="showColumnSetting" title="列设置" direction="rtl" size="300px">
    <el-divider>表格显示列</el-divider>
    <el-checkbox-group v-model="checkedColumns">
      <div v-for="(col, key) in columns" :key="key" class="column-item">
        <el-checkbox :label="key">{{ col.label }}</el-checkbox>
      </div>
    </el-checkbox-group>
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="resetColumns">重置</el-button>
        <el-button type="primary" @click="saveColumns">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>
<!-- 列设置抽屉 -->
<!-- 列设置抽屉 -->

<script setup lang="ts">
defineOptions({
  name: 'SingerView',
})
import { ref, reactive, onMounted } from 'vue'
import { useSingerStore } from '@/stores/singer'
import { SingerLocations, SingerType } from '@/types/singer'
import type { SingerInfo } from '@/types/singer'
import { ElMessageBox, ElMessage } from 'element-plus'
import { Delete } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import usePermission from '@/hooks/usePermission'

// 使用权限hook
const { hasPermission } = usePermission()

// 默认头像
const defaultAvatar = ref('/favicon.ico')

// Store
const singerStore = useSingerStore()

// 头像预览
const previewAvatarVisible = ref(false)
const previewAvatarUrl = ref('')

// 弹窗属性
interface DialogProps {
  visible: boolean
  title: string
  type: 'add' | 'edit'
  data?: Partial<SingerInfo>
}

const dialogProps = reactive<DialogProps>({
  visible: false,
  title: '',
  type: 'add',
  data: undefined,
})

const selectedSingers = ref<SingerInfo[]>([])
const avatarDialogVisible = ref(false)
const currentSingerId = ref<number>(0)

// 列设置
const showColumnSetting = ref(false)

interface ColumnItem {
  visible: boolean
  label: string
}

type ColumnsType = {
  [key: string]: ColumnItem
}

const columns = reactive<ColumnsType>({
  singerId: { visible: true, label: '歌手编号' },
  avatar: { visible: true, label: '歌手头像' },
  singerName: { visible: true, label: '歌手名称' },
  type: { visible: true, label: '类型' },
  birth: { visible: true, label: '出生日期' },
  location: { visible: true, label: '地区' },
  songsCount: { visible: true, label: '歌曲数' },
  fansCount: { visible: true, label: '粉丝数' },
  introduction: { visible: true, label: '简介' },
  createTime: { visible: true, label: '创建时间' },
  updateTime: { visible: true, label: '修改时间' },
})

// 选中的列
const checkedColumns = ref(Object.keys(columns).filter((key) => columns[key].visible))

// 重置列设置
const resetColumns = () => {
  Object.keys(columns).forEach((key) => {
    columns[key].visible = true
  })
  checkedColumns.value = Object.keys(columns)
}

// 保存列设置
const saveColumns = () => {
  // 更新列可见性
  Object.keys(columns).forEach((key) => {
    columns[key].visible = checkedColumns.value.includes(key)
  })

  // 保存到本地存储
  localStorage.setItem('singerTableColumns', JSON.stringify(columns))

  showColumnSetting.value = false
  ElMessage.success('列设置保存成功')
}
// 搜索
const handleSearch = () => {
  singerStore.queryParams.pageNum = 1
  singerStore.getList()
}

// 重置
const handleReset = () => {
  singerStore.resetQuery()
}

// 刷新
const handleRefresh = () => {
  singerStore.getList()
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  singerStore.queryParams.pageSize = val
  singerStore.getList()
}

// 页码变化
const handleCurrentChange = (val: number) => {
  singerStore.queryParams.pageNum = val
  singerStore.getList()
}

// 添加歌手
const handleAdd = () => {
  dialogProps.visible = true
  dialogProps.title = '新增歌手'
  dialogProps.type = 'add'
  dialogProps.data = undefined
}

// 编辑歌手
const handleEdit = (singer: SingerInfo) => {
  dialogProps.visible = true
  dialogProps.title = '编辑歌手'
  dialogProps.type = 'edit'
  dialogProps.data = { ...singer }
}

// 预览头像
const handlePreviewAvatar = (avatarUrl: string) => {
  if (avatarUrl && avatarUrl !== defaultAvatar.value) {
    previewAvatarUrl.value = avatarUrl
    previewAvatarVisible.value = true
  }
}

// 删除歌手
const handleDelete = async (singer: SingerInfo) => {
  try {
    await ElMessageBox.confirm(`确认删除歌手【${singer.singerName}】吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    await singerStore.deleteSinger(singer.singerId)
  } catch (error) {
    // 用户取消删除
    console.log(error)
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedSingers.value.length === 0) {
    ElMessage.warning('请至少选择一个歌手')
    return
  }

  const singerNames = selectedSingers.value.map((item) => item.singerName).join('、')
  const singerIds = selectedSingers.value.map((item) => item.singerId)

  try {
    await ElMessageBox.confirm(`确认删除以下歌手吗？<br/>${singerNames}`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true,
    })

    await singerStore.deleteBatchSingers(singerIds)
    selectedSingers.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败', error)
    }
  }
}

// 上传头像
const handleUploadAvatar = (singer: SingerInfo) => {
  currentSingerId.value = singer.singerId
  avatarDialogVisible.value = true
}

// 选择变化
const handleSelectionChange = (selection: SingerInfo[]) => {
  selectedSingers.value = selection
}

// 对话框成功回调
const handleDialogSuccess = async (formData: Partial<SingerInfo>, type: 'add' | 'edit') => {
  try {
    if (type === 'add') {
      await singerStore.addSinger(formData)
    } else {
      await singerStore.updateSinger(formData)
    }
    dialogProps.visible = false
    singerStore.getList()
  } catch (error) {
    console.error('操作失败:', error)
  }
}

// 头像上传成功回调
const handleAvatarSuccess = () => {
  avatarDialogVisible.value = false
  singerStore.getList()
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toString()
}

// 初始化
onMounted(async () => {
  // 加载歌手列表
  console.log('🔄 加载歌手列表')
  await singerStore.getList()

  // 加载本地存储的列配置
  const savedColumns = localStorage.getItem('singerTableColumns')
  if (savedColumns) {
    try {
      const parsedColumns = JSON.parse(savedColumns)
      Object.keys(parsedColumns).forEach((key) => {
        if (columns[key]) {
          columns[key].visible = parsedColumns[key].visible
        }
      })
      checkedColumns.value = Object.keys(columns).filter((key) => columns[key].visible)
    } catch (e) {
      console.error('加载列配置失败', e)
    }
  }
})
</script>

<style scoped>
/* 🎵 歌手管理页面增强样式 */

/* 搜索表单样式 */
.enhanced-search-form {
  background: transparent;
}

.search-card {
  border-radius: 12px;
}

.search-card :deep(.el-card__header) {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.search-card :deep(.el-card__body) {
  padding: 16px 20px;
}


.enhanced-form-item :deep(.el-form-item__label) {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0 !important;
  line-height: 32px; /* 与输入框高度对齐 */
  display: flex;
  align-items: center;
  font-size: 14px;
  height: 32px; /* 固定标签高度 */
  padding-right: 4px; /* 减少右侧间距 */
}

.enhanced-form-item {
  margin-bottom: 16px !important;
  align-items: center;
  display: flex;
}

.enhanced-form-item :deep(.el-form-item__content) {
  line-height: 32px;
  min-height: 32px; /* 确保内容区域最小高度 */
  display: flex;
  align-items: center;
  flex: 1;
}

/* 确保输入框和选择框的高度一致 */
.enhanced-input :deep(.el-input__inner),
.enhanced-select :deep(.el-input__inner) {
  height: 32px !important;
  line-height: 32px !important;
}

/* 日期选择器对齐 */
.enhanced-date-picker :deep(.el-input__inner) {
  height: 32px !important;
  line-height: 32px !important;
}

/* 选择框下拉箭头对齐 */
.enhanced-select :deep(.el-input__suffix) {
  height: 32px !important;
  display: flex;
  align-items: center;
}

/* 选择框选项样式优化 */
.enhanced-select :deep(.el-select-dropdown__item) {
  padding: 8px 12px;
  min-height: 36px;
  display: flex;
  align-items: center;
}

/* 确保选择框有足够的宽度显示选项文字 */
.enhanced-select {
  min-width: 120px;
}

.enhanced-input :deep(.el-input__wrapper) {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.enhanced-input :deep(.el-input__wrapper):hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.enhanced-input :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.enhanced-select :deep(.el-select__wrapper) {
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.enhanced-select :deep(.el-select__wrapper):hover {
  border-color: #667eea;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}
/* 玻璃态卡片样式 */
.enhanced-glass-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.enhanced-glass-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.95);
}

/* 按钮样式 */
.enhanced-btn {
  border-radius: 0.75rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.enhanced-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.enhanced-btn-primary {
  background: linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%);
  color: white !important;
}

.enhanced-btn-reset {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white !important;
}

.enhanced-btn-small {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 4px 8px;
  font-size: 12px;
}

/* 表格样式 */
.enhanced-table {
  border-radius: 12px;
  overflow: hidden;
}

.enhanced-table :deep(.el-table__header) {
  background: linear-gradient(135deg, #fdf2f8 0%, #fae8ff 100%);
}

.enhanced-table :deep(.el-table__row:hover) {
  background-color: rgba(236, 72, 153, 0.05) !important;
}

/* 标签样式 */
.enhanced-tag {
  border-radius: 8px;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 分页样式 */
.enhanced-pagination :deep(.el-pager li) {
  border-radius: 8px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.enhanced-pagination :deep(.el-pager li:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(236, 72, 153, 0.2);
}

.enhanced-pagination :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%);
  color: white;
  font-weight: 600;
}

/* 列设置样式 */
.column-item {
  margin: 8px 0;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
