package com.jpzz.service.admin;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jpzz.pojo.dto.admin.SongDTO;
import com.jpzz.pojo.dto.admin.SongQueryDTO;
import com.jpzz.pojo.entity.Song;
import com.jpzz.pojo.vo.admin.SongVO;
import com.jpzz.result.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 歌曲服务接口
 * <AUTHOR>
 */
public interface SongService extends IService<Song> {

    /**
     * 分页查询歌曲列表
     * @param songQueryDTO 查询参数
     * @return 分页结果
     */
    PageResult<SongVO> pageSongList(SongQueryDTO songQueryDTO);

    /**
     * 根据ID查询歌曲详情
     * @param songId 歌曲ID
     * @return 歌曲详情
     */
    SongVO getSongById(Long songId);

    /**
     * 添加歌曲
     * @param songDTO 歌曲信息
     * @return 歌曲ID
     */
    Long addSong(SongDTO songDTO);

    /**
     * 更新歌曲
     * @param songDTO 歌曲信息
     */
    void updateSong(SongDTO songDTO);

    /**
     * 删除歌曲
     * @param songId 歌曲ID
     */
    void deleteById(Long songId);

    /**
     * 批量删除歌曲
     * @param songIds 歌曲ID列表
     */
    void deleteByIds(List<Long> songIds);

    /**
     * 上传歌曲封面
     * @param file 封面文件
     * @param songId 歌曲ID
     * @return 封面URL
     */
    String uploadCover(MultipartFile file, Long songId);

    /**
     * 上传歌曲音频
     * @param file 音频文件
     * @param songId 歌曲ID
     * @return 音频URL
     */
    String uploadAudio(MultipartFile file, Long songId);

    long countByArtistId(Long singerId);

    void softDeleteByArtistId(Long singerId);
}