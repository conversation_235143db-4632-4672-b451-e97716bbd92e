package com.jpzz.controller.common;

import com.jpzz.pojo.dto.admin.LoginDTO;
import com.jpzz.pojo.vo.admin.UserLoginVO;
import com.jpzz.result.Result;
import com.jpzz.service.common.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@Tag(name = "用户认证", description = "登录、注销相关接口")
@Slf4j
@RequestMapping("/api/auth")
public class AuthController {

    @Resource
    private AuthService authService;

    /**
     * 用户登录
     * @param loginDTO 登录参数
     * @return 登录结果
     */
    @Operation(summary = "用户登录", description = "用户登录并返回令牌")
    @PostMapping("/login")
    public Result<UserLoginVO> login(@Valid @RequestBody LoginDTO loginDTO) {
        log.info("用户登录：{}", loginDTO);
        UserLoginVO userLoginVO = authService.login(loginDTO);
        return Result.success("登陆成功!",userLoginVO);
    }

    @Operation(summary = "用户注销", description = "用户注销并清楚缓存")
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        String username = (String) request.getAttribute("username");
        Long userId = (Long) request.getAttribute("userId");
        log.info("用户注销Id:{} ,用户名:{}", userId,username);
        authService.logout(userId);
        return Result.success("注销成功!");
    }
    @Operation(summary = "获取当前用户信息", description = "获取当前用户信息")
    @GetMapping("/info")
    public Result<UserLoginVO> getUserInfo(HttpServletRequest request) {
        String username = (String) request.getAttribute("username");
        Long userId = (Long) request.getAttribute("userId");
        log.info("获取当前用户信息Id:{} ,用户名:{}", userId,username);
        UserLoginVO userLoginVO = authService.getUserInfo(userId);
        return Result.success("获取成功!",userLoginVO);
    }


}
