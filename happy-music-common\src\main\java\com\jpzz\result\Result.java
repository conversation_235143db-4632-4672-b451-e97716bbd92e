package com.jpzz.result;

import com.jpzz.constant.ResultCodeConstant;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class Result<T> implements Serializable {
    private Integer code;
    private String message;
    private T data;

    public static <T> Result<T> success() {
        Result<T> result = new Result<>();
        result.setCode(ResultCodeConstant.SUCCESS);
        return result;
    }

    public static <T> Result<T> success(T data) {
        return success("成功!", data);
    }

    public static <T> Result<T> success(String message) {
        Result<T> result = new Result<>();
        result.setCode(ResultCodeConstant.SUCCESS);
        result.setMessage(message);
        return result;
    }

    public static <T> Result<T> success(String message, T data) {
        Result<T> result = new Result<>();
        result.setCode(ResultCodeConstant.SUCCESS);
        result.setData(data);
        result.setMessage(message);
        return result;
    }

    public static <T> Result<T> error() {
        return error(ResultCodeConstant.ERROR, "操作失败!");
    }

    public static <T> Result<T> error(String message) {
        return error(ResultCodeConstant.ERROR, message);
    }

    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }


}
