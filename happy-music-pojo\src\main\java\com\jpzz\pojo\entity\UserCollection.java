package com.jpzz.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户收藏实体
 * <AUTHOR>
 */
@Data
@TableName("tb_user_collection")
public class UserCollection {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 目标ID(歌曲/歌单)
     */
    private Long targetId;

    /**
     * 目标类型(SONG/PLAYLIST)
     */
    private String targetType;

    /**
     * 收藏夹名称
     */
    private String folderName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
