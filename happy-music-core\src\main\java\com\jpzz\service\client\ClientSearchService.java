package com.jpzz.service.client;

import com.jpzz.pojo.vo.client.ClientSongVO;
import com.jpzz.pojo.vo.client.ClientPlaylistVO;
import com.jpzz.pojo.vo.client.ClientSingerVO;
import com.jpzz.result.PageResult;

import java.util.List;
import java.util.Map;

/**
 * 客户端搜索服务接口
 * <AUTHOR>
 */
public interface ClientSearchService {

    /**
     * 综合搜索
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @return 综合搜索结果
     */
    Map<String, Object> searchAll(String keyword, Long userId);

    /**
     * 搜索歌曲
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    PageResult<ClientSongVO> searchSongs(String keyword, Long userId, Integer page, Integer size);

    /**
     * 搜索歌单
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    PageResult<ClientPlaylistVO> searchPlaylists(String keyword, Long userId, Integer page, Integer size);

    /**
     * 搜索歌手
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    PageResult<ClientSingerVO> searchSingers(String keyword, Long userId, Integer page, Integer size);

    /**
     * 获取热门搜索关键词
     * @param limit 数量限制
     * @return 热门搜索关键词列表
     */
    List<String> getHotSearchKeywords(Integer limit);

    /**
     * 获取用户搜索历史
     * @param userId 用户ID
     * @param limit 数量限制
     * @return 搜索历史列表
     */
    List<String> getUserSearchHistory(Long userId, Integer limit);

    /**
     * 记录搜索历史
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @param resultCount 搜索结果数量
     * @param regionCode 地区编码
     */
    void recordSearchHistory(Long userId, String keyword, Integer resultCount, String regionCode);

    /**
     * 清除用户搜索历史
     * @param userId 用户ID
     */
    void clearUserSearchHistory(Long userId);

    /**
     * 获取搜索建议
     * @param keyword 关键词前缀
     * @param limit 数量限制
     * @return 搜索建议列表
     */
    List<String> getSearchSuggestions(String keyword, Integer limit);
}
