<template>
  <div class="dashboard-container relative min-h-screen overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-purple-50"></div>
    <div class="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
      <!-- 浮动音符装饰 -->
      <div class="floating-notes">
        <div class="note note-1">♪</div>
        <div class="note note-2">♫</div>
        <div class="note note-3">♪</div>
        <div class="note note-4">♬</div>
        <div class="note note-5">♩</div>
        <div class="note note-6">♪</div>
      </div>
      <!-- 渐变光晕 -->
      <div
        class="absolute top-20 right-20 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-10 blur-3xl"
      ></div>
      <div
        class="absolute bottom-20 left-20 w-80 h-80 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full opacity-10 blur-3xl"
      ></div>
    </div>

    <div class="relative z-10 p-6">
      <!-- 欢迎横幅 -->
      <div
        v-motion
        :initial="{ opacity: 0, y: -50 }"
        :enter="{ opacity: 1, y: 0, transition: { duration: 800 } }"
        class="welcome-banner mb-8 p-8 glass-card music-ripple relative overflow-hidden"
      >
        <!-- 背景渐变 -->
        <div
          class="absolute inset-0 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 opacity-90"
        ></div>
        <div
          class="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-indigo-600/20"
        ></div>

        <!-- 装饰元素 -->
        <div class="absolute top-4 right-4 text-white/20 text-6xl animate-pulse">♪</div>
        <div class="absolute bottom-4 left-4 text-white/20 text-4xl animate-bounce">♫</div>

        <div class="relative z-10 flex items-center justify-between text-white">
          <div class="flex-1">
            <div class="flex items-center mb-4">
              <div class="w-2 h-2 bg-white rounded-full mr-3 animate-pulse"></div>
              <h1 class="text-4xl font-bold gradient-text-white">🎵 欢迎回来，管理员！</h1>
            </div>
            <p class="text-white/90 text-xl font-medium mb-2">今天是 {{ currentDate }}</p>
            <p class="text-white/80 text-lg">
              让我们一起管理好音乐平台，为用户带来美妙的音乐体验 🎶
            </p>

            <!-- 快速统计 -->
            <div class="flex items-center space-x-6 mt-6">
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span class="text-white/90 text-sm">系统运行正常</span>
              </div>
              <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                <span class="text-white/90 text-sm"
                  >{{ overview?.userStats.totalUsers || 0 }} 位用户在线</span
                >
              </div>
            </div>
          </div>

          <div class="hidden lg:block">
            <div class="relative">
              <!-- 主图标 -->
              <div
                class="w-32 h-32 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30 float-animation"
              >
                <Icon icon="mdi:headphones" class="text-6xl text-white" />
              </div>
              <!-- 环绕装饰 -->
              <div
                class="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center animate-bounce"
              >
                <Icon icon="mdi:music-note" class="text-white text-lg" />
              </div>
              <div
                class="absolute -bottom-2 -left-2 w-6 h-6 bg-pink-400 rounded-full flex items-center justify-center animate-pulse"
              >
                <Icon icon="mdi:heart" class="text-white text-sm" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心指标卡片 -->
      <div
        v-motion
        :initial="{ opacity: 0, y: 50 }"
        :enter="{ opacity: 1, y: 0, transition: { duration: 800, delay: 200 } }"
        class="metrics-grid mb-8"
      >
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <!-- 用户统计 -->
          <div class="metric-card-wrapper">
            <MetricCard
              title="总用户数"
              :value="overview?.userStats.totalUsers || 0"
              icon="mdi:account-group"
              color="blue"
              :trend="{ type: 'up', value: 12.5, period: '较上月' }"
              :delay="0"
            />
            <div class="metric-decoration metric-decoration-1"></div>
          </div>

          <div class="metric-card-wrapper">
            <MetricCard
              title="歌手总数"
              :value="overview?.contentStats.totalSingers || 0"
              icon="mdi:microphone"
              color="green"
              :trend="{ type: 'up', value: 8.2, period: '较上月' }"
              :delay="100"
            />
            <div class="metric-decoration metric-decoration-2"></div>
          </div>

          <div class="metric-card-wrapper">
            <MetricCard
              title="歌曲总数"
              :value="overview?.contentStats.totalSongs || 0"
              icon="mdi:music-note"
              color="orange"
              :trend="{ type: 'up', value: 15.3, period: '较上月' }"
              :delay="200"
            />
            <div class="metric-decoration metric-decoration-3"></div>
          </div>

          <div class="metric-card-wrapper">
            <MetricCard
              title="反馈总数"
              :value="overview?.systemStats.totalFeedbacks || 0"
              icon="mdi:message-text"
              color="purple"
              :trend="{ type: 'down', value: 3.1, period: '较上月' }"
              :delay="300"
            />
            <div class="metric-decoration metric-decoration-4"></div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div
        v-motion
        :initial="{ opacity: 0, y: 50 }"
        :enter="{ opacity: 1, y: 0, transition: { duration: 800, delay: 400 } }"
        class="charts-section mb-8"
      >
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 用户增长趋势 -->
          <div class="chart-wrapper">
            <el-card class="chart-card enhanced-glass-card" shadow="never">
              <template #header>
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="icon-badge bg-gradient-to-r from-blue-500 to-indigo-500">
                      <Icon icon="mdi:chart-line" class="text-xl text-white" />
                    </div>
                    <div>
                      <h3 class="text-lg font-semibold text-gray-800">用户增长趋势</h3>
                      <p class="text-sm text-gray-500">实时监控用户增长情况</p>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <el-select
                      v-model="userTrendDays"
                      @change="refreshUserTrend"
                      size="small"
                      class="enhanced-select"
                    >
                      <el-option label="7天" :value="7" />
                      <el-option label="30天" :value="30" />
                      <el-option label="90天" :value="90" />
                    </el-select>
                    <el-button
                      text
                      @click="refreshUserTrend"
                      :loading="loading"
                      class="refresh-btn"
                    >
                      <Icon icon="mdi:refresh" class="text-lg" />
                    </el-button>
                  </div>
                </div>
              </template>
              <div class="h-80 relative">
                <UserTrendChart :data="userTrendData" :loading="loading" />
                <div class="chart-overlay"></div>
              </div>
            </el-card>
          </div>

          <!-- 反馈类型分布 -->
          <div class="chart-wrapper">
            <el-card class="chart-card enhanced-glass-card" shadow="never">
              <template #header>
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="icon-badge bg-gradient-to-r from-purple-500 to-pink-500">
                      <Icon icon="mdi:chart-pie" class="text-xl text-white" />
                    </div>
                    <div>
                      <h3 class="text-lg font-semibold text-gray-800">反馈类型分布</h3>
                      <p class="text-sm text-gray-500">用户反馈分类统计</p>
                    </div>
                  </div>
                  <el-button
                    text
                    @click="refreshFeedbackStats"
                    :loading="loading"
                    class="refresh-btn"
                  >
                    <Icon icon="mdi:refresh" class="text-lg" />
                  </el-button>
                </div>
              </template>
              <div class="h-80 relative">
                <FeedbackStatsChart :data="feedbackStatsData" :loading="loading" />
                <div class="chart-overlay"></div>
              </div>
            </el-card>
          </div>
        </div>
      </div>

      <!-- 内容统计图表 -->
      <div
        v-motion
        :initial="{ opacity: 0, y: 50 }"
        :enter="{ opacity: 1, y: 0, transition: { duration: 800, delay: 600 } }"
        class="mb-8"
      >
        <el-card class="chart-card glass-card" shadow="never">
          <template #header>
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <Icon icon="mdi:chart-bar" class="text-xl text-orange-500" />
                <h3 class="text-lg font-semibold text-gray-800">内容上传趋势</h3>
              </div>
              <div class="flex items-center space-x-2">
                <el-select
                  v-model="contentStatsMonths"
                  @change="refreshContentStats"
                  size="small"
                  style="width: 100px"
                >
                  <el-option label="6个月" :value="6" />
                  <el-option label="12个月" :value="12" />
                  <el-option label="24个月" :value="24" />
                </el-select>
                <el-button text @click="refreshContentStats" :loading="loading">
                  <Icon icon="mdi:refresh" class="text-lg" />
                </el-button>
              </div>
            </div>
          </template>
          <div class="h-96">
            <ContentStatsChart :data="contentStatsData" :loading="loading" />
          </div>
        </el-card>
      </div>

      <!-- 快捷操作和最新动态 -->
      <div
        v-motion
        :initial="{ opacity: 0, y: 50 }"
        :enter="{ opacity: 1, y: 0, transition: { duration: 800, delay: 800 } }"
        class="bottom-section"
      >
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- 快捷操作 -->
          <el-card class="quick-actions enhanced-glass-card" shadow="never">
            <template #header>
              <div class="flex items-center space-x-3">
                <div class="icon-badge bg-gradient-to-r from-yellow-500 to-orange-500">
                  <Icon icon="mdi:lightning-bolt" class="text-xl text-white" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">快捷操作</h3>
                  <p class="text-sm text-gray-500">常用功能快速入口</p>
                </div>
              </div>
            </template>
            <div class="space-y-4">
              <el-button
                type="primary"
                class="w-full justify-start enhanced-action-btn"
                @click="$router.push('/singer')"
              >
                <Icon icon="mdi:account-plus" class="mr-3 text-lg" />
                <span class="flex-1 text-left">添加歌手</span>
                <Icon icon="mdi:arrow-right" class="text-sm opacity-60" />
              </el-button>
              <el-button
                type="success"
                class="w-full justify-start enhanced-action-btn"
                @click="$router.push('/song')"
              >
                <Icon icon="mdi:upload" class="mr-3 text-lg" />
                <span class="flex-1 text-left">上传歌曲</span>
                <Icon icon="mdi:arrow-right" class="text-sm opacity-60" />
              </el-button>
              <el-button
                type="warning"
                class="w-full justify-start enhanced-action-btn"
                @click="$router.push('/playlist')"
              >
                <Icon icon="mdi:playlist-plus" class="mr-3 text-lg" />
                <span class="flex-1 text-left">创建歌单</span>
                <Icon icon="mdi:arrow-right" class="text-sm opacity-60" />
              </el-button>
              <el-button
                type="info"
                class="w-full justify-start enhanced-action-btn"
                @click="$router.push('/feedback')"
              >
                <Icon icon="mdi:message-processing" class="mr-3 text-lg" />
                <span class="flex-1 text-left">处理反馈</span>
                <Icon icon="mdi:arrow-right" class="text-sm opacity-60" />
              </el-button>
            </div>
          </el-card>

          <!-- 系统状态 -->
          <el-card class="system-status enhanced-glass-card" shadow="never">
            <template #header>
              <div class="flex items-center space-x-3">
                <div class="icon-badge bg-gradient-to-r from-green-500 to-emerald-500">
                  <Icon icon="mdi:server" class="text-xl text-white" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">系统状态</h3>
                  <p class="text-sm text-gray-500">实时系统监控</p>
                </div>
              </div>
            </template>
            <div class="space-y-4">
              <div class="status-item">
                <div class="flex items-center space-x-3">
                  <div class="status-indicator status-success">
                    <Icon icon="mdi:check-circle" class="text-white text-sm" />
                  </div>
                  <span class="text-gray-700 font-medium">系统运行状态</span>
                </div>
                <el-tag type="success" effect="light" class="enhanced-tag">正常</el-tag>
              </div>
              <div class="status-item">
                <div class="flex items-center space-x-3">
                  <div class="status-indicator status-warning">
                    <Icon icon="mdi:alert-circle" class="text-white text-sm" />
                  </div>
                  <span class="text-gray-700 font-medium">待处理反馈</span>
                </div>
                <el-tag type="warning" effect="light" class="enhanced-tag">{{
                  overview?.systemStats.pendingFeedbacks || 0
                }}</el-tag>
              </div>
              <div class="status-item">
                <div class="flex items-center space-x-3">
                  <div class="status-indicator status-info">
                    <Icon icon="mdi:account-plus" class="text-white text-sm" />
                  </div>
                  <span class="text-gray-700 font-medium">今日新增用户</span>
                </div>
                <el-tag type="info" effect="light" class="enhanced-tag">{{
                  overview?.userStats.todayNewUsers || 0
                }}</el-tag>
              </div>
              <div class="status-item">
                <div class="flex items-center space-x-3">
                  <div class="status-indicator status-danger">
                    <Icon icon="mdi:heart" class="text-white text-sm" />
                  </div>
                  <span class="text-gray-700 font-medium">总点赞数</span>
                </div>
                <el-tag type="danger" effect="light" class="enhanced-tag">{{
                  formatNumber(overview?.interactionStats.totalLikes || 0)
                }}</el-tag>
              </div>
            </div>
          </el-card>

          <!-- 最新动态 -->
          <el-card class="recent-activities enhanced-glass-card" shadow="never">
            <template #header>
              <div class="flex items-center space-x-3">
                <div class="icon-badge bg-gradient-to-r from-indigo-500 to-purple-500">
                  <Icon icon="mdi:timeline" class="text-xl text-white" />
                </div>
                <div>
                  <h3 class="text-lg font-semibold text-gray-800">最新动态</h3>
                  <p class="text-sm text-gray-500">平台实时活动</p>
                </div>
              </div>
            </template>
            <div class="space-y-4">
              <div class="activity-item">
                <div class="activity-indicator bg-gradient-to-r from-blue-500 to-cyan-500">
                  <Icon icon="mdi:account-plus" class="text-white text-xs" />
                </div>
                <div class="activity-content">
                  <p class="text-sm text-gray-800 font-medium">新用户 "音乐爱好者123" 注册成功</p>
                  <p class="text-xs text-gray-500 flex items-center">
                    <Icon icon="mdi:clock-outline" class="mr-1" />
                    5分钟前
                  </p>
                </div>
              </div>
              <div class="activity-item">
                <div class="activity-indicator bg-gradient-to-r from-green-500 to-emerald-500">
                  <Icon icon="mdi:music-note" class="text-white text-xs" />
                </div>
                <div class="activity-content">
                  <p class="text-sm text-gray-800 font-medium">歌手 "周杰伦" 上传了新歌曲</p>
                  <p class="text-xs text-gray-500 flex items-center">
                    <Icon icon="mdi:clock-outline" class="mr-1" />
                    15分钟前
                  </p>
                </div>
              </div>
              <div class="activity-item">
                <div class="activity-indicator bg-gradient-to-r from-orange-500 to-red-500">
                  <Icon icon="mdi:message-alert" class="text-white text-xs" />
                </div>
                <div class="activity-content">
                  <p class="text-sm text-gray-800 font-medium">收到新的用户反馈</p>
                  <p class="text-xs text-gray-500 flex items-center">
                    <Icon icon="mdi:clock-outline" class="mr-1" />
                    30分钟前
                  </p>
                </div>
              </div>
              <div class="activity-item">
                <div class="activity-indicator bg-gradient-to-r from-purple-500 to-pink-500">
                  <Icon icon="mdi:heart" class="text-white text-xs" />
                </div>
                <div class="activity-content">
                  <p class="text-sm text-gray-800 font-medium">歌单 "热门金曲" 被收藏100次</p>
                  <p class="text-xs text-gray-500 flex items-center">
                    <Icon icon="mdi:clock-outline" class="mr-1" />
                    1小时前
                  </p>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useDashboardStore } from '@/stores/dashboard'
import { Icon } from '@iconify/vue'
defineOptions({
  name: 'DashboardView',
})

// Store
const dashboardStore = useDashboardStore()
const { loading, overview, userTrendData, feedbackStatsData, contentStatsData } =
  storeToRefs(dashboardStore)

// 图表选项
const userTrendDays = ref(30)
const contentStatsMonths = ref(12)

// 当前日期
const currentDate = computed(() => {
  return new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
  })
})

// 格式化数字
const formatNumber = (num: number): string => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toString()
}

// 刷新方法
const refreshUserTrend = () => {
  dashboardStore.getUserTrendData(userTrendDays.value)
}

const refreshFeedbackStats = () => {
  dashboardStore.getFeedbackStatsData()
}

const refreshContentStats = () => {
  dashboardStore.getContentStatsData(contentStatsMonths.value)
}

// 初始化
onMounted(() => {
  dashboardStore.initDashboard()
})
</script>

<style scoped>
/* 🎵 Dashboard 美化样式 */
.dashboard-container {
  animation: fadeIn 0.8s ease-out;
  position: relative;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 浮动音符装饰 */
.floating-notes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.note {
  position: absolute;
  font-size: 2rem;
  color: rgba(102, 126, 234, 0.1);
  animation: floatNote 8s ease-in-out infinite;
}

.note-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}
.note-2 {
  top: 20%;
  right: 15%;
  animation-delay: 1s;
}
.note-3 {
  top: 60%;
  left: 5%;
  animation-delay: 2s;
}
.note-4 {
  top: 80%;
  right: 10%;
  animation-delay: 3s;
}
.note-5 {
  top: 40%;
  left: 80%;
  animation-delay: 4s;
}
.note-6 {
  top: 70%;
  right: 70%;
  animation-delay: 5s;
}

@keyframes floatNote {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.1;
  }
  25% {
    transform: translateY(-20px) rotate(5deg);
    opacity: 0.2;
  }
  50% {
    transform: translateY(-10px) rotate(-3deg);
    opacity: 0.15;
  }
  75% {
    transform: translateY(-30px) rotate(8deg);
    opacity: 0.25;
  }
}

/* 欢迎横幅样式 */
.welcome-banner {
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-text-white {
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 50%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 指标卡片装饰 */
.metric-card-wrapper {
  position: relative;
}

.metric-decoration {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  border-radius: 1rem;
  opacity: 0.1;
  transition: opacity 0.3s ease;
}

.metric-card-wrapper:hover .metric-decoration {
  opacity: 0.2;
}

.metric-decoration-1 {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.metric-decoration-2 {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.metric-decoration-3 {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.metric-decoration-4 {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

/* 图表卡片增强 */
.chart-wrapper {
  position: relative;
}

.enhanced-glass-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.5rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.enhanced-glass-card:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.95);
}

.icon-badge {
  width: 3rem;
  height: 3rem;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.icon-badge:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.enhanced-select {
  border-radius: 0.75rem;
}

.refresh-btn {
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  transform: rotate(180deg);
  background: rgba(102, 126, 234, 0.1);
}

.chart-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    transparent 0%,
    rgba(102, 126, 234, 0.02) 50%,
    transparent 100%
  );
  pointer-events: none;
  border-radius: 0.75rem;
}

/* 浮动动画 */
.float-animation {
  animation: float 4s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

/* 快捷操作和底部区域美化 */
.glass-card {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1.25rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.12),
    0 6px 20px rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.9);
}

.glass-button {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.glass-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.glass-button:hover::before {
  left: 100%;
}

.glass-button:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

/* 快捷操作按钮增强 */
.enhanced-action-btn {
  border-radius: 1rem;
  padding: 1rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.enhanced-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.enhanced-action-btn:hover::before {
  left: 100%;
}

.enhanced-action-btn:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* 系统状态项样式 */
.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  border-radius: 0.75rem;
  background: rgba(248, 250, 252, 0.5);
  transition: all 0.3s ease;
  border: 1px solid rgba(226, 232, 240, 0.5);
}

.status-item:hover {
  background: rgba(248, 250, 252, 0.8);
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.status-indicator {
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.status-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.status-info {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.status-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.enhanced-tag {
  border-radius: 0.5rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border: none;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 活动项样式 */
.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: 0.75rem;
  background: rgba(248, 250, 252, 0.5);
  transition: all 0.3s ease;
  border: 1px solid rgba(226, 232, 240, 0.5);
  position: relative;
}

.activity-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 0.375rem 0.375rem 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.activity-item:hover::before {
  opacity: 1;
}

.activity-item:hover {
  background: rgba(248, 250, 252, 0.8);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.activity-indicator {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
  flex-shrink: 0;
  margin-right: 0.75rem;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-content p:first-child {
  margin-bottom: 0.25rem;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .floating-notes {
    display: none;
  }

  .welcome-banner {
    padding: 1.5rem;
  }

  .note {
    font-size: 1.5rem;
  }

  .enhanced-action-btn {
    padding: 0.75rem 1rem;
  }

  .activity-item {
    padding: 0.75rem;
  }

  .status-item {
    padding: 0.5rem;
  }
}

@media (max-width: 1024px) {
  .enhanced-glass-card:hover {
    transform: translateY(-2px);
  }

  .enhanced-action-btn:hover {
    transform: translateY(-1px);
  }
}

/* 加载动画 */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}
</style>
