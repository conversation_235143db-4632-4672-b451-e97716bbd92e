// 歌单信息接口
export interface PlaylistInfo {
  id: number
  name: string
  description?: string
  style?: string // 改为单个字符串
  coverUrl?: string
  songCount?: number
  createTime?: string
  updateTime?: string
}

// 歌单查询参数
export interface PlaylistQueryParams {
  pageNum: number
  pageSize: number
  name?: string
  style?: string
  beginTime?: string
  endTime?: string
}

// 歌单风格枚举 - 参考主流音乐平台
export const PlaylistStyles = [
  // 流行音乐
  '抖音热门',
  '华语流行',
  '欧美流行',
  '日韩流行',
  '粤语流行',

  // 摇滚金属
  '摇滚',
  '金属',
  '朋克',
  '独立摇滚',

  // 电子音乐
  '电子',
  'EDM',
  '说唱',
  'Hip-Hop',

  // 民族风格
  '民谣',
  '国风',
  '古典',
  '爵士',

  // 情感分类
  '治愈',
  '励志',
  '伤感',

  // 场景分类
  '运动',
  '学习',
  '睡眠',
  '开车',

  // 其他
  '纯音乐',
  '影视原声',
  '游戏音乐',
  '其他'
] as const

export type PlaylistStyle = typeof PlaylistStyles[number]


