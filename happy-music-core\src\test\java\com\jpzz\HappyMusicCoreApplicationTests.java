package com.jpzz;

import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

@SpringBootTest
class HappyMusicCoreApplicationTests {

	@Resource
	private JdbcTemplate jdbcTemplate;
	@Test
	void contextLoads() {
		System.out.println(jdbcTemplate.getDataSource().getClass());
	}

}
