package com.jpzz.pojo.dto.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 反馈查询DTO
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "反馈查询DTO")
public class FeedbackQueryDTO extends PageQueryDTO {

    @Schema(description = "反馈内容关键词", example = "登录")
    private String content;

    @Schema(description = "反馈类型", example = "BUG", allowableValues = {"BUG", "FEATURE", "COMPLAINT", "SUGGESTION", "OTHER"})
    private String type;

    @Schema(description = "反馈状态", example = "PENDING", allowableValues = {"PENDING", "PROCESSING", "RESOLVED", "CLOSED"})
    private String status;

    @Schema(description = "用户ID", example = "1")
    private Long userId;
}
