package com.jpzz.utils;


import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.jpzz.properties.JwtProperties;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
public class JwtUtils {
    @Resource
    private JwtProperties jwtProperties;

    /**
     * 生成JWT令牌
     *
     * @param userId   用户ID
     * @param username 用户名
     * @param role     用户角色
     * @return JWT令牌
     */
    public  String generateToken(Long userId, String username, String role) {

        Date now = new Date();
        Date expirationDate = new Date(now.getTime() + jwtProperties.getExpiration());
        return JWT.create()
                .withSubject(String.valueOf(userId))
                .withClaim("username", username)
                .withClaim("role", role)
                .withIssuedAt(now)
                .withExpiresAt(expirationDate)
                .sign(Algorithm.HMAC256(jwtProperties.getSecretKey()));
    }
    /**
     * 验证令牌
     *
     * @param token JWT令牌
     * @return 解码后的JWT
     * @throws JWTVerificationException 如果令牌无效或已过期
     */
    public DecodedJWT verifyToken(String token) {
        JWTVerifier verifier = JWT.require(Algorithm.HMAC256((jwtProperties.getSecretKey()))).build();
        return verifier.verify(token);
    }
    /**
     * 从令牌中获取用户ID
     *
     * @param token JWT令牌
     * @return 用户ID
     */
    public Long getUserIdByToken(String token){
        DecodedJWT jwt =verifyToken(token);
        return Long.valueOf(jwt.getSubject());
    }
    /**
     * 从令牌中获取用户名
     *
     * @param token JWT令牌
     * @return 用户名
     */
    public String getUserNameByToken(String token){
        DecodedJWT jwt =verifyToken(token);
        return jwt.getClaim("username").asString();
    }
    /**
     * 从令牌中获取用户角色
     *
     * @param token JWT令牌
     * @return 用户角色
     */
    public String getRoleByToken(String token){
        DecodedJWT jwt =verifyToken(token);
        return jwt.getClaim("role").asString();
    }

    /**
     * 验证令牌是否有效
     *
     * @param token JWT令牌
     * @return 是否有效
     */
    public boolean validateToken(String token){
        try{
            verifyToken(token);
            return true;
        }catch (JWTVerificationException e){
            return false;
        }

    }
}
