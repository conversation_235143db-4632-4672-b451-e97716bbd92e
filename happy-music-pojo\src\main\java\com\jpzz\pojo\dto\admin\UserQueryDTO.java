package com.jpzz.pojo.dto.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户查询参数DTO
 * 用于接收前端传入的查询条件
 * <AUTHOR>
 */
@Data
@Schema(description = "用户查询参数")
public class UserQueryDTO extends PageQueryDTO{



    @Schema(description = "用户名(模糊查询)", example = "admin")
    private String username;


    @Schema(description = "手机号码", example = "13800000000")
    private String phone;


    @Schema(description = "状态(0-禁用,1-启用)", example = "1")
    private String status;


    @Schema(description = "角色(ADMIN-管理员,USER-普通用户)", example = "ADMIN")
    private String role;


    @Schema(description = "开始时间", example = "2023-01-01 00:00:00")
    private String beginTime;


    @Schema(description = "结束时间", example = "2023-12-31 23:59:59")
    private String endTime;
}