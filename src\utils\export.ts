import * as XLSX from 'xlsx'

interface ExportExcelOptions {
  /** Excel的表头 */
  header: string[]
  /** Excel的数据 */
  data: (string | number | boolean | null)[][]
  /** 文件名称 */
  fileName?: string
  /** 是否自动宽度 */
  autoWidth?: boolean
  /** 文件类型 */
  bookType?: 'xlsx' | 'csv' | 'txt'
}

/**
 * 导出Excel的方法
 * @param options 配置选项
 */
export function exportExcel(options: ExportExcelOptions) {
  const {
    header = [],
    data = [],
    fileName = '导出数据',
    autoWidth = true,
    bookType = 'xlsx'
  } = options

  // 创建工作簿
  const wb = XLSX.utils.book_new()
  // 创建工作表数据
  const ws = XLSX.utils.aoa_to_sheet([header, ...data])

  // 设置自动列宽
  if (autoWidth) {
    const colWidth = data.map(row =>
      row.map(val => {
        if (val == null) return { wch: 10 }
        const strVal = String(val)
        return { wch: Math.max(10, strVal.length * 2) }
      })
    )
    // 以第一行为初始值
    const result = colWidth[0]
    // 合并所有行的列宽
    for (let i = 1; i < colWidth.length; i++) {
      for (let j = 0; j < colWidth[i].length; j++) {
        if (result[j].wch < colWidth[i][j].wch) {
          result[j].wch = colWidth[i][j].wch
        }
      }
    }
    ws['!cols'] = result
  }

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

  // 导出文件
  XLSX.writeFile(wb, `${fileName}.${bookType}`)
}
