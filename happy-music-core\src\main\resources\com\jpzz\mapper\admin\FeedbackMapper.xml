<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jpzz.mapper.admin.FeedbackMapper">

    <!-- 反馈VO结果映射 -->
    <resultMap id="FeedbackVOMap" type="com.jpzz.pojo.vo.admin.FeedbackVO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="avatar" property="userAvatar"/>
        <result column="content" property="content"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="admin_reply" property="adminReply"/>
        <result column="reply_time" property="replyTime"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 分页查询反馈列表 -->
    <select id="selectFeedbackPage" resultMap="FeedbackVOMap">
        SELECT
            f.id,
            f.user_id,
            u.username,
            u.avatar,
            f.content,
            f.type,
            f.status,
            f.admin_reply,
            f.reply_time,
            f.create_time
        FROM tb_feedback f
        LEFT JOIN tb_user u ON f.user_id = u.user_id
        <where>
            f.deleted = 0
            <if test="query.content != null and query.content != ''">
                AND f.content LIKE CONCAT('%', #{query.content}, '%')
            </if>
            <if test="query.type != null and query.type != ''">
                AND f.type = #{query.type}
            </if>
            <if test="query.status != null and query.status != ''">
                AND f.status = #{query.status}
            </if>
            <if test="query.userId != null">
                AND f.user_id = #{query.userId}
            </if>
        </where>
        ORDER BY f.create_time DESC
    </select>

    <!-- 根据ID查询反馈详情 -->
    <select id="selectFeedbackById" resultMap="FeedbackVOMap">
        SELECT
            f.id,
            f.user_id,
            u.username,
            u.avatar,
            f.content,
            f.type,
            f.status,
            f.admin_reply,
            f.reply_time,
            f.create_time
        FROM tb_feedback f
        LEFT JOIN tb_user u ON f.user_id = u.user_id
        WHERE f.id = #{feedbackId} AND f.deleted = 0
    </select>

</mapper>