import { defineStore } from 'pinia'
import type { SongInfo, SongQueryParams } from '@/types/song'
import {
    getSongList,
    addSong as apiAddSong,
    updateSong as apiUpdateSong,
    deleteSong as apiDeleteSong,
    batchDeleteSongs,
    uploadSongCover as apiUploadSongCover,
    uploadSongAudio as apiUploadSongAudio,
} from '@/api/song'
import { ElMessage } from 'element-plus'

export const useSongStore = defineStore('song', () => {
    // 状态定义
    const songList = ref<SongInfo[]>([])
    const currentSong = ref<SongInfo | null>(null)
    const total = ref(0)
    const loading = ref(false)

    // 查询参数
    const queryParams = reactive<SongQueryParams>({
        name: '',
        artistId: undefined,
        artistName: '',
        style: '',
        album: '',
        pageNum: 1,
        pageSize: 10
    })

    // 获取歌曲列表
    const getList = async () => {
        try {
            loading.value = true
            const res = await getSongList(queryParams)
            songList.value = res.list
            total.value = res.total
        } catch (error) {
            console.error('获取歌曲列表失败', error)
            ElMessage.error('获取歌曲列表失败')
        } finally {
            loading.value = false
        }
    }

    // 重置查询条件
    const resetQuery = () => {
        queryParams.name = ''
        queryParams.artistId = undefined
        queryParams.artistName = ''
        queryParams.style = ''
        queryParams.album = ''
        queryParams.pageNum = 1
        getList()
    }

    // 添加歌曲
    const addSong = async (data: Partial<SongInfo>) => {
        try {
            await apiAddSong(data)
            ElMessage.success('添加歌曲成功')
            getList()
            return true
        } catch (error) {
            console.error('添加歌曲失败', error)
            ElMessage.error('添加歌曲失败')
            return false
        }
    }

    // 更新歌曲
    const updateSong = async (data: Partial<SongInfo>) => {
        try {
            await apiUpdateSong(data)
            ElMessage.success('更新歌曲成功')
            getList()
            return true
        } catch (error) {
            console.error('更新歌曲失败', error)
            ElMessage.error('更新歌曲失败')
            return false
        }
    }

    // 删除歌曲
    const deleteSong = async (songId: number) => {
        try {
            await apiDeleteSong(songId)
            ElMessage.success('删除歌曲成功')
            getList()
            return true
        } catch (error) {
            console.error('删除歌曲失败', error)
            ElMessage.error('删除歌曲失败')
            return false
        }
    }

    // 批量删除歌曲
    const batchDelete = async (songIds: number[]) => {
        try {
            await batchDeleteSongs(songIds)
            ElMessage.success(`成功删除 ${songIds.length} 首歌曲`)
            getList()
            return true
        } catch (error) {
            console.error('批量删除歌曲失败', error)
            ElMessage.error('批量删除歌曲失败')
            return false
        }
    }

    // 上传封面
    const uploadCover = async (songId: number, file: File) => {
        try {
            const res = await apiUploadSongCover(songId, file)
            ElMessage.success('上传封面成功')
            getList()
            return res.url
        } catch (error) {
            console.error('上传封面失败', error)
            ElMessage.error('上传封面失败')
            return null
        }
    }

    // 上传音频
    const uploadAudio = async (songId: number, file: File) => {
        try {
            const res = await apiUploadSongAudio(songId, file)
            ElMessage.success('上传音频成功')
            getList()
            return res.url
        } catch (error) {
            console.error('上传音频失败', error)
            ElMessage.error('上传音频失败')
            return null
        }
    }

    return {
        // 状态
        songList,
        currentSong,
        total,
        loading,
        queryParams,
        
        // 方法
        getList,
        resetQuery,
        addSong,
        updateSong,
        deleteSong,
        batchDelete,
        uploadCover,
        uploadAudio,
    }
})