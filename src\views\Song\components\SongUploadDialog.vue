<template>
  <el-dialog
    :title="uploadType === 'cover' ? '上传歌曲封面' : '上传歌曲音频'"
    :model-value="modelValue"
    @close="handleClose"
    width="500px"
    class="song-upload-dialog"
  >
    <div class="text-center">
      <!-- 当前封面/音频预览 -->
      <div class="flex flex-col items-center mb-4" v-if="uploadType === 'cover'">
        <div class="text-sm text-gray-600 mb-2">当前封面：</div>
        <el-image
          :src="currentCover || defaultCover"
          class="w-20 h-20 rounded"
          fit="cover"
        >
          <template #error>
            <div class="w-20 h-20 bg-gray-100 flex items-center justify-center rounded">
              <el-icon class="text-gray-400"><Picture /></el-icon>
            </div>
          </template>
        </el-image>
      </div>

      <!-- 上传区域 -->
      <el-upload
        ref="uploadRef"
        class="uploader w-full"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handleFileChange"
        :accept="uploadType === 'cover' ? 'image/*' : 'audio/*'"
        drag
      >
        <div class="flex flex-col items-center justify-center h-full">
          <el-icon class="text-3xl text-gray-400 mb-4" v-if="!uploading">
            <component :is="uploadType === 'cover' ? Picture : Upload" />
          </el-icon>
          <div v-if="uploading" class="flex flex-col items-center">
            <el-progress type="circle" :percentage="uploadProgress" :width="60" />
            <div class="text-sm mt-2">上传中...</div>
          </div>
          <div v-else class="text-gray-600">
            <div class="text-base mb-1">
              点击或拖拽上传{{ uploadType === 'cover' ? '封面' : '音频文件' }}
            </div>
            <div class="text-sm text-gray-500">
              {{ uploadType === 'cover'
                ? '支持 JPG、PNG 格式，文件大小不超过 5MB'
                : '支持 MP3、WAV、FLAC 格式，文件大小不超过 50MB'
              }}
            </div>
          </div>
        </div>
      </el-upload>

      <!-- 预览新文件 -->
      <div v-if="previewUrl && uploadType === 'cover'" class="flex flex-col items-center mt-4">
        <div class="text-sm text-gray-600 mb-2">预览新封面：</div>
        <el-image :src="previewUrl" class="w-20 h-20 rounded" fit="cover" />
      </div>

      <div v-if="selectedFile && uploadType === 'audio'" class="mt-4">
        <div class="text-sm text-gray-600 mb-2">选择的音频文件：</div>
        <div class="text-sm">{{ selectedFile.name }}</div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3 pt-4 border-t border-gray-200">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="!selectedFile || uploading"
          :loading="uploading"
        >
          确定上传
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { Picture, Upload } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import { useSongStore } from '@/stores/song'

defineOptions({
  name: 'SongUploadDialog',
})

const props = defineProps<{
  modelValue: boolean
  songId?: number
  uploadType: 'cover' | 'audio'
}>()

const emit = defineEmits(['update:modelValue', 'success'])

// Store
const songStore = useSongStore()

// 上传相关
const uploading = ref(false)
const uploadProgress = ref(0)
const previewUrl = ref('')
const currentCover = ref('')
const selectedFile = ref<File | null>(null)

// 默认封面
const defaultCover = '/favicon.ico'

// 监听歌曲ID变化，获取当前封面
watch(
  () => props.songId,
  async (newId) => {
    if (newId && props.uploadType === 'cover') {
      const song = songStore.songList.find((s) => s.id === newId)
      if (song) {
        currentCover.value = song.coverUrl || ''
      }
    }
  },
  { immediate: true },
)

// 文件选择处理
const handleFileChange = (file: UploadFile) => {
  const rawFile = file.raw
  if (!rawFile) return

  if (props.uploadType === 'cover') {
    const isImage = rawFile.type.startsWith('image/')
    const isLt5M = rawFile.size / 1024 / 1024 < 5

    if (!isImage) {
      ElMessage.error('只能上传图片文件!')
      return
    }
    if (!isLt5M) {
      ElMessage.error('图片大小不能超过 5MB!')
      return
    }

    // 创建预览URL
    if (previewUrl.value) {
      URL.revokeObjectURL(previewUrl.value)
    }
    previewUrl.value = URL.createObjectURL(rawFile)
  } else {
    const isAudio = rawFile.type.startsWith('audio/')
    const isLt50M = rawFile.size / 1024 / 1024 < 50

    if (!isAudio) {
      ElMessage.error('只能上传音频文件!')
      return
    }
    if (!isLt50M) {
      ElMessage.error('音频文件大小不能超过 50MB!')
      return
    }
  }

  selectedFile.value = rawFile
}

// 确认上传
const handleConfirm = async () => {
  if (!selectedFile.value || !props.songId) {
    ElMessage.warning('请先选择要上传的文件')
    return
  }

  try {
    uploading.value = true
    uploadProgress.value = 0

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += 10
      }
    }, 100)

    let result
    if (props.uploadType === 'cover') {
      result = await songStore.uploadCover(props.songId, selectedFile.value)
    } else {
      result = await songStore.uploadAudio(props.songId, selectedFile.value)
    }

    clearInterval(progressInterval)
    uploadProgress.value = 100

    if (result) {
      emit('success')
      handleClose()
    }
  } catch (error) {
    console.error('上传失败:', error)
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

// 关闭对话框
const handleClose = () => {
  emit('update:modelValue', false)
  // 清理预览URL
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
    previewUrl.value = ''
  }
  selectedFile.value = null
  uploading.value = false
  uploadProgress.value = 0
}
</script>

<style scoped>
.song-upload-dialog :deep(.el-dialog__body) {
  @apply p-6;
}

.uploader :deep(.el-upload) {
  @apply w-full;
}

.uploader :deep(.el-upload-dragger) {
  @apply w-full h-44 flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg cursor-pointer relative overflow-hidden transition-all duration-300;
}

.uploader :deep(.el-upload-dragger:hover) {
  @apply border-blue-500;
}
</style>
