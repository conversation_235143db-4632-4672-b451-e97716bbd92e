package com.jpzz.pojo.dto.admin;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;

/**
 * 歌单操作DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "歌单操作DTO")
public class PlaylistDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "歌单ID")
    private Long id;

    @NotBlank(message = "歌单名称不能为空")
    @Size(max = 100, message = "歌单名称长度不能超过100个字符")
    @Schema(description = "歌单名称")
    private String name;

    @Size(max = 500, message = "歌单描述长度不能超过500个字符")
    @Schema(description = "歌单描述")
    private String description;

    @Schema(description = "歌单风格")
    private String style;

    @Schema(description = "封面URL")
    private String coverUrl;
}