<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jpzz.mapper.admin.PlaylistSongMapper">

    <!-- 歌曲VO结果映射 -->
    <resultMap id="SongVOMap" type="com.jpzz.pojo.vo.admin.SongVO">
        <id property="id" column="id"/>
        <result property="artistId" column="artist_id"/>
        <result property="artistName" column="artist_name"/>
        <result property="name" column="name"/>
        <result property="album" column="album"/>
        <result property="lyric" column="lyric"/>
        <result property="duration" column="duration"/>
        <result property="style" column="style"/>
        <result property="coverUrl" column="cover_url"/>
        <result property="audioUrl" column="audio_url"/>
        <result property="releaseTime" column="release_time"/>
        <result property="likeCount" column="like_count"/>
        <result property="sortOrder" column="sort_order"/>
    </resultMap>

    <!-- 分页查询歌单中的歌曲列表 -->
    <select id="selectPlaylistSongs" resultMap="SongVOMap">
        SELECT
            s.id,
            s.artist_id,
            singer.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count,
            ps.sort_order
        FROM tb_playlist_song ps
        LEFT JOIN tb_song s ON ps.song_id = s.id
        LEFT JOIN tb_singer singer ON s.artist_id = singer.singer_id
        <where>
            AND ps.playlist_id = #{query.playlistId}
            <if test="query.name != null and query.name != ''">
                AND s.name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.artistName != null and query.artistName != ''">
                AND singer.singer_name LIKE CONCAT('%', #{query.artistName}, '%')
            </if>
            <if test="query.style != null and query.style != ''">
                AND s.style LIKE CONCAT('%', #{query.style}, '%')
            </if>
        </where>
        ORDER BY ps.sort_order , ps.create_time
    </select>

</mapper>
