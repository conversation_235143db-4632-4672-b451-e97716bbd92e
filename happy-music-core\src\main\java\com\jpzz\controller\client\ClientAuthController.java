package com.jpzz.controller.client;

import com.jpzz.pojo.dto.client.LoginRequestDTO;
import com.jpzz.pojo.dto.client.RegisterRequestDTO;
import com.jpzz.pojo.vo.client.ClientUserVO;
import com.jpzz.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 客户端认证控制器
 * 临时实现，用于测试
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/client/auth")
@Tag(name = "客户端认证接口", description = "客户端登录、登出等认证功能")
@Slf4j
public class ClientAuthController {

    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户登录接口")
    public Result<Map<String, Object>> login(@RequestBody LoginRequestDTO loginRequest) {
        log.info("用户登录请求，邮箱：{}", loginRequest.getEmail());

        // 临时实现：简单验证
        if (loginRequest.getEmail() != null && loginRequest.getPassword() != null && loginRequest.getPassword().length() >= 6) {
            Map<String, Object> result = new HashMap<>();
            result.put("token", "temp_token_" + System.currentTimeMillis());

            // 创建用户信息
            ClientUserVO user = new ClientUserVO();
            user.setUserId(1L);
            user.setEmail(loginRequest.getEmail());
            user.setNickname("测试用户");
            user.setAvatar("https://via.placeholder.com/100");
            user.setMemberLevel("VIP");
            user.setJoinDate(LocalDateTime.now().minusMonths(6));
            user.setIsVip(true);
            user.setVipExpireTime(LocalDateTime.now().plusYears(1));
            user.setStatus("ACTIVE");

            result.put("user", user);

            log.info("用户登录成功，邮箱：{}", loginRequest.getEmail());
            return Result.success("登录成功", result);
        } else {
            log.warn("用户登录失败，邮箱：{}，密码长度：{}", loginRequest.getEmail(),
                    loginRequest.getPassword() != null ? loginRequest.getPassword().length() : 0);
            return Result.error("邮箱或密码错误");
        }
    }

    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出接口")
    public Result<Void> logout(@RequestHeader(value = "Authorization", required = false) String token) {
        log.info("用户登出请求，token：{}", token);
        // 临时实现：直接返回成功
        return Result.success("登出成功");
    }

    @GetMapping("/profile")
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的信息")
    public Result<ClientUserVO> getProfile(@RequestHeader(value = "Authorization", required = false) String token) {
        log.info("获取用户信息请求，token：{}", token);

        // 临时实现：返回固定用户信息
        ClientUserVO profile = new ClientUserVO();
        profile.setUserId(1L);
        profile.setEmail("<EMAIL>");
        profile.setNickname("测试用户");
        profile.setAvatar("https://via.placeholder.com/100");
        profile.setMemberLevel("VIP");
        profile.setJoinDate(LocalDateTime.now().minusMonths(6));
        profile.setIsVip(true);
        profile.setVipExpireTime(LocalDateTime.now().plusYears(1));
        profile.setStatus("ACTIVE");

        return Result.success("获取成功", profile);
    }

    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "用户注册接口")
    public Result<Map<String, Object>> register(@RequestBody RegisterRequestDTO registerRequest) {
        log.info("用户注册请求，邮箱：{}，昵称：{}", registerRequest.getEmail(), registerRequest.getNickname());

        // 临时实现：简单验证
        if (registerRequest.getEmail() != null && registerRequest.getPassword() != null &&
                registerRequest.getPassword().length() >= 6 &&
                registerRequest.getPassword().equals(registerRequest.getConfirmPassword())) {

            Map<String, Object> result = new HashMap<>();
            result.put("token", "temp_token_" + System.currentTimeMillis());

            // 创建用户信息
            ClientUserVO user = new ClientUserVO();
            user.setUserId(System.currentTimeMillis() % 10000);
            user.setEmail(registerRequest.getEmail());
            user.setNickname(registerRequest.getNickname() != null ? registerRequest.getNickname() : "新用户");
            user.setAvatar("https://via.placeholder.com/100");
            user.setMemberLevel("NORMAL");
            user.setJoinDate(LocalDateTime.now());
            user.setIsVip(false);
            user.setStatus("ACTIVE");

            result.put("user", user);

            log.info("用户注册成功，邮箱：{}", registerRequest.getEmail());
            return Result.success("注册成功", result);
        } else {
            log.warn("用户注册失败，邮箱：{}，密码长度：{}", registerRequest.getEmail(),
                    registerRequest.getPassword() != null ? registerRequest.getPassword().length() : 0);
            return Result.error("注册信息不完整、密码过短或两次密码不一致");
        }
    }

    @PostMapping("/refresh-token")
    @Operation(summary = "刷新令牌", description = "刷新访问令牌")
    public Result<Map<String, Object>> refreshToken(@RequestHeader(value = "Authorization", required = false) String token) {
        log.info("刷新令牌请求，token：{}", token);

        // 临时实现：返回新token
        Map<String, Object> result = new HashMap<>();
        result.put("token", "temp_token_" + System.currentTimeMillis());
        result.put("expiresIn", 3600); // 1小时

        return Result.success("刷新成功", result);
    }

    @GetMapping("/check")
    @Operation(summary = "检查登录状态", description = "检查用户是否已登录")
    public Result<Map<String, Object>> checkLoginStatus(@RequestHeader(value = "Authorization", required = false) String token) {
        log.info("检查登录状态，token：{}", token);

        // 临时实现：如果有token就认为已登录
        Map<String, Object> result = new HashMap<>();
        if (token != null && !token.isEmpty()) {
            result.put("isLoggedIn", true);
            result.put("userId", 1L);
            result.put("email", "<EMAIL>");
        } else {
            result.put("isLoggedIn", false);
        }

        return Result.success("检查完成", result);
    }
}
