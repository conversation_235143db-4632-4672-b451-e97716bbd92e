<template>
  <div>
    <el-dialog
      :title="dialogProps.title"
      :model-value="dialogProps.visible"
      @close="handleClose"
      width="650px"
      class="song-dialog"
    >


      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="right"
        class="px-4"
      >
      <el-form-item label="歌手ID" prop="artistId">
          <el-input
            disabled
            v-model="form.artistId"
            :min="1"
            placeholder="请输入歌手ID"
            class="w-full"
          />
        </el-form-item>
        <el-form-item v-if="dialogProps.type === 'edit'" label="歌曲编号">
          <el-input v-model="form.id" disabled class="w-full" />
        </el-form-item>

        <el-form-item label="歌曲名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入歌曲名称" class="w-full" clearable />
        </el-form-item>



        <el-form-item label="专辑名称" prop="album">
          <el-input v-model="form.album" placeholder="请输入专辑名称" class="w-full" clearable />
        </el-form-item>

        <el-form-item label="音乐风格" prop="style">
          <el-select
            v-model="form.style"
            placeholder="请选择音乐风格"
            class="w-full"
            clearable
            filterable
          >
            <el-option v-for="style in SongStyles" :key="style" :label="style" :value="style" />
          </el-select>
        </el-form-item>

        <el-form-item label="发行时间" prop="releaseTime">
          <el-date-picker
            v-model="form.releaseTime"
            type="date"
            placeholder="请选择发行时间"
            value-format="YYYY-MM-DD"
            class="w-full"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="歌曲时长" prop="duration">
              <el-input v-model="form.duration" placeholder="如：3:45" class="w-full" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="点赞数">
              <el-input-number
                v-model="form.likeCount"
                :min="0"
                placeholder="点赞数量"
                class="w-full"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="歌词" prop="lyric">
          <el-input
            v-model="form.lyric"
            type="textarea"
            :rows="4"
            placeholder="请输入歌词内容"
            maxlength="2000"
            show-word-limit
            class="w-full"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-end gap-3 pt-4 border-t border-gray-200">
          <el-button @click="handleClose" class="px-6">取消</el-button>
          <el-button type="primary" @click="handleSubmit" class="px-6">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'SongDialog',
})

import { SongStyles } from '@/types/song'
import type { SongInfo } from '@/types/song'
import { type FormInstance, type FormRules } from 'element-plus'

const props = defineProps<{
  dialogProps: {
    title: string
    visible: boolean
    type: 'add' | 'edit'
    data?: Partial<SongInfo>
  }
}>()
const emit = defineEmits(['update:visible', 'success'])
const formRef = ref<FormInstance>()

const form = reactive<Partial<SongInfo>>({
  id: undefined,
  name: '',
  artistId: undefined,
  album: '',
  style: '',
  releaseTime: '',
  duration: '',
  lyric: '',
  likeCount: 0,
})

const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入歌曲名称', trigger: 'blur' },
    { min: 1, max: 100, message: '歌曲名称长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  artistId: [
    { required: true, message: '请输入歌手ID', trigger: 'blur' },
    { type: 'number', message: '歌手ID必须是数字', trigger: 'blur' },
  ],
  album: [
    { required: true, message: '请输入专辑名称', trigger: 'blur' },
    { min: 1, max: 100, message: '专辑名称长度在 1 到 100 个字符', trigger: 'blur' },
  ],
  releaseTime: [{ required: true, message: '请选择发行时间', trigger: 'change' }],
  duration: [
    {
      pattern: /^(\d{1,2}):([0-5]\d)$/,
      message: '请输入正确的时长格式，如：3:45',
      trigger: 'blur',
    },
  ],
  lyric: [{ max: 2000, message: '歌词最多2000个字符', trigger: 'blur' }],
})

watch(
  () => props.dialogProps.visible,
  (val) => {
    if (val) {
      if (props.dialogProps.type === 'edit' && props.dialogProps.data) {
        Object.assign(form, props.dialogProps.data)
      } else {
        form.id = undefined
        form.name = ''
        form.artistId = props.dialogProps.data?.artistId
        form.album = ''
        form.style = ''
        form.releaseTime = ''
        form.duration = ''
        form.lyric = ''
        form.likeCount = 0
      }
    }
  },
)

const handleClose = () => {
  emit('update:visible', false)
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  await formRef.value.validate(async (valid) => {
    if (valid) {
      emit('success', form, props.dialogProps.type)
      handleClose()
    }
  })
}
</script>

<style scoped>
.song-dialog :deep(.el-dialog__body) {
  padding: 20px 24px;
}

.song-dialog :deep(.el-dialog__footer) {
  padding: 0 24px 20px;
}

.song-dialog :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.song-dialog :deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.2s ease;
}

.song-dialog :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.song-dialog :deep(.el-textarea__inner) {
  border-radius: 6px;
  transition: all 0.2s ease;
}

.song-dialog :deep(.el-textarea__inner:hover) {
  border-color: #c0c4cc;
}

.song-dialog :deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}
</style>
